name: shared
description: <PERSON><PERSON> shared package with common models, utilities, and constants for client-server communication
version: 2.0.0
# repository: https://github.com/my_org/my_repo

environment:
  sdk: ^3.8.1

# Add regular dependencies here.
dependencies:
  # JSON serialization
  json_annotation: ^4.9.0

  # HTTP and networking
  http: ^1.2.1
  web_socket_channel: ^2.4.5

  # Utilities
  meta: ^1.15.0
  equatable: ^2.0.5
  crypto: ^3.0.6

  # UUID generation
  uuid: ^4.4.0

dev_dependencies:
  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.8.0

  # Code quality
  lints: ^5.0.0
  test: ^1.25.8
