# Development environment configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:

  mongodb:
    ports:
      - "27017:27017"  # Expose MongoDB for development tools
    volumes:
      - ./settings/data/mongodb:/data/db  # Use local directory for easier access
  # Expose Mongo Express for development
  mongo-express:
    ports:
      - "8081:8081"  # MongoDB Admin interface
    environment:
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: dev123

  redis:
    ports:
      - "6379:6379"  # Expose Redis for development tools

  # Expose Redis Commander for development
  redis-commander:
    ports:
      - "8082:8081"  # Redis Admin interface
    environment:
      HTTP_USER: admin
      HTTP_PASSWORD: dev123

  server:
    build:
      target: development
    environment:
      NODE_ENV: development
      DEBUG: "true"
      LOG_LEVEL: debug
    ports:
      - "8080:8080"  # Expose server port for direct access in development
    volumes:
      - ./server:/app/server  # Mount server source code for hot reload
      - ./shared:/app/shared  # Mount shared package for hot reload
      - server_pub_cache:/app/server/.pub-cache  # Preserve pub cache
    command: ["sh", "-c", "dart pub get && dart run bin/server.dart --observe"]

  client:
    build:
      target: development
    environment:
      FLUTTER_WEB_PORT: 8000
      FLUTTER_WEB_HOSTNAME: 0.0.0.0
      API_BASE_URL: http://localhost:8080/api
      WS_URL: ws://localhost:8080/ws
    ports:
      - "8000:8000"  # Expose client port for direct access
    volumes:
      - ./client:/app/client  # Mount client source code for hot reload
      - ./shared:/app/shared  # Mount shared package for hot reload
      - /app/client/.pub-cache  # Preserve pub cache
    command: ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "8000"]

  nginx:
    ports:
      - "3000:80"  # Use different port to avoid conflicts
    volumes:
      - ./settings/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro  # Use development nginx config

volumes:
  server_pub_cache:
