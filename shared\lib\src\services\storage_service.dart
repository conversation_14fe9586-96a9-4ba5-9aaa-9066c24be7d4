import 'dart:async';
import 'dart:typed_data';

/// Storage service interface for persistent data storage
abstract class StorageService {
  /// Initialize storage service
  Future<void> initialize();
  
  /// Check if storage is available
  Future<bool> isAvailable();
  
  /// Get storage size in bytes
  Future<int> getStorageSize();
  
  /// Clear all storage
  Future<void> clearAll();
  
  /// String storage methods
  Future<void> setString(String key, String value);
  Future<String?> getString(String key);
  Future<void> removeString(String key);
  
  /// Integer storage methods
  Future<void> setInt(String key, int value);
  Future<int?> getInt(String key);
  Future<void> removeInt(String key);
  
  /// Double storage methods
  Future<void> setDouble(String key, double value);
  Future<double?> getDouble(String key);
  Future<void> removeDouble(String key);
  
  /// Boolean storage methods
  Future<void> setBool(String key, bool value);
  Future<bool?> getBool(String key);
  Future<void> removeBool(String key);
  
  /// List storage methods
  Future<void> setStringList(String key, List<String> value);
  Future<List<String>?> getStringList(String key);
  Future<void> removeStringList(String key);
  
  /// Binary data storage methods
  Future<void> setBytes(String key, Uint8List value);
  Future<Uint8List?> getBytes(String key);
  Future<void> removeBytes(String key);
  
  /// JSON storage methods
  Future<void> setJson(String key, Map<String, dynamic> value);
  Future<Map<String, dynamic>?> getJson(String key);
  Future<void> removeJson(String key);
  
  /// Check if key exists
  Future<bool> containsKey(String key);
  
  /// Get all keys
  Future<Set<String>> getKeys();
  
  /// Remove key
  Future<void> remove(String key);
  
  /// Batch operations
  Future<void> setBatch(Map<String, dynamic> data);
  Future<Map<String, dynamic>> getBatch(List<String> keys);
  Future<void> removeBatch(List<String> keys);
}

/// Secure storage service interface for sensitive data
abstract class SecureStorageService {
  /// Initialize secure storage
  Future<void> initialize();
  
  /// Check if secure storage is available
  Future<bool> isAvailable();
  
  /// Store secure string
  Future<void> setSecureString(String key, String value);
  
  /// Get secure string
  Future<String?> getSecureString(String key);
  
  /// Remove secure string
  Future<void> removeSecureString(String key);
  
  /// Store secure JSON
  Future<void> setSecureJson(String key, Map<String, dynamic> value);
  
  /// Get secure JSON
  Future<Map<String, dynamic>?> getSecureJson(String key);
  
  /// Remove secure JSON
  Future<void> removeSecureJson(String key);
  
  /// Check if secure key exists
  Future<bool> containsSecureKey(String key);
  
  /// Get all secure keys
  Future<Set<String>> getSecureKeys();
  
  /// Clear all secure storage
  Future<void> clearSecureStorage();
}

/// Cache service interface for temporary data storage
abstract class CacheService {
  /// Initialize cache service
  Future<void> initialize();
  
  /// Set cache value with expiration
  Future<void> set(String key, dynamic value, {Duration? expiration});
  
  /// Get cache value
  Future<T?> get<T>(String key);
  
  /// Remove cache value
  Future<void> remove(String key);
  
  /// Check if cache key exists and is not expired
  Future<bool> exists(String key);
  
  /// Clear all cache
  Future<void> clear();
  
  /// Clear expired cache entries
  Future<void> clearExpired();
  
  /// Get cache size
  Future<int> size();
  
  /// Get cache statistics
  Future<Map<String, dynamic>> getStats();
  
  /// Set cache with tags for group operations
  Future<void> setWithTags(String key, dynamic value, List<String> tags, {Duration? expiration});
  
  /// Remove all cache entries with specific tag
  Future<void> removeByTag(String tag);
  
  /// Get all cache keys
  Future<List<String>> getKeys();
  
  /// Get cache expiration time
  Future<DateTime?> getExpiration(String key);
}

/// File storage service interface for file operations
abstract class FileStorageService {
  /// Initialize file storage
  Future<void> initialize();
  
  /// Check if file storage is available
  Future<bool> isAvailable();
  
  /// Write file
  Future<void> writeFile(String path, Uint8List data);
  
  /// Read file
  Future<Uint8List?> readFile(String path);
  
  /// Write text file
  Future<void> writeTextFile(String path, String content);
  
  /// Read text file
  Future<String?> readTextFile(String path);
  
  /// Write JSON file
  Future<void> writeJsonFile(String path, Map<String, dynamic> data);
  
  /// Read JSON file
  Future<Map<String, dynamic>?> readJsonFile(String path);
  
  /// Check if file exists
  Future<bool> fileExists(String path);
  
  /// Delete file
  Future<void> deleteFile(String path);
  
  /// Get file size
  Future<int> getFileSize(String path);
  
  /// Get file modification time
  Future<DateTime> getFileModificationTime(String path);
  
  /// List files in directory
  Future<List<String>> listFiles(String directory);
  
  /// Create directory
  Future<void> createDirectory(String path);
  
  /// Delete directory
  Future<void> deleteDirectory(String path);
  
  /// Copy file
  Future<void> copyFile(String sourcePath, String destinationPath);
  
  /// Move file
  Future<void> moveFile(String sourcePath, String destinationPath);
  
  /// Get available storage space
  Future<int> getAvailableSpace();
  
  /// Get total storage space
  Future<int> getTotalSpace();
}

/// Authentication-specific storage service
abstract class AuthStorageService {
  /// Save authentication token
  Future<void> saveAuthToken(String token);
  
  /// Get authentication token
  Future<String?> getAuthToken();
  
  /// Save refresh token
  Future<void> saveRefreshToken(String token);
  
  /// Get refresh token
  Future<String?> getRefreshToken();
  
  /// Save user data
  Future<void> saveUserData(Map<String, dynamic> userData);
  
  /// Get user data
  Future<Map<String, dynamic>?> getUserData();
  
  /// Clear authentication data
  Future<void> clearAuthData();
  
  /// Save user preferences
  Future<void> saveUserPreferences(Map<String, dynamic> preferences);
  
  /// Get user preferences
  Future<Map<String, dynamic>?> getUserPreferences();
  
  /// Save app settings
  Future<void> saveAppSettings(Map<String, dynamic> settings);
  
  /// Get app settings
  Future<Map<String, dynamic>?> getAppSettings();
}
