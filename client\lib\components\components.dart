/// Quester Components Library
/// 
/// This file exports all reusable UI components for the Quester application.
/// Components are designed to be responsive, accessible, and consistent with
/// the app's design system.
library;

// Layout Components
export 'quester_app_bar.dart';
export 'responsive_navigation.dart';
export 'responsive_builder.dart';
export 'account_sidebar.dart';

// UI Components
export 'quester_button.dart';
export 'quester_card.dart';
export 'quester_input.dart';
export 'notification_badge.dart';

// State Components
export 'loading_overlay.dart';
export 'empty_state.dart';

// Component Documentation
/// 
/// ## Layout Components
/// 
/// ### QuesterAppBar
/// Responsive app bar with logo, search, notifications, and account icons.
/// Automatically adapts styling for mobile, tablet, and desktop.
/// 
/// ```dart
/// QuesterAppBar(
///   onAccountTap: () => _toggleAccountSidebar(),
/// )
/// ```
/// 
/// ### ResponsiveNavigation
/// Adaptive navigation that shows as bottom bar on mobile and side rail on larger screens.
/// 
/// ```dart
/// ResponsiveNavigation(
///   isExpanded: isDesktop,
///   isVertical: !isMobile,
/// )
/// ```
/// 
/// ### ResponsiveBuilder
/// Utility widget for building responsive layouts based on screen size.
/// 
/// ```dart
/// ResponsiveBuilder(
///   mobile: MobileLayout(),
///   tablet: TabletLayout(),
///   desktop: DesktopLayout(),
/// )
/// ```
/// 
/// ### AccountSidebar
/// Right sidebar showing user account information and settings.
/// Updates in real-time using WebSocket connections.
/// 
/// ```dart
/// AccountSidebar(
///   onClose: () => setState(() => _sidebarOpen = false),
/// )
/// ```
/// 
/// ## UI Components
/// 
/// ### QuesterButton
/// Comprehensive button component with multiple variants and sizes.
/// 
/// ```dart
/// // Primary button
/// QuesterButton.primary(
///   text: 'Complete Quest',
///   onPressed: () => _completeQuest(),
///   icon: Icons.check,
/// )
/// 
/// // Secondary button
/// QuesterButton.secondary(
///   text: 'Cancel',
///   onPressed: () => _cancel(),
/// )
/// 
/// // Outline button
/// QuesterButton.outline(
///   text: 'Learn More',
///   onPressed: () => _showDetails(),
/// )
/// ```
/// 
/// ### QuesterCard
/// Flexible card component with multiple presets for different use cases.
/// 
/// ```dart
/// // Standard card
/// QuesterCard.standard(
///   child: Column(
///     children: [
///       QuesterCardHeader(title: 'Quest Progress'),
///       ProgressIndicator(value: 0.7),
///       QuesterCardFooter(actions: [
///         QuesterButton.text(text: 'View Details'),
///       ]),
///     ],
///   ),
/// )
/// 
/// // Quest-specific card
/// QuesterCard.quest(
///   onTap: () => _openQuest(quest),
///   child: QuestContent(quest: quest),
/// )
/// 
/// // Achievement card with gradient
/// QuesterCard.achievement(
///   child: AchievementContent(achievement: achievement),
/// )
/// ```
/// 
/// ### QuesterInput
/// Comprehensive input field component with validation and multiple types.
/// 
/// ```dart
/// // Email input
/// QuesterInput.email(
///   controller: _emailController,
///   validator: (value) => _validateEmail(value),
/// )
/// 
/// // Password input with visibility toggle
/// QuesterInput.password(
///   controller: _passwordController,
///   validator: (value) => _validatePassword(value),
/// )
/// 
/// // Search input with clear button
/// QuesterInput.search(
///   hint: 'Search quests...',
///   onChanged: (query) => _searchQuests(query),
/// )
/// 
/// // Multiline text input
/// QuesterInput.multiline(
///   label: 'Description',
///   maxLines: 4,
///   maxLength: 500,
/// )
/// ```
/// 
/// ### NotificationBadge
/// Badge component for showing notification counts on icons.
/// 
/// ```dart
/// NotificationBadge(
///   count: unreadCount,
///   child: Icon(Icons.notifications),
/// )
/// ```
/// 
/// ## State Components
/// 
/// ### LoadingOverlay
/// Full-screen loading overlay with spinner and optional message.
/// 
/// ```dart
/// LoadingOverlay(
///   isLoading: _isLoading,
///   message: 'Saving quest...',
///   child: QuestForm(),
/// )
/// ```
/// 
/// ### EmptyState
/// Component for showing empty states with icon, message, and optional action.
/// 
/// ```dart
/// EmptyState(
///   icon: Icons.search_off,
///   title: 'No quests found',
///   message: 'Try adjusting your search criteria',
///   action: QuesterButton.primary(
///     text: 'Clear Filters',
///     onPressed: () => _clearFilters(),
///   ),
/// )
/// ```
/// 
/// ## Design System
/// 
/// All components follow the Quester design system:
/// 
/// - **Colors**: Primary blue (#2563EB), grey app bar (#F5F5F5)
/// - **Typography**: Inter font family with consistent sizing
/// - **Spacing**: 8px grid system using ScreenUtil for responsiveness
/// - **Elevation**: Consistent shadow system for depth
/// - **Border Radius**: 8-16px for modern, friendly appearance
/// - **Responsive**: Automatic adaptation for mobile, tablet, desktop
/// 
/// ## Accessibility
/// 
/// Components include:
/// - Semantic labels and tooltips
/// - Keyboard navigation support
/// - Screen reader compatibility
/// - High contrast color support
/// - Touch target sizing (44px minimum)
/// 
/// ## Performance
/// 
/// Components are optimized for:
/// - Minimal rebuilds using const constructors
/// - Efficient state management
/// - Lazy loading where appropriate
/// - Memory-efficient image handling
/// - Smooth animations and transitions
/// 
/// ## Customization
/// 
/// Most components accept customization parameters:
/// - Custom colors and styling
/// - Flexible padding and margins
/// - Override default behaviors
/// - Theme integration
/// 
/// Example of customization:
/// 
/// ```dart
/// QuesterButton(
///   text: 'Custom Button',
///   customColor: Colors.purple,
///   customTextColor: Colors.white,
///   borderRadius: BorderRadius.circular(20),
///   onPressed: () => _customAction(),
/// )
/// ```
