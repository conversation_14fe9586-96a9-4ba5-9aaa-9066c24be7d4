# Client-specific .dockerignore
# Optimizes Flutter client Docker build context

# Docker files
.dockerignore
Dockerfile*

# Version control
.git/
.github/
.gitignore

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Build artifacts
build/
.dart_tool/
.packages
.pub-cache/

# Flutter specific
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# Documentation
*.md
!README.md

# Test files (can be excluded in production)
test/
*_test.dart

# Coverage reports
coverage/

# Logs
*.log
logs/

# Environment files
.env*

# OS files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Flutter ephemeral files and symlinks
**/flutter/ephemeral/
**/.plugin_symlinks/
**/Generated.xcconfig
**/flutter_export_environment.sh

# Platform-specific build outputs (keep source)
android/app/build/
ios/build/
linux/build/
macos/build/
windows/build/
web/build/

# Flutter generated files
.flutter-plugins
.flutter-plugins-dependencies
