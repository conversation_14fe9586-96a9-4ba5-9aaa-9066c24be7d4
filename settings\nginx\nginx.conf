events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
      upstream backend {
        server server:8080;
    }    upstream frontend {
        server client:8000;
    }

    server {
        listen 80;
        server_name localhost;        # API routes
        location /api/ {
            proxy_pass http://backend/api/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Important for request body forwarding
            proxy_pass_request_body on;
            proxy_pass_request_headers on;
            
            # Disable buffering for real-time data
            proxy_buffering off;
            proxy_request_buffering off;
            proxy_cache off;
            
            # Increase timeouts and body size limits
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            client_max_body_size 50m;
            client_body_buffer_size 128k;
            
            # Preserve Content-Length
            proxy_set_header Content-Length $content_length;
        }

        # WebSocket route
        location /ws {
            proxy_pass http://backend/ws;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }        # Frontend routes
        location / {
            proxy_pass http://frontend/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }        # API health check endpoint (proxy to backend)
        location /api/health {
            proxy_pass http://backend/api/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
