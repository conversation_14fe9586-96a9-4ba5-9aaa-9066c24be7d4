import 'dart:async';
import '../models/models.dart';

/// Authentication states
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Authentication result wrapper
class AuthResult {
  final bool success;
  final String? message;
  final User? user;
  final String? token;
  final String? refreshToken;
  final Map<String, dynamic>? errors;

  const AuthResult({
    required this.success,
    this.message,
    this.user,
    this.token,
    this.refreshToken,
    this.errors,
  });

  /// Success result factory
  factory AuthResult.success({
    String? message,
    User? user,
    String? token,
    String? refreshToken,
  }) {
    return AuthResult(
      success: true,
      message: message,
      user: user,
      token: token,
      refreshToken: refreshToken,
    );
  }

  /// Failure result factory
  factory AuthResult.failure({
    required String message,
    Map<String, dynamic>? errors,
  }) {
    return AuthResult(
      success: false,
      message: message,
      errors: errors,
    );
  }
}

/// Abstract authentication service interface
abstract class AuthService {
  /// Current authentication state stream
  Stream<AuthState> get authStateStream;
  
  /// Current user stream
  Stream<User?> get userStream;
  
  /// Current authentication state
  AuthState get currentState;
  
  /// Current authenticated user
  User? get currentUser;
  
  /// Check if user is authenticated
  bool get isAuthenticated;
  
  /// Initialize authentication service
  Future<void> initialize();
  
  /// Register a new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String username,
    String? firstName,
    String? lastName,
    String? displayName,
  });
  
  /// Login with email and password
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  });
  
  /// Login with username and password
  Future<AuthResult> loginWithUsername({
    required String username,
    required String password,
    bool rememberMe = false,
  });
  
  /// Logout current user
  Future<void> logout();
  
  /// Refresh authentication token
  Future<bool> refreshToken();
  
  /// Verify current authentication status
  Future<bool> verifyAuth();
  
  /// Get current user profile
  Future<User?> getCurrentUser();
  
  /// Update user profile
  Future<AuthResult> updateProfile(Map<String, dynamic> updates);
  
  /// Change password
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  });
  
  /// Request password reset
  Future<AuthResult> requestPasswordReset(String email);
  
  /// Reset password with token
  Future<AuthResult> resetPassword({
    required String token,
    required String newPassword,
  });
  
  /// Verify email address
  Future<AuthResult> verifyEmail(String token);
  
  /// Resend email verification
  Future<AuthResult> resendEmailVerification();
  
  /// Delete user account
  Future<AuthResult> deleteAccount(String password);
  
  /// Clear all authentication data
  Future<void> clearAuthData();
  
  /// Dispose resources
  void dispose();
}

/// Token management interface
abstract class TokenManager {
  /// Get access token
  Future<String?> getAccessToken();
  
  /// Get refresh token
  Future<String?> getRefreshToken();
  
  /// Save tokens
  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
  });
  
  /// Clear tokens
  Future<void> clearTokens();
  
  /// Check if access token is expired
  bool isAccessTokenExpired(String token);
  
  /// Check if refresh token is expired
  bool isRefreshTokenExpired(String token);
  
  /// Parse JWT token
  Map<String, dynamic>? parseJwtToken(String token);
  
  /// Get token expiration date
  DateTime? getTokenExpirationDate(String token);
}

/// Session management interface
abstract class SessionManager {
  /// Start new session
  Future<void> startSession(User user, String token);
  
  /// End current session
  Future<void> endSession();
  
  /// Get current session
  Future<Map<String, dynamic>?> getCurrentSession();
  
  /// Check if session is valid
  Future<bool> isSessionValid();
  
  /// Extend session
  Future<void> extendSession();
  
  /// Get session duration
  Duration getSessionDuration();
  
  /// Set session timeout
  void setSessionTimeout(Duration timeout);
}

/// Biometric authentication interface
abstract class BiometricAuthService {
  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable();
  
  /// Check if biometric authentication is enabled
  Future<bool> isBiometricEnabled();
  
  /// Enable biometric authentication
  Future<bool> enableBiometric();
  
  /// Disable biometric authentication
  Future<void> disableBiometric();
  
  /// Authenticate with biometric
  Future<bool> authenticateWithBiometric();
  
  /// Get available biometric types
  Future<List<String>> getAvailableBiometricTypes();
}

/// Social authentication interface
abstract class SocialAuthService {
  /// Login with Google
  Future<AuthResult> loginWithGoogle();
  
  /// Login with Apple
  Future<AuthResult> loginWithApple();
  
  /// Login with Facebook
  Future<AuthResult> loginWithFacebook();
  
  /// Login with Twitter
  Future<AuthResult> loginWithTwitter();
  
  /// Link social account
  Future<AuthResult> linkSocialAccount(String provider, String token);
  
  /// Unlink social account
  Future<AuthResult> unlinkSocialAccount(String provider);
  
  /// Get linked social accounts
  Future<List<String>> getLinkedSocialAccounts();
}
