# Quester Platform Environment Configuration
# Copy this file to .env and update the values

# =====================================
# Database Configuration
# =====================================
MONGO_USERNAME=admin
MONGO_PASSWORD=secure_mongo_password_here
MONGO_DATABASE=quester

# MongoDB Admin Interface (Mongo Express)
MONGO_ADMIN_USERNAME=admin
MONGO_ADMIN_PASSWORD=secure_admin_password_here

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_here

# Redis Admin Interface (Redis Commander)
REDIS_ADMIN_USERNAME=admin
REDIS_ADMIN_PASSWORD=secure_redis_admin_password_here

# =====================================
# JWT Configuration
# =====================================
JWT_SECRET=your-super-secret-jwt-key-make-it-long-and-random
JWT_REFRESH_SECRET=your-refresh-secret-different-from-jwt-secret

# =====================================
# Application Configuration
# =====================================
NODE_ENV=development
LOG_LEVEL=debug
PORT=8080

# =====================================
# Domain Configuration (for production)
# =====================================
DOMAIN=localhost
SSL_EMAIL=<EMAIL>

# =====================================
# Development Settings
# =====================================
# Set to true to enable development features
DEV_MODE=true
DEBUG=true

# =====================================
# Security Settings
# =====================================
# Session timeout in minutes
SESSION_TIMEOUT=30

# Maximum concurrent sessions per user
MAX_CONCURRENT_SESSIONS=3

# Rate limiting (requests per window)
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

# =====================================
# External Services (Optional)
# =====================================
# Email Service (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Push Notifications
FCM_SERVER_KEY=your-firebase-server-key
APNS_KEY_ID=your-apple-key-id
APNS_TEAM_ID=your-apple-team-id

# File Upload Configuration
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# Analytics (Optional)
GOOGLE_ANALYTICS_ID=GA-XXXXX-X
ENABLE_ANALYTICS=false
