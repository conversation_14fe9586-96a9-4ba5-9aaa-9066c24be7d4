/// Quest-related exception classes
class QuestException implements Exception {
  final String message;
  final String? code;
  final String? questId;
  final String? userId;
  final Map<String, dynamic>? details;

  const QuestException({
    required this.message,
    this.code,
    this.questId,
    this.userId,
    this.details,
  });

  @override
  String toString() {
    final buffer = StringBuffer('QuestException: $message');
    if (code != null) buffer.write(' (Code: $code)');
    if (questId != null) buffer.write(' (Quest: $questId)');
    return buffer.toString();
  }

  /// Quest not found
  factory QuestException.notFound({
    required String questId,
    String message = 'Quest not found',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_NOT_FOUND',
      questId: questId,
    );
  }

  /// Quest already completed
  factory QuestException.alreadyCompleted({
    required String questId,
    String? userId,
    String message = 'Quest has already been completed',
    DateTime? completedAt,
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_ALREADY_COMPLETED',
      questId: questId,
      userId: userId,
      details: completedAt != null ? {'completedAt': completedAt.toIso8601String()} : null,
    );
  }

  /// Quest not assigned
  factory QuestException.notAssigned({
    required String questId,
    required String userId,
    String message = 'Quest is not assigned to this user',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_NOT_ASSIGNED',
      questId: questId,
      userId: userId,
    );
  }

  /// Quest already assigned
  factory QuestException.alreadyAssigned({
    required String questId,
    required String userId,
    String message = 'Quest is already assigned to this user',
    DateTime? assignedAt,
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_ALREADY_ASSIGNED',
      questId: questId,
      userId: userId,
      details: assignedAt != null ? {'assignedAt': assignedAt.toIso8601String()} : null,
    );
  }

  /// Quest expired
  factory QuestException.expired({
    required String questId,
    String? userId,
    String message = 'Quest has expired',
    DateTime? expiredAt,
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_EXPIRED',
      questId: questId,
      userId: userId,
      details: expiredAt != null ? {'expiredAt': expiredAt.toIso8601String()} : null,
    );
  }

  /// Quest not active
  factory QuestException.notActive({
    required String questId,
    String message = 'Quest is not active',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_NOT_ACTIVE',
      questId: questId,
    );
  }

  /// Quest requirements not met
  factory QuestException.requirementsNotMet({
    required String questId,
    required String userId,
    required List<String> missingRequirements,
    String message = 'Quest requirements not met',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_REQUIREMENTS_NOT_MET',
      questId: questId,
      userId: userId,
      details: {'missingRequirements': missingRequirements},
    );
  }

  /// Quest creation failed
  factory QuestException.creationFailed({
    required String reason,
    String message = 'Failed to create quest',
    Map<String, dynamic>? validationErrors,
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_CREATION_FAILED',
      details: {
        'reason': reason,
        if (validationErrors != null) 'validationErrors': validationErrors,
      },
    );
  }

  /// Quest update failed
  factory QuestException.updateFailed({
    required String questId,
    required String reason,
    String message = 'Failed to update quest',
    Map<String, dynamic>? validationErrors,
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_UPDATE_FAILED',
      questId: questId,
      details: {
        'reason': reason,
        if (validationErrors != null) 'validationErrors': validationErrors,
      },
    );
  }

  /// Quest deletion failed
  factory QuestException.deletionFailed({
    required String questId,
    required String reason,
    String message = 'Failed to delete quest',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_DELETION_FAILED',
      questId: questId,
      details: {'reason': reason},
    );
  }

  /// Quest completion failed
  factory QuestException.completionFailed({
    required String questId,
    required String userId,
    required String reason,
    String message = 'Failed to complete quest',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_COMPLETION_FAILED',
      questId: questId,
      userId: userId,
      details: {'reason': reason},
    );
  }

  /// Quest assignment failed
  factory QuestException.assignmentFailed({
    required String questId,
    required String userId,
    required String reason,
    String message = 'Failed to assign quest',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_ASSIGNMENT_FAILED',
      questId: questId,
      userId: userId,
      details: {'reason': reason},
    );
  }

  /// Quest unassignment failed
  factory QuestException.unassignmentFailed({
    required String questId,
    required String userId,
    required String reason,
    String message = 'Failed to unassign quest',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_UNASSIGNMENT_FAILED',
      questId: questId,
      userId: userId,
      details: {'reason': reason},
    );
  }

  /// Quest limit exceeded
  factory QuestException.limitExceeded({
    required String userId,
    required int currentCount,
    required int maxAllowed,
    String message = 'Quest limit exceeded',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_LIMIT_EXCEEDED',
      userId: userId,
      details: {
        'currentCount': currentCount,
        'maxAllowed': maxAllowed,
      },
    );
  }

  /// Quest dependency not met
  factory QuestException.dependencyNotMet({
    required String questId,
    required String userId,
    required List<String> dependencyQuestIds,
    String message = 'Quest dependencies not completed',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_DEPENDENCY_NOT_MET',
      questId: questId,
      userId: userId,
      details: {'dependencyQuestIds': dependencyQuestIds},
    );
  }

  /// Quest level requirement not met
  factory QuestException.levelRequirementNotMet({
    required String questId,
    required String userId,
    required int requiredLevel,
    required int userLevel,
    String message = 'User level requirement not met',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_LEVEL_REQUIREMENT_NOT_MET',
      questId: questId,
      userId: userId,
      details: {
        'requiredLevel': requiredLevel,
        'userLevel': userLevel,
      },
    );
  }

  /// Quest cooldown active
  factory QuestException.cooldownActive({
    required String questId,
    required String userId,
    required DateTime cooldownEnds,
    String message = 'Quest is on cooldown',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_COOLDOWN_ACTIVE',
      questId: questId,
      userId: userId,
      details: {'cooldownEnds': cooldownEnds.toIso8601String()},
    );
  }

  /// Quest data invalid
  factory QuestException.invalidData({
    required String field,
    required String reason,
    String? questId,
    String message = 'Quest data is invalid',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_INVALID_DATA',
      questId: questId,
      details: {
        'field': field,
        'reason': reason,
      },
    );
  }

  /// Quest permission denied
  factory QuestException.permissionDenied({
    required String questId,
    required String userId,
    required String action,
    String message = 'Permission denied for quest action',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_PERMISSION_DENIED',
      questId: questId,
      userId: userId,
      details: {'action': action},
    );
  }

  /// Quest state invalid
  factory QuestException.invalidState({
    required String questId,
    required String currentState,
    required String expectedState,
    String message = 'Quest is in invalid state for this operation',
  }) {
    return QuestException(
      message: message,
      code: 'QUEST_INVALID_STATE',
      questId: questId,
      details: {
        'currentState': currentState,
        'expectedState': expectedState,
      },
    );
  }

  /// Check if this is a not found error
  bool get isNotFound => code == 'QUEST_NOT_FOUND';

  /// Check if this is a completion error
  bool get isCompletionError => code?.contains('COMPLETION') == true;

  /// Check if this is an assignment error
  bool get isAssignmentError => code?.contains('ASSIGNMENT') == true;

  /// Check if this is a permission error
  bool get isPermissionError => code == 'QUEST_PERMISSION_DENIED';

  /// Check if this is a validation error
  bool get isValidationError => code?.contains('INVALID') == true || code?.contains('REQUIREMENTS') == true;

  /// Check if this is a state error
  bool get isStateError => code?.contains('STATE') == true || code?.contains('ACTIVE') == true;

  /// Get missing requirements
  List<String>? get missingRequirements => details?['missingRequirements'] as List<String>?;

  /// Get validation errors
  Map<String, dynamic>? get validationErrors => details?['validationErrors'] as Map<String, dynamic>?;

  /// Get cooldown end time
  DateTime? get cooldownEnds {
    final timeString = details?['cooldownEnds'] as String?;
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Get completion time
  DateTime? get completedAt {
    final timeString = details?['completedAt'] as String?;
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Get assignment time
  DateTime? get assignedAt {
    final timeString = details?['assignedAt'] as String?;
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Get expiration time
  DateTime? get expiredAt {
    final timeString = details?['expiredAt'] as String?;
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'code': code,
      'questId': questId,
      'userId': userId,
      'details': details,
    };
  }

  /// Create from map
  factory QuestException.fromMap(Map<String, dynamic> map) {
    return QuestException(
      message: map['message'] as String,
      code: map['code'] as String?,
      questId: map['questId'] as String?,
      userId: map['userId'] as String?,
      details: map['details'] as Map<String, dynamic>?,
    );
  }
}
