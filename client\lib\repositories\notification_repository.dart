import 'dart:core';
import 'package:shared/shared.dart' hide ApiService;

import '../services/api_service.dart';

/// Repository for notification-related operations
class NotificationRepository {
  final ApiService _apiService;

  NotificationRepository({
    required ApiService apiService,
  }) : _apiService = apiService;

  /// Get all notifications
  Future<ApiResponse<List<Map<String, dynamic>>>> getNotifications({
    bool? isRead,
    String? type,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (isRead != null) queryParams['isRead'] = isRead.toString();
    if (type != null) queryParams['type'] = type;

    return await _apiService.get('/notifications', queryParameters: queryParams);
  }

  /// Get notification by ID
  Future<ApiResponse<Map<String, dynamic>>> getNotificationById(String notificationId) async {
    return await _apiService.get('/notifications/$notificationId');
  }

  /// Mark notification as read
  Future<ApiResponse<void>> markAsRead(String notificationId) async {
    return await _apiService.put('/notifications/$notificationId/read');
  }

  /// Mark notification as unread
  Future<ApiResponse<void>> markAsUnread(String notificationId) async {
    return await _apiService.delete('/notifications/$notificationId/read');
  }

  /// Mark all notifications as read
  Future<ApiResponse<void>> markAllAsRead() async {
    return await _apiService.put('/notifications/mark-all-read');
  }

  /// Delete notification
  Future<ApiResponse<void>> deleteNotification(String notificationId) async {
    return await _apiService.delete('/notifications/$notificationId');
  }

  /// Delete all notifications
  Future<ApiResponse<void>> deleteAllNotifications() async {
    return await _apiService.delete('/notifications');
  }

  /// Get unread notification count
  Future<ApiResponse<Map<String, dynamic>>> getUnreadCount() async {
    return await _apiService.get('/notifications/unread-count');
  }

  /// Get notification types
  Future<ApiResponse<List<Map<String, dynamic>>>> getNotificationTypes() async {
    return await _apiService.get('/notifications/types');
  }

  /// Create notification (admin only)
  Future<ApiResponse<Map<String, dynamic>>> createNotification({
    required String title,
    required String message,
    required String type,
    String? userId,
    List<String>? userIds,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
  }) async {
    final requestData = <String, dynamic>{
      'title': title,
      'message': message,
      'type': type,
    };

    if (userId != null) requestData['userId'] = userId;
    if (userIds != null) requestData['userIds'] = userIds;
    if (data != null) requestData['data'] = data;
    if (scheduledAt != null) requestData['scheduledAt'] = scheduledAt.toIso8601String();

    return await _apiService.post('/notifications', data: requestData);
  }

  /// Send bulk notifications (admin only)
  Future<ApiResponse<Map<String, dynamic>>> sendBulkNotifications({
    required String title,
    required String message,
    required String type,
    required List<String> userIds,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
  }) async {
    final requestData = <String, dynamic>{
      'title': title,
      'message': message,
      'type': type,
      'userIds': userIds,
    };

    if (data != null) requestData['data'] = data;
    if (scheduledAt != null) requestData['scheduledAt'] = scheduledAt.toIso8601String();

    return await _apiService.post('/notifications/bulk', data: requestData);
  }

  /// Send notification to all users (admin only)
  Future<ApiResponse<Map<String, dynamic>>> sendBroadcastNotification({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? data,
    DateTime? scheduledAt,
  }) async {
    final requestData = <String, dynamic>{
      'title': title,
      'message': message,
      'type': type,
    };

    if (data != null) requestData['data'] = data;
    if (scheduledAt != null) requestData['scheduledAt'] = scheduledAt.toIso8601String();

    return await _apiService.post('/notifications/broadcast', data: requestData);
  }

  /// Get notification preferences
  Future<ApiResponse<Map<String, dynamic>>> getNotificationPreferences() async {
    return await _apiService.get('/notifications/preferences');
  }

  /// Update notification preferences
  Future<ApiResponse<void>> updateNotificationPreferences({
    bool? emailNotifications,
    bool? pushNotifications,
    bool? questNotifications,
    bool? achievementNotifications,
    bool? socialNotifications,
    bool? systemNotifications,
    bool? marketingNotifications,
    Map<String, bool>? categoryPreferences,
  }) async {
    final data = <String, dynamic>{};

    if (emailNotifications != null) data['emailNotifications'] = emailNotifications;
    if (pushNotifications != null) data['pushNotifications'] = pushNotifications;
    if (questNotifications != null) data['questNotifications'] = questNotifications;
    if (achievementNotifications != null) data['achievementNotifications'] = achievementNotifications;
    if (socialNotifications != null) data['socialNotifications'] = socialNotifications;
    if (systemNotifications != null) data['systemNotifications'] = systemNotifications;
    if (marketingNotifications != null) data['marketingNotifications'] = marketingNotifications;
    if (categoryPreferences != null) data['categoryPreferences'] = categoryPreferences;

    return await _apiService.put('/notifications/preferences', data: data);
  }

  /// Subscribe to notification topic
  Future<ApiResponse<void>> subscribeToTopic(String topic) async {
    return await _apiService.post('/notifications/topics/$topic/subscribe');
  }

  /// Unsubscribe from notification topic
  Future<ApiResponse<void>> unsubscribeFromTopic(String topic) async {
    return await _apiService.delete('/notifications/topics/$topic/subscribe');
  }

  /// Get subscribed topics
  Future<ApiResponse<List<String>>> getSubscribedTopics() async {
    return await _apiService.get('/notifications/topics/subscribed');
  }

  /// Get available topics
  Future<ApiResponse<List<Map<String, dynamic>>>> getAvailableTopics() async {
    return await _apiService.get('/notifications/topics');
  }

  /// Register device for push notifications
  Future<ApiResponse<void>> registerDevice({
    required String deviceToken,
    required String platform,
    String? deviceId,
    Map<String, dynamic>? deviceInfo,
  }) async {
    final data = <String, dynamic>{
      'deviceToken': deviceToken,
      'platform': platform,
    };

    if (deviceId != null) data['deviceId'] = deviceId;
    if (deviceInfo != null) data['deviceInfo'] = deviceInfo;

    return await _apiService.post('/notifications/devices', data: data);
  }

  /// Unregister device for push notifications
  Future<ApiResponse<void>> unregisterDevice(String deviceToken) async {
    return await _apiService.delete('/notifications/devices/$deviceToken');
  }

  /// Get registered devices
  Future<ApiResponse<List<Map<String, dynamic>>>> getRegisteredDevices() async {
    return await _apiService.get('/notifications/devices');
  }

  /// Test notification (development only)
  Future<ApiResponse<void>> sendTestNotification({
    required String title,
    required String message,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    final requestData = <String, dynamic>{
      'title': title,
      'message': message,
    };

    if (type != null) requestData['type'] = type;
    if (data != null) requestData['data'] = data;

    return await _apiService.post('/notifications/test', data: requestData);
  }

  /// Get notification statistics (admin only)
  Future<ApiResponse<Map<String, dynamic>>> getNotificationStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? type,
  }) async {
    final queryParams = <String, String>{};

    if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
    if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();
    if (type != null) queryParams['type'] = type;

    return await _apiService.get('/notifications/statistics', queryParameters: queryParams);
  }

  /// Get notification delivery status (admin only)
  Future<ApiResponse<Map<String, dynamic>>> getDeliveryStatus(String notificationId) async {
    return await _apiService.get('/notifications/$notificationId/delivery-status');
  }

  /// Retry failed notification (admin only)
  Future<ApiResponse<void>> retryFailedNotification(String notificationId) async {
    return await _apiService.post('/notifications/$notificationId/retry');
  }

  /// Cancel scheduled notification (admin only)
  Future<ApiResponse<void>> cancelScheduledNotification(String notificationId) async {
    return await _apiService.delete('/notifications/$notificationId/schedule');
  }

  /// Get scheduled notifications (admin only)
  Future<ApiResponse<List<Map<String, dynamic>>>> getScheduledNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/notifications/scheduled', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Archive notification
  Future<ApiResponse<void>> archiveNotification(String notificationId) async {
    return await _apiService.put('/notifications/$notificationId/archive');
  }

  /// Unarchive notification
  Future<ApiResponse<void>> unarchiveNotification(String notificationId) async {
    return await _apiService.delete('/notifications/$notificationId/archive');
  }

  /// Get archived notifications
  Future<ApiResponse<List<Map<String, dynamic>>>> getArchivedNotifications({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/notifications/archived', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Clear archived notifications
  Future<ApiResponse<void>> clearArchivedNotifications() async {
    return await _apiService.delete('/notifications/archived');
  }

  /// Export notifications (GDPR compliance)
  Future<ApiResponse<Map<String, dynamic>>> exportNotifications({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};

    if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
    if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

    return await _apiService.get('/notifications/export', queryParameters: queryParams);
  }
}
