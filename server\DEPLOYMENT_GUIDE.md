# Quester Server Deployment Guide

## 🚀 Deployment Options

### 1. Local Development
### 2. Docker Container
### 3. Cloud Deployment (AWS, Google Cloud, Azure)
### 4. Production Server

---

## 🔧 Prerequisites

### System Requirements
- **Dart SDK**: 3.0 or higher
- **Memory**: Minimum 512MB RAM, Recommended 2GB+
- **Storage**: Minimum 1GB free space
- **Network**: Port 8080 (configurable)

### Dependencies
- **MongoDB**: For production database (optional for development)
- **Docker**: For containerized deployment (optional)
- **Reverse Proxy**: Nginx/Apache for production (recommended)

---

## 🏠 Local Development Deployment

### Quick Start
```bash
# Navigate to server directory
cd d:\quester\server

# Install dependencies
dart pub get

# Create configuration
cp config/server.env .env

# Start development server
dart run bin/server.dart
```

### Configuration
Edit `.env` file:
```bash
NODE_ENV=development
HOST=0.0.0.0
PORT=8080
JWT_SECRET=dev-secret-key
DB_HOST=localhost
DB_PORT=27017
DB_NAME=quester_dev
ENABLE_REGISTRATION=true
LOG_LEVEL=debug
```

### Verification
```bash
# Test health endpoint
curl http://localhost:8080/api/health

# Expected response
{
  "status": "healthy",
  "timestamp": "2025-01-02T10:00:00.000Z",
  "uptime": "0:00:30",
  "version": "1.0.0"
}
```

---

## 🐳 Docker Deployment

### Create Dockerfile
```dockerfile
FROM dart:stable AS build

# Set working directory
WORKDIR /app

# Copy pubspec files
COPY pubspec.* ./

# Install dependencies
RUN dart pub get

# Copy source code
COPY . .

# Build application
RUN dart compile exe bin/server.dart -o bin/server

# Runtime stage
FROM debian:bullseye-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false appuser

# Set working directory
WORKDIR /app

# Copy compiled binary
COPY --from=build /app/bin/server ./server
COPY --from=build /app/config ./config

# Change ownership
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/api/health || exit 1

# Start server
CMD ["./server"]
```

### Build and Run
```bash
# Build Docker image
docker build -t quester-server .

# Run container
docker run -d \
  --name quester-server \
  -p 8080:8080 \
  -e NODE_ENV=production \
  -e JWT_SECRET=your-production-secret \
  -e DB_HOST=your-mongodb-host \
  quester-server

# Check logs
docker logs quester-server

# Stop container
docker stop quester-server
```

### Docker Compose
Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  quester-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=${JWT_SECRET}
      - DB_HOST=mongodb
      - DB_NAME=quester_prod
    depends_on:
      - mongodb
    restart: unless-stopped

  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      - MONGO_INITDB_DATABASE=quester_prod
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - quester-server
    restart: unless-stopped

volumes:
  mongodb_data:
```

### Environment File (.env)
```bash
JWT_SECRET=your-super-secure-production-secret
MONGO_USERNAME=admin
MONGO_PASSWORD=secure-password
```

### Run with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f quester-server

# Stop all services
docker-compose down
```

---

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS ECS
1. **Create ECR Repository**
```bash
aws ecr create-repository --repository-name quester-server
```

2. **Build and Push Image**
```bash
# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Tag image
docker tag quester-server:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/quester-server:latest

# Push image
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/quester-server:latest
```

3. **Create ECS Task Definition**
```json
{
  "family": "quester-server",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "quester-server",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/quester-server:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:<account-id>:secret:quester/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/quester-server",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Using AWS Lambda (Serverless)
Create `serverless.yml`:
```yaml
service: quester-server

provider:
  name: aws
  runtime: provided.al2
  region: us-east-1
  environment:
    NODE_ENV: production
    JWT_SECRET: ${env:JWT_SECRET}

functions:
  api:
    handler: bootstrap
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - websocket:
          route: $connect
      - websocket:
          route: $disconnect
      - websocket:
          route: $default

plugins:
  - serverless-dart
```

### Google Cloud Deployment

#### Using Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/quester-server

# Deploy to Cloud Run
gcloud run deploy quester-server \
  --image gcr.io/PROJECT-ID/quester-server \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production \
  --set-env-vars JWT_SECRET=your-secret
```

### Azure Deployment

#### Using Container Instances
```bash
# Create resource group
az group create --name quester-rg --location eastus

# Deploy container
az container create \
  --resource-group quester-rg \
  --name quester-server \
  --image quester-server:latest \
  --dns-name-label quester-server \
  --ports 8080 \
  --environment-variables NODE_ENV=production \
  --secure-environment-variables JWT_SECRET=your-secret
```

---

## 🏭 Production Server Deployment

### System Setup (Ubuntu 20.04+)

#### Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Dart SDK
sudo apt-get update
sudo apt-get install apt-transport-https
wget -qO- https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
wget -qO- https://storage.googleapis.com/download.dartlang.org/linux/debian/dart_stable.list | sudo tee /etc/apt/sources.list.d/dart_stable.list
sudo apt-get update
sudo apt-get install dart

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Nginx
sudo apt install nginx -y

# Install PM2 (Process Manager)
sudo npm install -g pm2
```

#### Create Application User
```bash
sudo useradd -r -s /bin/false quester
sudo mkdir -p /opt/quester
sudo chown quester:quester /opt/quester
```

#### Deploy Application
```bash
# Copy application files
sudo cp -r /path/to/server/* /opt/quester/
sudo chown -R quester:quester /opt/quester

# Install dependencies
cd /opt/quester
sudo -u quester dart pub get

# Compile application
sudo -u quester dart compile exe bin/server.dart -o bin/server
```

#### Configure Environment
Create `/opt/quester/.env`:
```bash
NODE_ENV=production
HOST=127.0.0.1
PORT=8080
JWT_SECRET=your-super-secure-production-secret
DB_HOST=localhost
DB_PORT=27017
DB_NAME=quester_prod
DB_USERNAME=quester_user
DB_PASSWORD=secure_db_password
ENABLE_HTTPS=false
LOG_LEVEL=info
```

#### Create Systemd Service
Create `/etc/systemd/system/quester.service`:
```ini
[Unit]
Description=Quester Server
After=network.target mongodb.service

[Service]
Type=simple
User=quester
Group=quester
WorkingDirectory=/opt/quester
ExecStart=/opt/quester/bin/server
Restart=always
RestartSec=10
Environment=NODE_ENV=production
EnvironmentFile=/opt/quester/.env

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/quester/logs /opt/quester/uploads

[Install]
WantedBy=multi-user.target
```

#### Start Services
```bash
# Enable and start MongoDB
sudo systemctl enable mongod
sudo systemctl start mongod

# Enable and start Quester server
sudo systemctl enable quester
sudo systemctl start quester

# Check status
sudo systemctl status quester
```

#### Configure Nginx Reverse Proxy
Create `/etc/nginx/sites-available/quester`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Proxy to Dart server
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static files (if any)
    location /static/ {
        alias /opt/quester/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable site:
```bash
sudo ln -s /etc/nginx/sites-available/quester /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Using Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### Firewall Configuration
```bash
# Configure UFW
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### MongoDB Security
```bash
# Create database user
mongo
use quester_prod
db.createUser({
  user: "quester_user",
  pwd: "secure_db_password",
  roles: [{ role: "readWrite", db: "quester_prod" }]
})
```

---

## 📊 Monitoring and Logging

### Log Configuration
```bash
# Create log directory
sudo mkdir -p /var/log/quester
sudo chown quester:quester /var/log/quester

# Configure log rotation
sudo tee /etc/logrotate.d/quester << EOF
/var/log/quester/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 quester quester
    postrotate
        systemctl reload quester
    endscript
}
EOF
```

### Health Monitoring
```bash
# Create health check script
sudo tee /usr/local/bin/quester-health-check << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/health)
if [ $response -eq 200 ]; then
    echo "Quester server is healthy"
    exit 0
else
    echo "Quester server is unhealthy (HTTP $response)"
    exit 1
fi
EOF

sudo chmod +x /usr/local/bin/quester-health-check

# Add to crontab for monitoring
echo "*/5 * * * * /usr/local/bin/quester-health-check" | sudo crontab -
```

---

## 🔄 Maintenance

### Backup Strategy
```bash
# Database backup
mongodump --db quester_prod --out /backup/mongodb/$(date +%Y%m%d)

# Application backup
tar -czf /backup/app/quester-$(date +%Y%m%d).tar.gz /opt/quester
```

### Updates
```bash
# Stop service
sudo systemctl stop quester

# Backup current version
sudo cp -r /opt/quester /opt/quester.backup

# Deploy new version
# ... copy new files ...

# Restart service
sudo systemctl start quester

# Verify deployment
curl http://localhost:8080/api/health
```

### Troubleshooting
```bash
# Check service status
sudo systemctl status quester

# View logs
sudo journalctl -u quester -f

# Check application logs
sudo tail -f /var/log/quester/app.log

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
```

This deployment guide covers all major deployment scenarios from local development to production-ready cloud deployments.
