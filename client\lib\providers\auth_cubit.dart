import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shared/shared.dart' hide ApiService, WebSocketService;

import '../services/api_service.dart';
import '../services/websocket_service.dart';

/// Authentication states
abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final User user;
  final String token;

  AuthAuthenticated({required this.user, required this.token});

  @override
  List<Object?> get props => [user, token];
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Authentication management cubit
class AuthCubit extends Cubit<AuthState> {
  final ApiService _apiService;
  final WebSocketService _webSocketService;

  // Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  AuthCubit({
    required ApiService apiService,
    required WebSocketService webSocketService,
  })  : _apiService = apiService,
        _webSocketService = webSocketService,
        super(AuthInitial()) {
    _checkAuthStatus();
  }

  /// Check if user is already authenticated
  Future<void> _checkAuthStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);
      final userData = prefs.getString(_userKey);

      if (token != null && userData != null) {
        // Set API token
        _apiService.setAuthToken(token);

        try {
          // Verify token with server
          final response = await _apiService.getCurrentUser();
          if (response.success && response.data != null) {
            final user = response.data!;
            emit(AuthAuthenticated(user: user, token: token));

            // Connect to WebSocket
            _webSocketService.connect(token);
          } else {
            // Token is invalid, clear storage
            await prefs.remove(_tokenKey);
            await prefs.remove(_userKey);
            _apiService.clearAuthToken();
            emit(AuthUnauthenticated());
          }
        } catch (e) {
          // Network error or token invalid, try to use cached user data
          try {
            final userJson = jsonDecode(userData) as Map<String, dynamic>;
            final user = User.fromJson(userJson);
            emit(AuthAuthenticated(user: user, token: token));

            // Try to connect to WebSocket
            _webSocketService.connect(token);
          } catch (parseError) {
            // Cached data is corrupted, clear everything
            await prefs.remove(_tokenKey);
            await prefs.remove(_userKey);
            _apiService.clearAuthToken();
            emit(AuthUnauthenticated());
          }
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }

  /// Login with email and password
  Future<void> login(String email, String password) async {
    emit(AuthLoading());
    
    try {
      // Call login API
      final loginRequest = LoginRequest(email: email, password: password);
      final response = await _apiService.login(loginRequest);

      if (response.success && response.data != null) {
        final authResponse = response.data!;
        final user = authResponse.user;
        final token = authResponse.accessToken;

        // Set API token
        _apiService.setAuthToken(token);

        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, token);
        await prefs.setString(_userKey, jsonEncode(user.toJson()));

        emit(AuthAuthenticated(user: user, token: token));

        // Connect to WebSocket
        _webSocketService.connect(token);
      } else {
        emit(AuthError(message: response.message ?? 'Login failed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Register new user
  Future<void> register(RegisterRequest request) async {
    emit(AuthLoading());
    
    try {
      // Call register API
      final response = await _apiService.register(request);

      if (response.success && response.data != null) {
        final authResponse = response.data!;
        final user = authResponse.user;
        final token = authResponse.accessToken;

        // Set API token
        _apiService.setAuthToken(token);

        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, token);
        await prefs.setString(_userKey, jsonEncode(user.toJson()));

        emit(AuthAuthenticated(user: user, token: token));

        // Connect to WebSocket
        _webSocketService.connect(token);
      } else {
        emit(AuthError(message: response.message ?? 'Registration failed'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Disconnect WebSocket
      await _webSocketService.disconnect();

      // Clear local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);

      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  /// Get current user
  User? get currentUser {
    final state = this.state;
    if (state is AuthAuthenticated) {
      return state.user;
    }
    return null;
  }

  /// Get current token
  String? get currentToken {
    final state = this.state;
    if (state is AuthAuthenticated) {
      return state.token;
    }
    return null;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Check if authentication is loading
  bool get isLoading => state is AuthLoading;
}
