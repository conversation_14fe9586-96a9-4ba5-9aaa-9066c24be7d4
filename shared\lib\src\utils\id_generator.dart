import 'dart:math';
import 'crypto_utils.dart';

/// ID generation utility class
class IdGenerator {
  static final Random _random = Random.secure();
  
  /// Generate a short ID (8 characters)
  static String generateShortId() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(8, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate a medium ID (16 characters)
  static String generateMediumId() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(16, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate a long ID (32 characters)
  static String generateLongId() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(32, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate UUID v4
  static String generateUuid() {
    return CryptoUtils.generateUuid();
  }
  
  /// Generate user ID
  static String generateUserId() {
    return 'user_${generateMediumId()}';
  }
  
  /// Generate quest ID
  static String generateQuestId() {
    return 'quest_${generateMediumId()}';
  }
  
  /// Generate notification ID
  static String generateNotificationId() {
    return 'notif_${generateMediumId()}';
  }
  
  /// Generate session ID
  static String generateSessionId() {
    return 'sess_${generateLongId()}';
  }
  
  /// Generate API key
  static String generateApiKey() {
    return 'ak_${generateLongId()}';
  }
  
  /// Generate transaction ID
  static String generateTransactionId() {
    return 'txn_${generateMediumId()}';
  }
  
  /// Generate file ID
  static String generateFileId() {
    return 'file_${generateMediumId()}';
  }
  
  /// Generate activity ID
  static String generateActivityId() {
    return 'act_${generateMediumId()}';
  }
  
  /// Generate custom ID with prefix
  static String generateCustomId(String prefix, {int length = 16}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final id = List.generate(length, (index) => chars[_random.nextInt(chars.length)]).join();
    return '${prefix}_$id';
  }
  
  /// Generate numeric ID
  static String generateNumericId(int length) {
    const chars = '0123456789';
    return List.generate(length, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate alphanumeric ID
  static String generateAlphanumericId(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(length, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate URL-safe ID
  static String generateUrlSafeId(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_';
    return List.generate(length, (index) => chars[_random.nextInt(chars.length)]).join();
  }
  
  /// Generate timestamp-based ID
  static String generateTimestampId({String? prefix}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = generateAlphanumericId(8);
    final id = '${timestamp}_$randomSuffix';
    return prefix != null ? '${prefix}_$id' : id;
  }
  
  /// Generate sortable ID (timestamp + random)
  static String generateSortableId({String? prefix}) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toRadixString(36);
    final randomSuffix = generateAlphanumericId(8).toLowerCase();
    final id = '$timestamp$randomSuffix';
    return prefix != null ? '${prefix}_$id' : id;
  }
  
  /// Generate human-readable ID
  static String generateHumanReadableId({String? prefix}) {
    const adjectives = [
      'happy', 'clever', 'bright', 'swift', 'brave', 'calm', 'eager', 'fair',
      'gentle', 'kind', 'lively', 'nice', 'proud', 'quiet', 'smart', 'wise'
    ];
    
    const nouns = [
      'cat', 'dog', 'bird', 'fish', 'lion', 'bear', 'wolf', 'fox',
      'deer', 'owl', 'hawk', 'dove', 'swan', 'duck', 'frog', 'bee'
    ];
    
    final adjective = adjectives[_random.nextInt(adjectives.length)];
    final noun = nouns[_random.nextInt(nouns.length)];
    final number = _random.nextInt(1000);
    
    final id = '$adjective-$noun-$number';
    return prefix != null ? '${prefix}_$id' : id;
  }
  
  /// Generate ID with checksum
  static String generateIdWithChecksum(int length) {
    final id = generateAlphanumericId(length - 2);
    final checksum = CryptoUtils.generateChecksum(id).substring(0, 2);
    return '$id$checksum';
  }
  
  /// Validate ID with checksum
  static bool validateIdWithChecksum(String id) {
    if (id.length < 3) return false;
    
    final mainId = id.substring(0, id.length - 2);
    final checksum = id.substring(id.length - 2);
    final expectedChecksum = CryptoUtils.generateChecksum(mainId).substring(0, 2);
    
    return checksum == expectedChecksum;
  }
  
  /// Generate batch of unique IDs
  static List<String> generateBatch(int count, {String? prefix, int length = 16}) {
    final ids = <String>{};
    
    while (ids.length < count) {
      final id = prefix != null 
          ? generateCustomId(prefix, length: length)
          : generateAlphanumericId(length);
      ids.add(id);
    }
    
    return ids.toList();
  }
  
  /// Check if ID format is valid
  static bool isValidIdFormat(String id, {String? expectedPrefix}) {
    if (id.isEmpty) return false;
    
    if (expectedPrefix != null) {
      if (!id.startsWith('${expectedPrefix}_')) return false;
      final actualId = id.substring(expectedPrefix.length + 1);
      return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(actualId);
    }
    
    return RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(id);
  }
  
  /// Extract prefix from ID
  static String? extractPrefix(String id) {
    final parts = id.split('_');
    return parts.length > 1 ? parts[0] : null;
  }
  
  /// Extract main ID part (without prefix)
  static String extractMainId(String id) {
    final parts = id.split('_');
    return parts.length > 1 ? parts.sublist(1).join('_') : id;
  }
}
