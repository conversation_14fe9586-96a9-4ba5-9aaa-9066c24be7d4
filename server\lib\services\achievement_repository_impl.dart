import 'dart:async';
import 'package:shared/shared.dart';

/// In-memory implementation of AchievementRepository for development/testing
class InMemoryAchievementRepository implements AchievementRepository {
  final Map<String, Achievement> _achievements = {};
  final Map<String, UserAchievement> _userAchievements = {}; // userAchievementId -> UserAchievement
  final Map<String, List<String>> _userAchievementIds = {}; // userId -> userAchievementIds

  @override
  Future<Achievement> create(Achievement entity) async {
    _achievements[entity.id] = entity;
    return entity;
  }

  @override
  Future<Achievement?> getById(String id) async {
    return _achievements[id];
  }

  @override
  Future<List<Achievement>> getAll({int page = 1, int pageSize = 20}) async {
    final achievements = _achievements.values.toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= achievements.length) return [];
    
    return achievements.sublist(
      startIndex,
      endIndex > achievements.length ? achievements.length : endIndex,
    );
  }

  @override
  Future<Achievement> update(String id, Achievement entity) async {
    if (!_achievements.containsKey(id)) {
      throw Exception('Achievement not found');
    }
    _achievements[id] = entity;
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    _achievements.remove(id);
    
    // Remove all user achievements for this achievement
    final userAchievementsToRemove = _userAchievements.entries
        .where((entry) => entry.value.achievementId == id)
        .map((entry) => entry.key)
        .toList();
    
    for (final userAchievementId in userAchievementsToRemove) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null) {
        _userAchievements.remove(userAchievementId);
        _userAchievementIds[userAchievement.userId]?.remove(userAchievementId);
      }
    }
  }

  @override
  Future<bool> exists(String id) async {
    return _achievements.containsKey(id);
  }

  @override
  Future<int> count() async {
    return _achievements.length;
  }

  @override
  Future<List<Achievement>> search(String query, {int page = 1, int pageSize = 20}) async {
    final achievements = _achievements.values.where((achievement) =>
        achievement.title.toLowerCase().contains(query.toLowerCase()) ||
        achievement.description.toLowerCase().contains(query.toLowerCase())
    ).toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= achievements.length) return [];
    
    return achievements.sublist(
      startIndex,
      endIndex > achievements.length ? achievements.length : endIndex,
    );
  }

  @override
  Future<List<Achievement>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    var achievements = _achievements.values.toList();
    
    // Apply filters
    if (filters.containsKey('category')) {
      final category = AchievementCategory.values.firstWhere(
        (c) => c.name == filters['category'],
        orElse: () => AchievementCategory.milestone,
      );
      achievements = achievements.where((achievement) => achievement.category == category).toList();
    }
    
    if (filters.containsKey('rarity')) {
      final rarity = AchievementRarity.values.firstWhere(
        (r) => r.name == filters['rarity'],
        orElse: () => AchievementRarity.common,
      );
      achievements = achievements.where((achievement) => achievement.rarity == rarity).toList();
    }
    
    if (filters.containsKey('isHidden')) {
      final isHidden = filters['isHidden'] as bool;
      achievements = achievements.where((achievement) => achievement.isHidden == isHidden).toList();
    }
    
    if (filters.containsKey('isAvailable')) {
      final isAvailable = filters['isAvailable'] as bool;
      achievements = achievements.where((achievement) => achievement.isAvailable == isAvailable).toList();
    }
    
    achievements.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= achievements.length) return [];
    
    return achievements.sublist(
      startIndex,
      endIndex > achievements.length ? achievements.length : endIndex,
    );
  }

  @override
  Future<List<Achievement>> createBatch(List<Achievement> entities) async {
    for (final achievement in entities) {
      _achievements[achievement.id] = achievement;
    }
    return entities;
  }

  @override
  Future<List<Achievement>> updateBatch(List<Achievement> entities) async {
    for (final achievement in entities) {
      if (_achievements.containsKey(achievement.id)) {
        _achievements[achievement.id] = achievement;
      }
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      await delete(id);
    }
  }

  @override
  Future<List<Achievement>> getByCategory(String category, {int page = 1, int pageSize = 20}) async {
    final categoryEnum = AchievementCategory.values.firstWhere(
      (c) => c.name == category,
      orElse: () => AchievementCategory.milestone,
    );
    
    final achievements = _achievements.values
        .where((achievement) => achievement.category == categoryEnum)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= achievements.length) return [];
    
    return achievements.sublist(
      startIndex,
      endIndex > achievements.length ? achievements.length : endIndex,
    );
  }

  @override
  Future<List<Achievement>> getUserAchievements(String userId, {int page = 1, int pageSize = 20}) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    final achievements = <Achievement>[];
    
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null && userAchievement.status == AchievementStatus.unlocked) {
        final achievement = _achievements[userAchievement.achievementId];
        if (achievement != null) {
          achievements.add(achievement);
        }
      }
    }
    
    achievements.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= achievements.length) return [];
    
    return achievements.sublist(
      startIndex,
      endIndex > achievements.length ? achievements.length : endIndex,
    );
  }

  @override
  Future<List<Achievement>> getAvailableForUser(String userId, {int page = 1, int pageSize = 20}) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    final userAchievementMap = <String, UserAchievement>{};
    
    // Build map of user's achievements
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null) {
        userAchievementMap[userAchievement.achievementId] = userAchievement;
      }
    }
    
    // Get achievements that are available and not yet unlocked
    final availableAchievements = _achievements.values.where((achievement) {
      if (!achievement.isAvailable || achievement.isHidden) return false;
      
      final userAchievement = userAchievementMap[achievement.id];
      return userAchievement == null || userAchievement.status != AchievementStatus.unlocked;
    }).toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= availableAchievements.length) return [];
    
    return availableAchievements.sublist(
      startIndex,
      endIndex > availableAchievements.length ? availableAchievements.length : endIndex,
    );
  }

  @override
  Future<void> awardToUser(String achievementId, String userId) async {
    final achievement = _achievements[achievementId];
    if (achievement == null) {
      throw Exception('Achievement not found');
    }
    
    // Check if user already has this achievement
    if (await userHasAchievement(userId, achievementId)) {
      if (!achievement.isRepeatable) {
        throw Exception('User already has this achievement and it is not repeatable');
      }
    }
    
    // Create user achievement
    final userAchievement = UserAchievement(
      id: IdGenerator.generateCustomId('user_achievement'),
      userId: userId,
      achievementId: achievementId,
      achievement: achievement,
      status: AchievementStatus.unlocked,
      progress: 1.0,
      unlockedAt: DateTime.now(),
    );
    
    _userAchievements[userAchievement.id] = userAchievement;
    
    if (!_userAchievementIds.containsKey(userId)) {
      _userAchievementIds[userId] = [];
    }
    _userAchievementIds[userId]!.add(userAchievement.id);
  }

  @override
  Future<bool> userHasAchievement(String userId, String achievementId) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null && 
          userAchievement.achievementId == achievementId &&
          userAchievement.status == AchievementStatus.unlocked) {
        return true;
      }
    }
    
    return false;
  }

  @override
  Future<Map<String, dynamic>> getProgressForUser(String userId, String achievementId) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null && userAchievement.achievementId == achievementId) {
        return {
          'progress': userAchievement.progress,
          'status': userAchievement.status.name,
          'progressData': userAchievement.progressData ?? {},
          'unlockedAt': userAchievement.unlockedAt?.toIso8601String(),
        };
      }
    }
    
    return {
      'progress': 0.0,
      'status': AchievementStatus.locked.name,
      'progressData': {},
    };
  }

  @override
  Future<void> updateProgressForUser(String userId, String achievementId, Map<String, dynamic> progress) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    UserAchievement? existingUserAchievement;
    String? existingId;
    
    // Find existing user achievement
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null && userAchievement.achievementId == achievementId) {
        existingUserAchievement = userAchievement;
        existingId = userAchievementId;
        break;
      }
    }
    
    final achievement = _achievements[achievementId];
    if (achievement == null) {
      throw Exception('Achievement not found');
    }
    
    final progressValue = (progress['progress'] as num?)?.toDouble() ?? 0.0;
    final progressData = progress['progressData'] as Map<String, dynamic>? ?? {};
    
    // Determine status based on progress
    AchievementStatus status;
    DateTime? unlockedAt;
    
    if (progressValue >= 1.0) {
      status = AchievementStatus.unlocked;
      unlockedAt = DateTime.now();
    } else if (progressValue > 0.0) {
      status = AchievementStatus.inProgress;
    } else {
      status = AchievementStatus.locked;
    }
    
    if (existingUserAchievement != null && existingId != null) {
      // Update existing
      final updatedUserAchievement = UserAchievement(
        id: existingUserAchievement.id,
        userId: userId,
        achievementId: achievementId,
        achievement: achievement,
        status: status,
        progress: progressValue,
        unlockedAt: unlockedAt ?? existingUserAchievement.unlockedAt,
        progressData: progressData,
      );
      
      _userAchievements[existingId] = updatedUserAchievement;
    } else {
      // Create new
      final userAchievement = UserAchievement(
        id: IdGenerator.generateCustomId('user_achievement'),
        userId: userId,
        achievementId: achievementId,
        achievement: achievement,
        status: status,
        progress: progressValue,
        unlockedAt: unlockedAt,
        progressData: progressData,
      );
      
      _userAchievements[userAchievement.id] = userAchievement;
      
      if (!_userAchievementIds.containsKey(userId)) {
        _userAchievementIds[userId] = [];
      }
      _userAchievementIds[userId]!.add(userAchievement.id);
    }
  }

  /// Get all user achievements (including progress)
  Future<List<UserAchievement>> getAllUserAchievements(String userId) async {
    final userAchievementIds = _userAchievementIds[userId] ?? [];
    final userAchievements = <UserAchievement>[];
    
    for (final userAchievementId in userAchievementIds) {
      final userAchievement = _userAchievements[userAchievementId];
      if (userAchievement != null) {
        userAchievements.add(userAchievement);
      }
    }
    
    return userAchievements;
  }

  /// Initialize with sample achievements
  Future<void> initializeSampleAchievements() async {
    final sampleAchievements = [
      Achievement(
        id: 'achievement_first_quest',
        title: 'First Steps',
        description: 'Complete your first quest',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.common,
        xpReward: 50,
        requirements: [
          AchievementRequirement(
            id: 'req_1',
            type: AchievementRequirementType.questCompletion,
            description: 'Complete 1 quest',
            criteria: {'questCount': 1},
            targetValue: 1,
            unit: 'quests',
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Achievement(
        id: 'achievement_level_5',
        title: 'Rising Star',
        description: 'Reach level 5',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.uncommon,
        xpReward: 100,
        requirements: [
          AchievementRequirement(
            id: 'req_2',
            type: AchievementRequirementType.levelReached,
            description: 'Reach level 5',
            criteria: {'level': 5},
            targetValue: 5,
            unit: 'level',
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Achievement(
        id: 'achievement_quest_master',
        title: 'Quest Master',
        description: 'Complete 10 quests',
        category: AchievementCategory.quest,
        rarity: AchievementRarity.rare,
        xpReward: 250,
        coinReward: 100,
        requirements: [
          AchievementRequirement(
            id: 'req_3',
            type: AchievementRequirementType.questCompletion,
            description: 'Complete 10 quests',
            criteria: {'questCount': 10},
            targetValue: 10,
            unit: 'quests',
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Achievement(
        id: 'achievement_xp_1000',
        title: 'Experience Seeker',
        description: 'Earn 1000 XP',
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.uncommon,
        xpReward: 150,
        requirements: [
          AchievementRequirement(
            id: 'req_4',
            type: AchievementRequirementType.xpEarned,
            description: 'Earn 1000 XP',
            criteria: {'xpAmount': 1000},
            targetValue: 1000,
            unit: 'XP',
          ),
        ],
        createdAt: DateTime.now(),
      ),
    ];

    for (final achievement in sampleAchievements) {
      await create(achievement);
    }
  }
}
