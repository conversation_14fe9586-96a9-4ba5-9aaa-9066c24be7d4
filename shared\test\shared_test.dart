import 'package:shared/shared.dart';
import 'package:test/test.dart';

void main() {
  group('Shared Package Tests', () {
    setUp(() {
      // Additional setup goes here.
    });

    test('User model creation and serialization', () {
      final user = User(
        id: 'test-id',
        username: 'testuser',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        createdAt: DateTime.now(),
      );

      expect(user.id, equals('test-id'));
      expect(user.username, equals('testuser'));
      expect(user.email, equals('<EMAIL>'));
      expect(user.firstName, equals('Test'));
      expect(user.lastName, equals('User'));
      expect(user.fullName, equals('Test User'));
      expect(user.isActive, isTrue);
      expect(user.role, equals(UserRole.user));
    });

    test('API Response creation', () {
      final response = ApiResponse<String>.success(
        data: 'test data',
        message: 'Success message',
      );

      expect(response.success, isTrue);
      expect(response.data, equals('test data'));
      expect(response.message, equals('Success message'));
    });

    test('User JSON serialization', () {
      final user = User(
        id: 'test-id',
        username: 'testuser',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        createdAt: DateTime.parse('2025-01-01T00:00:00.000Z'),
      );

      final json = user.toJson();
      final userFromJson = User.fromJson(json);

      expect(userFromJson.id, equals(user.id));
      expect(userFromJson.username, equals(user.username));
      expect(userFromJson.email, equals(user.email));
      expect(userFromJson.firstName, equals(user.firstName));
      expect(userFromJson.lastName, equals(user.lastName));
    });
  });
}
