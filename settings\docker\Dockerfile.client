# Multi-stage Dockerfile for Flutter client
FROM ghcr.io/cirruslabs/flutter:stable AS base

WORKDIR /app

# Copy shared package first (complete package structure)
COPY shared/pubspec.yaml ./shared/
COPY shared/pubspec.lock ./shared/
COPY shared/lib/ ./shared/lib/
COPY shared/analysis_options.yaml ./shared/

# Install shared package dependencies first
WORKDIR /app/shared
RUN dart pub get

# Copy client pubspec files for better caching
WORKDIR /app
COPY client/pubspec.yaml ./client/
COPY client/pubspec.lock ./client/

WORKDIR /app/client

# Clean up any potential symlinks from the copied files
RUN find . -type l -delete 2>/dev/null || true

# Get Flutter dependencies (which includes the shared package)
RUN flutter pub get

# Clean up ephemeral files and symlinks created by pub get
RUN find . -type l -delete 2>/dev/null || true && \
    rm -rf linux/flutter/ephemeral/ && \
    rm -rf macos/Flutter/ephemeral/ && \
    rm -rf windows/flutter/ephemeral/ && \
    find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true

# Development stage
FROM base AS development

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Go back to app directory to preserve proper relative paths
WORKDIR /app

# Copy remaining shared package files
COPY shared/ ./shared/

# Copy client source code excluding problematic files
COPY client/ ./client/

WORKDIR /app/client

# Aggressively clean up all ephemeral and symlinked files that might cause issues
RUN find . -type l -delete 2>/dev/null || true && \
    rm -rf linux/flutter/ephemeral/ && \
    rm -rf macos/Flutter/ephemeral/ && \
    rm -rf windows/flutter/ephemeral/ && \
    find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "ephemeral" -type d -exec rm -rf {} + 2>/dev/null || true && \
    rm -rf .dart_tool/ && \
    rm -rf build/

# Ensure dependencies are properly resolved after copying source
RUN flutter pub get

# Clean up any new symlinks created during pub get
RUN find . -type l -delete 2>/dev/null || true && \
    find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true

EXPOSE 8000

# Run Flutter in web server mode for development
CMD ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "8000"]

# Build stage
FROM base AS build

# Go back to app directory to preserve proper relative paths
WORKDIR /app

# Copy remaining shared package files
COPY shared/ ./shared/

# Copy client source code
COPY client/ ./client/

WORKDIR /app/client

# Aggressively clean up all ephemeral and symlinked files that might cause issues
RUN find . -type l -delete 2>/dev/null || true && \
    rm -rf linux/flutter/ephemeral/ && \
    rm -rf macos/Flutter/ephemeral/ && \
    rm -rf windows/flutter/ephemeral/ && \
    find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true && \
    find . -name "ephemeral" -type d -exec rm -rf {} + 2>/dev/null || true && \
    rm -rf .dart_tool/ && \
    rm -rf build/

# Ensure dependencies are properly resolved after copying source
RUN flutter pub get

# Clean up any new symlinks created during pub get
RUN find . -type l -delete 2>/dev/null || true && \
    find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true

# Build web application for production
RUN flutter build web --release

# Production stage
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built web app from build stage
COPY --from=build /app/client/build/web /usr/share/nginx/html

# Create nginx user and set permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html && \
    # Create directories needed for nginx to run as non-root user
    mkdir -p /var/cache/nginx/client_temp && \
    mkdir -p /var/cache/nginx/proxy_temp && \
    mkdir -p /var/cache/nginx/fastcgi_temp && \
    mkdir -p /var/cache/nginx/uwsgi_temp && \
    mkdir -p /var/cache/nginx/scgi_temp && \
    chown -R nginx:nginx /var/cache/nginx && \
    chmod -R 755 /var/cache/nginx && \
    # Create log directories
    mkdir -p /var/log/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    # Create pid directory
    mkdir -p /var/run && \
    chown nginx:nginx /var/run

# Copy custom nginx configuration if needed
# COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

# Use nginx user for security
USER nginx

CMD ["nginx", "-g", "daemon off;"]
