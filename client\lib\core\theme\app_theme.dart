import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared/shared.dart';

import 'design_tokens.dart';

/// Universal Material 3 Theme Configuration for Quester
///
/// Implements comprehensive Material 3 design system with:
/// - Dynamic color schemes from design tokens
/// - Responsive typography system
/// - Universal component theming
/// - Cross-platform optimization
class AppTheme {
  // Use design tokens for consistent theming
  static Color get primaryColor => DesignTokens.primarySeed;
  static Color get secondaryColor => DesignTokens.secondarySeed;
  static Color get accentColor => DesignTokens.tertiarySeed;

  // Grey palette for elevated app bar
  static Color get greyPrimary => DesignTokens.greyPrimary;
  static Color get greySecondary => DesignTokens.greySecondary;
  static Color get greyLight => DesignTokens.greyLight;
  static Color get greyDark => DesignTokens.greyDark;

  // Status colors
  static Color get successColor => DesignTokens.successColor;
  static Color get warningColor => DesignTokens.warningColor;
  static Color get errorColor => DesignTokens.errorColor;
  static Color get infoColor => DesignTokens.infoColor;

  // Surface colors
  static Color get surfaceLight => DesignTokens.surfaceLight;
  static Color get surfaceDark => DesignTokens.surfaceDark;
  
  /// Universal Light Theme with Material 3 Design System
  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.light,
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: surfaceLight,
      error: errorColor,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,

      // Typography with design tokens
      textTheme: _buildTextTheme(colorScheme),

      // Component themes with responsive design
      appBarTheme: _buildAppBarTheme(colorScheme, true),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme),
      cardTheme: _buildCardTheme(colorScheme),
      navigationRailTheme: _buildNavigationRailTheme(colorScheme, true),
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(colorScheme, true),
      drawerTheme: _buildDrawerTheme(colorScheme, true),
      snackBarTheme: _buildSnackBarTheme(colorScheme),
      chipTheme: _buildChipTheme(colorScheme),
      floatingActionButtonTheme: _buildFABTheme(colorScheme),

      // Additional Material 3 themes
      dividerTheme: _buildDividerTheme(colorScheme),
      listTileTheme: _buildListTileTheme(colorScheme),
      switchTheme: _buildSwitchTheme(colorScheme),
      checkboxTheme: _buildCheckboxTheme(colorScheme),
      radioTheme: _buildRadioTheme(colorScheme),
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.dark,
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: surfaceDark,
      error: errorColor,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: _buildTextTheme(colorScheme),
      appBarTheme: _buildAppBarTheme(colorScheme, false),
      elevatedButtonTheme: _buildElevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(colorScheme),
      textButtonTheme: _buildTextButtonTheme(colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(colorScheme),
      cardTheme: _buildCardTheme(colorScheme),
      navigationRailTheme: _buildNavigationRailTheme(colorScheme, false),
      bottomNavigationBarTheme: _buildBottomNavigationBarTheme(colorScheme, false),
      drawerTheme: _buildDrawerTheme(colorScheme, false),
      snackBarTheme: _buildSnackBarTheme(colorScheme),
      chipTheme: _buildChipTheme(colorScheme),
      floatingActionButtonTheme: _buildFABTheme(colorScheme),
    );
  }

  /// Build text theme with Google Fonts
  static TextTheme _buildTextTheme(ColorScheme colorScheme) {
    return GoogleFonts.interTextTheme().copyWith(
      displayLarge: GoogleFonts.inter(
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: colorScheme.onSurface,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      displaySmall: GoogleFonts.inter(
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 32,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      headlineSmall: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      titleLarge: GoogleFonts.inter(
        fontSize: 22,
        fontWeight: FontWeight.w500,
        letterSpacing: 0,
        color: colorScheme.onSurface,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        color: colorScheme.onSurface,
      ),
      titleSmall: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: colorScheme.onSurface,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: colorScheme.onSurface,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: colorScheme.onSurface,
      ),
      labelLarge: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: colorScheme.onSurface,
      ),
      labelMedium: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
      ),
      labelSmall: GoogleFonts.inter(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: colorScheme.onSurface,
      ),
    );
  }

  /// Build responsive elevated grey app bar theme with design tokens
  static AppBarTheme _buildAppBarTheme(ColorScheme colorScheme, bool isLight) {
    return AppBarTheme(
      elevation: DesignTokens.elevationLevel3,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      backgroundColor: isLight ? greyLight : greyDark,
      foregroundColor: isLight ? greyDark : greyLight,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: GoogleFonts.inter(
        fontSize: DesignTokens.titleLarge,
        fontWeight: FontWeight.w600,
        color: isLight ? greyDark : greyLight,
        letterSpacing: 0.15,
      ),
      iconTheme: IconThemeData(
        color: isLight ? greyDark : greyLight,
        size: DesignTokens.iconMedium,
      ),
      actionsIconTheme: IconThemeData(
        color: isLight ? greyDark : greyLight,
        size: DesignTokens.iconMedium,
      ),
      centerTitle: false,
      // Responsive toolbar height will be handled by the app bar component
      toolbarHeight: DesignTokens.appBarHeightDesktop,
    );
  }

  /// Build elevated button theme
  static ElevatedButtonThemeData _buildElevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  /// Build outlined button theme
  static OutlinedButtonThemeData _buildOutlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        side: BorderSide(color: colorScheme.outline),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  /// Build text button theme
  static TextButtonThemeData _buildTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.1,
        ),
      ),
    );
  }

  /// Build input decoration theme
  static InputDecorationTheme _buildInputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      labelStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      hintStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  /// Build card theme
  static CardThemeData _buildCardTheme(ColorScheme colorScheme) {
    return CardThemeData(
      elevation: 2,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.all(8),
    );
  }

  /// Build navigation rail theme
  static NavigationRailThemeData _buildNavigationRailTheme(ColorScheme colorScheme, bool isLight) {
    return NavigationRailThemeData(
      backgroundColor: isLight ? greyLight : greyDark,
      elevation: 4,
      selectedIconTheme: IconThemeData(
        color: colorScheme.primary,
        size: 24,
      ),
      unselectedIconTheme: IconThemeData(
        color: isLight ? greyDark.withValues(alpha: 0.6) : greyLight.withValues(alpha: 0.6),
        size: 24,
      ),
      selectedLabelTextStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: colorScheme.primary,
      ),
      unselectedLabelTextStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: isLight ? greyDark.withValues(alpha: 0.6) : greyLight.withValues(alpha: 0.6),
      ),
    );
  }

  /// Build bottom navigation bar theme
  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme(ColorScheme colorScheme, bool isLight) {
    return BottomNavigationBarThemeData(
      backgroundColor: isLight ? greyLight : greyDark,
      elevation: 8,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: isLight ? greyDark.withValues(alpha: 0.6) : greyLight.withValues(alpha: 0.6),
      selectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
      type: BottomNavigationBarType.fixed,
    );
  }

  /// Build drawer theme
  static DrawerThemeData _buildDrawerTheme(ColorScheme colorScheme, bool isLight) {
    return DrawerThemeData(
      backgroundColor: colorScheme.surface,
      elevation: 16,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
    );
  }

  /// Build snack bar theme
  static SnackBarThemeData _buildSnackBarTheme(ColorScheme colorScheme) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.inverseSurface,
      contentTextStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: colorScheme.onInverseSurface,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      behavior: SnackBarBehavior.floating,
      elevation: 6,
    );
  }

  /// Build chip theme
  static ChipThemeData _buildChipTheme(ColorScheme colorScheme) {
    return ChipThemeData(
      backgroundColor: colorScheme.surface,
      selectedColor: colorScheme.primary.withValues(alpha: 0.12),
      disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
      deleteIconColor: colorScheme.onSurface,
      labelStyle: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      pressElevation: 2,
    );
  }

  /// Build floating action button theme with design tokens
  static FloatingActionButtonThemeData _buildFABTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      elevation: DesignTokens.elevationLevel3,
      focusElevation: DesignTokens.elevationLevel4,
      hoverElevation: DesignTokens.elevationLevel4,
      highlightElevation: DesignTokens.elevationLevel5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusLarge),
      ),
    );
  }

  /// Build divider theme with design tokens
  static DividerThemeData _buildDividerTheme(ColorScheme colorScheme) {
    return DividerThemeData(
      color: colorScheme.outline.withValues(alpha: 0.2),
      thickness: 1.0,
      space: DesignTokens.spaceMedium,
    );
  }

  /// Build list tile theme with design tokens
  static ListTileThemeData _buildListTileTheme(ColorScheme colorScheme) {
    return ListTileThemeData(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: DesignTokens.spaceMedium,
        vertical: DesignTokens.spaceSmall,
      ),
      minVerticalPadding: DesignTokens.spaceSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusSmall),
      ),
    );
  }

  /// Build switch theme with design tokens
  static SwitchThemeData _buildSwitchTheme(ColorScheme colorScheme) {
    return SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary.withValues(alpha: 0.5);
        }
        return colorScheme.surfaceContainerHighest;
      }),
    );
  }

  /// Build checkbox theme with design tokens
  static CheckboxThemeData _buildCheckboxTheme(ColorScheme colorScheme) {
    return CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return Colors.transparent;
      }),
      checkColor: WidgetStateProperty.all(colorScheme.onPrimary),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusXSmall),
      ),
    );
  }

  /// Build radio theme with design tokens
  static RadioThemeData _buildRadioTheme(ColorScheme colorScheme) {
    return RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.primary;
        }
        return colorScheme.outline;
      }),
    );
  }
}
