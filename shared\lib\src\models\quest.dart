import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'quest.g.dart';

/// Quest difficulty levels
enum QuestDifficulty {
  @JsonValue('easy')
  easy,
  @JsonValue('medium')
  medium,
  @JsonValue('hard')
  hard,
  @JsonValue('extreme')
  extreme,
}

/// Quest status tracking
enum QuestStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
}

/// Quest category enumeration
enum QuestCategory {
  @JsonValue('tutorial')
  tutorial,
  @JsonValue('adventure')
  adventure,
  @JsonValue('challenge')
  challenge,
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
  @JsonValue('special')
  special,
}

/// Main quest model
@JsonSerializable()
class Quest extends Equatable {
  final String id;
  final String title;
  final String description;
  final QuestDifficulty difficulty;
  final QuestStatus status;
  final QuestCategory category;
  final int xpReward;
  final int? coinReward;
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? expiresAt;
  final String? assignedUserId;
  final List<String> requirements;
  final Map<String, dynamic>? metadata;
  final bool isActive;

  const Quest({
    required this.id,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.status,
    required this.category,
    required this.xpReward,
    this.coinReward,
    required this.createdAt,
    this.startedAt,
    this.completedAt,
    this.expiresAt,
    this.assignedUserId,
    this.requirements = const [],
    this.metadata,
    this.isActive = true,
  });

  /// Create quest from JSON
  factory Quest.fromJson(Map<String, dynamic> json) => _$QuestFromJson(json);

  /// Convert quest to JSON
  Map<String, dynamic> toJson() => _$QuestToJson(this);

  /// Factory constructor for creating a new quest
  factory Quest.create({
    required String id,
    required String title,
    required String description,
    required QuestDifficulty difficulty,
    required QuestCategory category,
    required int xpReward,
    int? coinReward,
    List<String> requirements = const [],
    DateTime? expiresAt,
    Map<String, dynamic>? metadata,
  }) {
    return Quest(
      id: id,
      title: title,
      description: description,
      difficulty: difficulty,
      status: QuestStatus.pending,
      category: category,
      xpReward: xpReward,
      coinReward: coinReward,
      createdAt: DateTime.now(),
      requirements: requirements,
      expiresAt: expiresAt,
      metadata: metadata,
    );
  }

  /// Create a copy of the quest with updated fields
  Quest copyWith({
    String? id,
    String? title,
    String? description,
    QuestDifficulty? difficulty,
    QuestStatus? status,
    QuestCategory? category,
    int? xpReward,
    int? coinReward,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? expiresAt,
    String? assignedUserId,
    List<String>? requirements,
    Map<String, dynamic>? metadata,
    bool? isActive,
  }) {
    return Quest(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      difficulty: difficulty ?? this.difficulty,
      status: status ?? this.status,
      category: category ?? this.category,
      xpReward: xpReward ?? this.xpReward,
      coinReward: coinReward ?? this.coinReward,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      assignedUserId: assignedUserId ?? this.assignedUserId,
      requirements: requirements ?? this.requirements,
      metadata: metadata ?? this.metadata,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Utility methods
  bool get isCompleted => status == QuestStatus.completed;
  bool get isInProgress => status == QuestStatus.inProgress;
  bool get isPending => status == QuestStatus.pending;
  bool get isFailed => status == QuestStatus.failed;
  bool get isCancelled => status == QuestStatus.cancelled;
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  /// Get difficulty multiplier for XP calculation
  double get difficultyMultiplier {
    switch (difficulty) {
      case QuestDifficulty.easy:
        return 1.0;
      case QuestDifficulty.medium:
        return 1.5;
      case QuestDifficulty.hard:
        return 2.0;
      case QuestDifficulty.extreme:
        return 3.0;
    }
  }

  /// Calculate total XP reward with difficulty multiplier
  int get totalXpReward => (xpReward * difficultyMultiplier).round();

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        difficulty,
        status,
        category,
        xpReward,
        coinReward,
        createdAt,
        startedAt,
        completedAt,
        expiresAt,
        assignedUserId,
        requirements,
        metadata,
        isActive,
      ];
}

/// Quest completion request model
@JsonSerializable()
class QuestCompletionRequest extends Equatable {
  final String questId;
  final String userId;
  final Map<String, dynamic>? completionData;
  final DateTime completedAt;

  const QuestCompletionRequest({
    required this.questId,
    required this.userId,
    this.completionData,
    required this.completedAt,
  });

  factory QuestCompletionRequest.fromJson(Map<String, dynamic> json) =>
      _$QuestCompletionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$QuestCompletionRequestToJson(this);

  @override
  List<Object?> get props => [questId, userId, completionData, completedAt];
}
