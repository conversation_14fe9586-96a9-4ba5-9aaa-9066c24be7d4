import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

import '../utils/responsive_utils.dart';
import '../theme/design_tokens.dart';

/// Universal Adaptive Layout System for Quester
/// 
/// Provides comprehensive responsive layout components with:
/// - Adaptive navigation (drawer → rail → extended rail)
/// - Responsive grids and containers
/// - Universal breakpoint handling
/// - Cross-platform optimization
class AdaptiveLayout extends StatelessWidget {
  final Widget child;
  final Widget? drawer;
  final Widget? endDrawer;
  final List<NavigationDestination>? destinations;
  final int? selectedIndex;
  final ValueChanged<int>? onDestinationSelected;
  final Widget? floatingActionButton;
  final PreferredSizeWidget? appBar;

  const AdaptiveLayout({
    super.key,
    required this.child,
    this.drawer,
    this.endDrawer,
    this.destinations,
    this.selectedIndex,
    this.onDestinationSelected,
    this.floatingActionButton,
    this.appBar,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveUtils.responsive(
      context: context,
      xs: _buildMobileLayout(context),
      sm: _buildMobileLayout(context),
      md: _buildTabletLayout(context),
      lg: _buildTabletLayout(context),
      xl: _buildDesktopLayout(context),
      xxl: _buildDesktopLayout(context),
      fallback: _buildMobileLayout(context),
    );
  }

  /// Mobile layout with bottom navigation
  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      drawer: drawer,
      endDrawer: endDrawer,
      body: child,
      bottomNavigationBar: destinations != null
          ? _buildBottomNavigation(context)
          : null,
      floatingActionButton: floatingActionButton,
    );
  }

  /// Tablet layout with navigation rail
  Widget _buildTabletLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      endDrawer: endDrawer,
      body: Row(
        children: [
          if (destinations != null) _buildNavigationRail(context, false),
          Expanded(child: child),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  /// Desktop layout with extended navigation rail
  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      appBar: appBar,
      endDrawer: endDrawer,
      body: Row(
        children: [
          if (destinations != null) _buildNavigationRail(context, true),
          Expanded(child: child),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  /// Build bottom navigation for mobile
  Widget _buildBottomNavigation(BuildContext context) {
    return NavigationBar(
      selectedIndex: selectedIndex ?? 0,
      onDestinationSelected: onDestinationSelected,
      destinations: destinations!,
      height: DesignTokens.bottomNavHeight,
      elevation: DesignTokens.elevationLevel2,
    );
  }

  /// Build navigation rail for tablet/desktop
  Widget _buildNavigationRail(BuildContext context, bool extended) {
    return NavigationRail(
      selectedIndex: selectedIndex ?? 0,
      onDestinationSelected: onDestinationSelected,
      destinations: destinations!
          .map((dest) => NavigationRailDestination(
                icon: dest.icon,
                selectedIcon: dest.selectedIcon,
                label: Text(dest.label),
              ))
          .toList(),
      extended: extended,
      minWidth: DesignTokens.navigationRailWidth,
      minExtendedWidth: DesignTokens.navigationRailWidthExpanded,
      elevation: DesignTokens.elevationLevel1,
    );
  }
}

/// Responsive Grid Layout
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;
  final int? forceColumns;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = DesignTokens.spaceMedium,
    this.runSpacing = DesignTokens.spaceMedium,
    this.padding,
    this.forceColumns,
  });

  @override
  Widget build(BuildContext context) {
    final columns = forceColumns ?? ResponsiveUtils.getResponsiveGridColumns(context);
    
    return Padding(
      padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final itemWidth = (constraints.maxWidth - (spacing * (columns - 1))) / columns;
          
          return Wrap(
            spacing: spacing,
            runSpacing: runSpacing,
            children: children.map((child) {
              return SizedBox(
                width: itemWidth,
                child: child,
              );
            }).toList(),
          );
        },
      ),
    );
  }
}

/// Responsive Container with max width constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;
  final bool centerContent;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
    this.centerContent = true,
    this.backgroundColor,
    this.borderRadius,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      width: double.infinity,
      padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
      margin: margin ?? ResponsiveUtils.getResponsiveMargin(context),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
      ),
      child: centerContent
          ? Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: maxWidth ?? ResponsiveUtils.getMaxContentWidth(context),
                ),
                child: child,
              ),
            )
          : child,
    );

    return content;
  }
}

/// Responsive Card with adaptive sizing
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? EdgeInsets.all(
      ResponsiveUtils.getResponsiveValue(
        context,
        xs: DesignTokens.spaceMedium,
        sm: DesignTokens.spaceMedium,
        md: DesignTokens.spaceLarge,
        lg: DesignTokens.spaceLarge,
        xl: DesignTokens.spaceXLarge,
        xxl: DesignTokens.spaceXLarge,
        fallback: DesignTokens.spaceMedium,
      ),
    );

    final responsiveElevation = elevation ?? ResponsiveUtils.getResponsiveValue(
      context,
      xs: DesignTokens.elevationLevel1,
      sm: DesignTokens.elevationLevel1,
      md: DesignTokens.elevationLevel2,
      lg: DesignTokens.elevationLevel2,
      xl: DesignTokens.elevationLevel3,
      xxl: DesignTokens.elevationLevel3,
      fallback: DesignTokens.elevationLevel1,
    );

    final responsiveBorderRadius = borderRadius ?? BorderRadius.circular(
      ResponsiveUtils.getResponsiveValue(
        context,
        xs: DesignTokens.radiusSmall,
        sm: DesignTokens.radiusSmall,
        md: DesignTokens.radiusMedium,
        lg: DesignTokens.radiusMedium,
        xl: DesignTokens.radiusLarge,
        xxl: DesignTokens.radiusLarge,
        fallback: DesignTokens.radiusSmall,
      ),
    );

    return Card(
      margin: margin ?? ResponsiveUtils.getResponsiveMargin(context),
      color: backgroundColor,
      elevation: responsiveElevation,
      shape: RoundedRectangleBorder(borderRadius: responsiveBorderRadius),
      child: InkWell(
        onTap: onTap,
        borderRadius: responsiveBorderRadius,
        child: Padding(
          padding: responsivePadding,
          child: child,
        ),
      ),
    );
  }
}

/// Responsive Spacing Widget
class ResponsiveSpacing extends StatelessWidget {
  final double? xs, sm, md, lg, xl, xxl;
  final bool isVertical;

  const ResponsiveSpacing({
    super.key,
    this.xs,
    this.sm,
    this.md,
    this.lg,
    this.xl,
    this.xxl,
    this.isVertical = true,
  });

  const ResponsiveSpacing.horizontal({
    super.key,
    this.xs,
    this.sm,
    this.md,
    this.lg,
    this.xl,
    this.xxl,
  }) : isVertical = false;

  @override
  Widget build(BuildContext context) {
    final spacing = ResponsiveUtils.getResponsiveValue(
      context,
      xs: xs,
      sm: sm,
      md: md,
      lg: lg,
      xl: xl,
      xxl: xxl,
      fallback: DesignTokens.spaceMedium,
    );

    return SizedBox(
      width: isVertical ? null : spacing,
      height: isVertical ? spacing : null,
    );
  }
}
