import 'package:flutter/material.dart';
import 'package:shared/shared.dart';

/// Universal Responsive Breakpoints for Material 3 Design System
/// 
/// Implements comprehensive breakpoint system with:
/// - Material 3 standard breakpoints
/// - Responsive utilities and helpers
/// - Adaptive layout support
/// - Cross-platform optimization
class AppBreakpoints {
  // Material 3 Standard Breakpoints
  static const double xs = 0;      // Mobile portrait (0-575px)
  static const double sm = 576;    // Mobile landscape (576-767px)
  static const double md = 768;    // Tablet portrait (768-991px)
  static const double lg = 992;    // Tablet landscape / Small desktop (992-1199px)
  static const double xl = 1200;   // Desktop (1200-1399px)
  static const double xxl = 1400;  // Large desktop (1400px+)
  
  // Legacy breakpoints for compatibility
  static const double mobile = md;
  static const double tablet = lg;
  static const double desktop = xl;
}

/// Material 3 Design Token System - Spacing
class AppSpacing {
  // Material 3 Spacing Scale
  static const double xs = 4.0;    // Extra small
  static const double sm = 8.0;    // Small
  static const double md = 16.0;   // Medium (base)
  static const double lg = 24.0;   // Large
  static const double xl = 32.0;   // Extra large
  static const double xxl = 48.0;  // Extra extra large
  static const double xxxl = 64.0; // Extra extra extra large
  
  // Semantic spacing
  static const double none = 0.0;
  static const double tiny = 2.0;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double huge = xxl;
}

/// Material 3 Design Token System - Border Radius
class AppRadius {
  static const double none = 0.0;
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 12.0;
  static const double lg = 16.0;
  static const double xl = 24.0;
  static const double xxl = 32.0;
  static const double full = 999.0;
}

/// Material 3 Design Token System - Elevation
class AppElevation {
  static const double level0 = 0.0;
  static const double level1 = 1.0;
  static const double level2 = 3.0;
  static const double level3 = 6.0;
  static const double level4 = 8.0;
  static const double level5 = 12.0;
}

/// Screen Size Enumeration
enum ScreenSize {
  xs,      // Extra small (mobile portrait)
  sm,      // Small (mobile landscape)
  md,      // Medium (tablet portrait)
  lg,      // Large (tablet landscape)
  xl,      // Extra large (desktop)
  xxl,     // Extra extra large (large desktop)
}

/// Universal Responsive Utilities
class ResponsiveUtils {
  /// Get current screen size based on width
  static ScreenSize getScreenSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < AppBreakpoints.sm) {
      return ScreenSize.xs;
    } else if (width < AppBreakpoints.md) {
      return ScreenSize.sm;
    } else if (width < AppBreakpoints.lg) {
      return ScreenSize.md;
    } else if (width < AppBreakpoints.xl) {
      return ScreenSize.lg;
    } else if (width < AppBreakpoints.xxl) {
      return ScreenSize.xl;
    } else {
      return ScreenSize.xxl;
    }
  }
  
  /// Check if current screen is mobile (xs or sm)
  static bool isMobile(BuildContext context) {
    final screenSize = getScreenSize(context);
    return screenSize == ScreenSize.xs || screenSize == ScreenSize.sm;
  }
  
  /// Check if current screen is tablet (md or lg)
  static bool isTablet(BuildContext context) {
    final screenSize = getScreenSize(context);
    return screenSize == ScreenSize.md || screenSize == ScreenSize.lg;
  }
  
  /// Check if current screen is desktop (xl or xxl)
  static bool isDesktop(BuildContext context) {
    final screenSize = getScreenSize(context);
    return screenSize == ScreenSize.xl || screenSize == ScreenSize.xxl;
  }
  
  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    T? xs,
    T? sm,
    T? md,
    T? lg,
    T? xl,
    T? xxl,
    required T fallback,
  }) {
    final screenSize = getScreenSize(context);
    
    switch (screenSize) {
      case ScreenSize.xs:
        return xs ?? fallback;
      case ScreenSize.sm:
        return sm ?? xs ?? fallback;
      case ScreenSize.md:
        return md ?? sm ?? xs ?? fallback;
      case ScreenSize.lg:
        return lg ?? md ?? sm ?? xs ?? fallback;
      case ScreenSize.xl:
        return xl ?? lg ?? md ?? sm ?? xs ?? fallback;
      case ScreenSize.xxl:
        return xxl ?? xl ?? lg ?? md ?? sm ?? xs ?? fallback;
    }
  }
  
  /// Get responsive grid columns
  static int getResponsiveGridColumns(BuildContext context) {
    return getResponsiveValue(
      context,
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 4,
      xxl: 5,
      fallback: 1,
    );
  }
  
  /// Get responsive padding
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return EdgeInsets.all(
      getResponsiveValue(
        context,
        xs: AppSpacing.md,
        sm: AppSpacing.md,
        md: AppSpacing.lg,
        lg: AppSpacing.xl,
        xl: AppSpacing.xl,
        xxl: AppSpacing.xxl,
        fallback: AppSpacing.md,
      ),
    );
  }
  
  /// Get responsive margin
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    return EdgeInsets.all(
      getResponsiveValue(
        context,
        xs: AppSpacing.sm,
        sm: AppSpacing.sm,
        md: AppSpacing.md,
        lg: AppSpacing.lg,
        xl: AppSpacing.lg,
        xxl: AppSpacing.xl,
        fallback: AppSpacing.sm,
      ),
    );
  }
  
  /// Get max content width for responsive layouts
  static double getMaxContentWidth(BuildContext context) {
    return getResponsiveValue(
      context,
      xs: double.infinity,
      sm: double.infinity,
      md: 768,
      lg: 992,
      xl: 1200,
      xxl: 1400,
      fallback: double.infinity,
    );
  }
  
  /// Get responsive font scale
  static double getResponsiveFontScale(BuildContext context) {
    return getResponsiveValue(
      context,
      xs: 1.0,
      sm: 1.0,
      md: 1.05,
      lg: 1.1,
      xl: 1.15,
      xxl: 1.2,
      fallback: 1.0,
    );
  }
  
  /// Responsive widget builder
  static Widget responsive({
    required BuildContext context,
    Widget? xs,
    Widget? sm,
    Widget? md,
    Widget? lg,
    Widget? xl,
    Widget? xxl,
    Widget? fallback,
  }) {
    final screenSize = getScreenSize(context);
    
    Widget? widget;
    switch (screenSize) {
      case ScreenSize.xs:
        widget = xs;
        break;
      case ScreenSize.sm:
        widget = sm ?? xs;
        break;
      case ScreenSize.md:
        widget = md ?? sm ?? xs;
        break;
      case ScreenSize.lg:
        widget = lg ?? md ?? sm ?? xs;
        break;
      case ScreenSize.xl:
        widget = xl ?? lg ?? md ?? sm ?? xs;
        break;
      case ScreenSize.xxl:
        widget = xxl ?? xl ?? lg ?? md ?? sm ?? xs;
        break;
    }
    
    return widget ?? fallback ?? const SizedBox.shrink();
  }
}

/// Responsive Container with max width constraints
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;
  
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
      margin: margin ?? ResponsiveUtils.getResponsiveMargin(context),
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? ResponsiveUtils.getMaxContentWidth(context),
          ),
          child: child,
        ),
      ),
    );
  }
}
