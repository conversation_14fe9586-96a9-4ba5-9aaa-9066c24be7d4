# Quester Shared Package Documentation

The Quester shared package is a **comprehensive, type-safe library** that provides common data models, utilities, services, and constants shared between the Flutter client and Dart server. Built with modern Dart 3.8+ features, JSON serialization, and production-ready patterns.

## 📦 Package Overview

### ✨ Current Implementation Status (June 2025)

**✅ Production-Ready Features:**
- **Complete Data Model System** with JSON serialization support
- **Type-Safe API Response Framework** with generic support
- **Comprehensive User Management Models** with authentication support
- **Advanced Quest System Models** with status tracking and difficulty levels
- **Real-time Notification Models** for WebSocket communication
- **Dashboard Analytics Models** for data visualization
- **Shared Service Interfaces** for consistent business logic
- **Utility Functions** and helper classes
- **Exception Handling Framework** with custom error types

**🎯 Key Metrics:**
- **1,000+ lines** of production Dart code
- **Complete JSON serialization** with generated code
- **Zero compilation errors** - full null-safety compliance
- **Type-safe data models** with comprehensive validation
- **Cross-platform compatibility** (Flutter & Dart server)

## 🏗️ Architecture & Structure

### 📁 Package Organization

```
shared/lib/
├── shared.dart                         # Main library export file
└── src/
    ├── models/                         # Data models with JSON serialization
    │   ├── models.dart                 # Model exports
    │   ├── user.dart                   # User authentication models
    │   ├── quest.dart                  # Quest system models
    │   ├── api_response.dart           # API response wrapper
    │   ├── notification.dart           # Real-time notification models
    │   ├── dashboard.dart              # Dashboard analytics models
    │   └── *.g.dart                    # Generated JSON serialization
    ├── services/                       # Shared business logic
    │   ├── services.dart               # Service exports
    │   ├── validation_service.dart     # Input validation utilities
    │   └── websocket_service.dart      # WebSocket communication
    ├── utils/                          # Utility functions
    │   ├── utils.dart                  # Utility exports
    │   ├── date_utils.dart             # Date/time helpers
    │   └── string_utils.dart           # String manipulation
    ├── constants/                      # Application constants
    │   ├── constants.dart              # Constant exports
    │   ├── api_constants.dart          # API endpoints and configs
    │   └── app_constants.dart          # Application-wide constants
    └── exceptions/                     # Custom exception types
        ├── exceptions.dart             # Exception exports
        ├── api_exception.dart          # API-related exceptions
        └── validation_exception.dart   # Validation exceptions
```

## 📊 Data Models

### **User Management Models**

#### **User Model**
```dart
@JsonSerializable()
class User {
  final String id;
  final String username;
  final String email;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  
  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.createdAt,
    this.lastLoginAt,
  });
  
  // JSON serialization
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
  
  // Copy with method for immutable updates
  User copyWith({
    String? id,
    String? username,
    String? email,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}
```

#### **User Authentication Models**
```dart
// Login request model
@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;
  final bool rememberMe;
  
  const LoginRequest({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });
}

// Registration request model
@JsonSerializable()
class RegisterRequest {
  final String username;
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  
  const RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
  });
}
```

### **Quest System Models**

#### **Quest Model**
```dart
// Quest difficulty levels
enum QuestDifficulty {
  @JsonValue('easy')
  easy,
  @JsonValue('medium')
  medium,
  @JsonValue('hard')
  hard,
  @JsonValue('extreme')
  extreme,
}

// Quest status tracking
enum QuestStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
}

@JsonSerializable()
class Quest {
  final String id;
  final String title;
  final String description;
  final QuestDifficulty difficulty;
  final QuestStatus status;
  final int rewardPoints;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? assignedUserId;
  
  const Quest({
    required this.id,
    required this.title,
    required this.description,
    required this.difficulty,
    required this.status,
    required this.rewardPoints,
    required this.createdAt,
    this.completedAt,
    this.assignedUserId,
  });
  
  // Factory constructors for common quest states
  factory Quest.create({
    required String id,
    required String title,
    required String description,
    required QuestDifficulty difficulty,
    required int rewardPoints,
  }) {
    return Quest(
      id: id,
      title: title,
      description: description,
      difficulty: difficulty,
      status: QuestStatus.pending,
      rewardPoints: rewardPoints,
      createdAt: DateTime.now(),
    );
  }
  
  // Utility methods
  bool get isCompleted => status == QuestStatus.completed;
  bool get isInProgress => status == QuestStatus.inProgress;
  bool get isPending => status == QuestStatus.pending;
}
```

### **API Response Framework**

#### **Generic API Response**
```dart
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;
  
  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });
  
  // Success response factory
  factory ApiResponse.success({
    required T data,
    String? message,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
    );
  }
  
  // Error response factory
  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? errors,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
    );
  }
  
  // Type-safe JSON serialization
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);
  
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}
```

### **Notification System Models**

#### **Real-time Notification Model**
```dart
enum NotificationType {
  @JsonValue('info')
  info,
  @JsonValue('success')
  success,
  @JsonValue('warning')
  warning,
  @JsonValue('error')
  error,
  @JsonValue('quest_complete')
  questComplete,
  @JsonValue('achievement_unlocked')
  achievementUnlocked,
}

@JsonSerializable()
class Notification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final String? userId;
  final Map<String, dynamic>? metadata;
  
  const Notification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.userId,
    this.metadata,
  });
  
  // Factory for quest completion notifications
  factory Notification.questComplete({
    required String id,
    required String questTitle,
    required int xpReward,
    required String userId,
  }) {
    return Notification(
      id: id,
      title: 'Quest Complete!',
      message: 'You completed "$questTitle" and earned $xpReward XP!',
      type: NotificationType.questComplete,
      createdAt: DateTime.now(),
      userId: userId,
      metadata: {
        'questTitle': questTitle,
        'xpReward': xpReward,
      },
    );
  }
  
  // Mark as read
  Notification markAsRead() {
    return copyWith(isRead: true);
  }
}
```

### **Dashboard Analytics Models**

#### **Dashboard Statistics**
```dart
@JsonSerializable()
class DashboardStats {
  final int totalUsers;
  final int activeQuests;
  final int completedQuests;
  final int totalXpEarned;
  final double averageQuestCompletion;
  final DateTime lastUpdated;
  
  const DashboardStats({
    required this.totalUsers,
    required this.activeQuests,
    required this.completedQuests,
    required this.totalXpEarned,
    required this.averageQuestCompletion,
    required this.lastUpdated,
  });
  
  factory DashboardStats.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatsFromJson(json);
  Map<String, dynamic> toJson() => _$DashboardStatsToJson(this);
}

@JsonSerializable()
class UserActivity {
  final String userId;
  final String username;
  final String action;
  final DateTime timestamp;
  final Map<String, dynamic>? details;
  
  const UserActivity({
    required this.userId,
    required this.username,
    required this.action,
    required this.timestamp,
    this.details,
  });
  
  factory UserActivity.fromJson(Map<String, dynamic> json) =>
      _$UserActivityFromJson(json);
  Map<String, dynamic> toJson() => _$UserActivityToJson(this);
}
```

## 🔧 Services & Business Logic

### **Validation Service**
```dart
class ValidationService {
  // Email validation
  static bool isValidEmail(String email) {
    final regex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return regex.hasMatch(email);
  }
  
  // Username validation
  static bool isValidUsername(String username) {
    if (username.length < 3 || username.length > 20) return false;
    final regex = RegExp(r'^[a-zA-Z0-9_]+$');
    return regex.hasMatch(username);
  }
  
  // Password strength validation
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasNumbers = password.contains(RegExp(r'[0-9]'));
    final hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasNumbers && hasSpecialChars;
  }
  
  // Quest validation
  static bool isValidQuest(Quest quest) {
    if (quest.title.trim().isEmpty) return false;
    if (quest.description.trim().isEmpty) return false;
    if (quest.rewardPoints <= 0) return false;
    return true;
  }
}
```

### **WebSocket Service Interface**
```dart
abstract class WebSocketService {
  // Connection management
  void connect(String url);
  void disconnect();
  bool get isConnected;
  
  // Message handling
  void sendMessage(Map<String, dynamic> message);
  Stream<Map<String, dynamic>> get messageStream;
  
  // Event subscriptions
  void subscribe(String event, Function(Map<String, dynamic>) callback);
  void unsubscribe(String event);
  
  // Error handling
  Stream<String> get errorStream;
}
```

## 🛠️ Utility Functions

### **Date Utilities**
```dart
class DateUtils {
  // Format date for display
  static String formatDisplayDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
  
  // Time ago formatting
  static String timeAgo(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return formatDisplayDate(date);
    }
  }
  
  // Date range validation
  static bool isDateInRange(DateTime date, DateTime start, DateTime end) {
    return date.isAfter(start) && date.isBefore(end);
  }
}
```

### **String Utilities**
```dart
class StringUtils {
  // Capitalize first letter
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }
  
  // Generate random ID
  static String generateId([int length = 8]) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(length, (index) => 
        chars[Random().nextInt(chars.length)]).join();
  }
  
  // Truncate text with ellipsis
  static String truncate(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }
  
  // Remove HTML tags
  static String stripHtml(String htmlText) {
    final regex = RegExp(r'<[^>]*>');
    return htmlText.replaceAll(regex, '');
  }
}
```

## 🔧 Constants & Configuration

### **API Constants**
```dart
class ApiConstants {
  // Base URLs
  static const String baseUrl = 'http://localhost:3000/api';
  static const String wsBaseUrl = 'ws://localhost:8080/ws';
  
  // Endpoints
  static const String authRegister = '/auth/register';
  static const String authLogin = '/auth/login';
  static const String authRefresh = '/auth/refresh';
  static const String users = '/users';
  static const String quests = '/quests';
  static const String notifications = '/notifications';
  
  // Headers
  static const String contentTypeJson = 'application/json';
  static const String authorizationHeader = 'Authorization';
  static const String bearerPrefix = 'Bearer ';
  
  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
}
```

### **Application Constants**
```dart
class AppConstants {
  // App information
  static const String appName = 'Quester';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Adventure Platform';
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation limits
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  
  // Quest limits
  static const int minQuestTitleLength = 5;
  static const int maxQuestTitleLength = 100;
  static const int maxQuestDescriptionLength = 1000;
  
  // XP and levels
  static const int baseXpPerLevel = 1000;
  static const double xpMultiplier = 1.5;
  static const int maxLevel = 100;
}
```

## 🚨 Exception Handling

### **Custom Exception Framework**
```dart
// Base API exception
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;
  
  const ApiException({
    required this.message,
    this.statusCode,
    this.errors,
  });
  
  @override
  String toString() => 'ApiException: $message';
}

// Validation exception
class ValidationException extends ApiException {
  final Map<String, String> fieldErrors;
  
  const ValidationException({
    required String message,
    required this.fieldErrors,
  }) : super(message: message, statusCode: 400);
  
  factory ValidationException.fromFields(Map<String, String> fields) {
    return ValidationException(
      message: 'Validation failed',
      fieldErrors: fields,
    );
  }
}

// Authentication exception
class AuthenticationException extends ApiException {
  const AuthenticationException({
    String message = 'Authentication failed',
  }) : super(message: message, statusCode: 401);
}

// Authorization exception
class AuthorizationException extends ApiException {
  const AuthorizationException({
    String message = 'Access denied',
  }) : super(message: message, statusCode: 403);
}
```

## 🔧 Technical Specifications

### **Dependencies & Requirements**

#### **Core Dependencies**
```yaml
dependencies:
  # JSON serialization
  json_annotation: ^4.9.0        # JSON annotation support
  
  # HTTP and networking
  http: ^1.1.0                   # HTTP client
  web_socket_channel: ^2.4.5     # WebSocket communication
  
  # Utilities
  meta: ^1.9.1                   # Metadata annotations
  equatable: ^2.0.5              # Value equality support
  crypto: ^3.0.6                 # Cryptographic functions
```

#### **Build Dependencies**
```yaml
dev_dependencies:
  # Code generation
  build_runner: ^2.4.7           # Build system
  json_serializable: ^6.7.1      # JSON serialization generator
  
  # Code quality
  lints: ^5.0.0                  # Dart linting rules
  test: ^1.24.0                  # Testing framework
```

### **Code Generation**
```bash
# Using setup script
./setup.sh setup-shared

# Manual code generation
dart run build_runner build

# Watch for changes and rebuild
dart run build_runner watch

# Clean generated files
dart run build_runner clean
```

## 🚀 Getting Started

### **Quick Setup with Script**
```bash
# Using setup script (recommended)
./setup.sh setup-shared

# This will:
# - Install dependencies
# - Run code generation
# - Execute tests
# - Perform code analysis
```

### **Manual Installation**
```yaml
# Add to pubspec.yaml
dependencies:
  shared:
    path: ../shared  # Local path dependency
```

```bash
# Navigate to shared directory
cd shared

# Install dependencies
dart pub get

# Run code generation
dart run build_runner build --delete-conflicting-outputs

# Run tests
dart test

# Analyze code
dart analyze
```

### **Usage Examples**

#### **Basic Model Usage**
```dart
import 'package:shared/shared.dart';

// Create a user
final user = User(
  id: '1',
  username: 'johndoe',
  email: '<EMAIL>',
  createdAt: DateTime.now(),
);

// Serialize to JSON
final json = user.toJson();

// Deserialize from JSON
final userFromJson = User.fromJson(json);

// Update user with copyWith
final updatedUser = user.copyWith(
  lastLoginAt: DateTime.now(),
);
```

#### **API Response Handling**
```dart
// Success response
final successResponse = ApiResponse<User>.success(
  data: user,
  message: 'User retrieved successfully',
);

// Error response
final errorResponse = ApiResponse<User>.error(
  message: 'User not found',
  errors: {'id': 'Invalid user ID'},
);

// Check response status
if (response.success) {
  final userData = response.data!;
  print('User: ${userData.username}');
} else {
  print('Error: ${response.message}');
}
```

#### **Validation Usage**
```dart
// Validate email
if (!ValidationService.isValidEmail(email)) {
  throw ValidationException.fromFields({
    'email': 'Invalid email format'
  });
}

// Validate password strength
if (!ValidationService.isStrongPassword(password)) {
  throw ValidationException.fromFields({
    'password': 'Password must be at least 8 characters with mixed case, numbers, and symbols'
  });
}
```

## 🧪 Testing

### **Test Structure**
```dart
import 'package:test/test.dart';
import 'package:shared/shared.dart';

void main() {
  group('User Model Tests', () {
    test('should create user from JSON', () {
      final json = {
        'id': '1',
        'username': 'testuser',
        'email': '<EMAIL>',
        'createdAt': '2025-06-30T10:00:00.000Z',
      };
      
      final user = User.fromJson(json);
      
      expect(user.id, equals('1'));
      expect(user.username, equals('testuser'));
      expect(user.email, equals('<EMAIL>'));
    });
    
    test('should serialize user to JSON', () {
      final user = User(
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        createdAt: DateTime.parse('2025-06-30T10:00:00.000Z'),
      );
      
      final json = user.toJson();
      
      expect(json['id'], equals('1'));
      expect(json['username'], equals('testuser'));
      expect(json['email'], equals('<EMAIL>'));
    });
  });
  
  group('Validation Service Tests', () {
    test('should validate email format', () {
      expect(ValidationService.isValidEmail('<EMAIL>'), isTrue);
      expect(ValidationService.isValidEmail('invalid-email'), isFalse);
    });
    
    test('should validate password strength', () {
      expect(ValidationService.isStrongPassword('StrongPass123!'), isTrue);
      expect(ValidationService.isStrongPassword('weak'), isFalse);
    });
  });
}
```

### **Running Tests**
```bash
# Run all tests
dart test

# Run tests with coverage
dart test --coverage=coverage

# Run specific test file
dart test test/models/user_test.dart
```

## 🔗 Integration Examples

### **Client Integration (Flutter)**
```dart
// In Flutter app
import 'package:shared/shared.dart';

class ApiService {
  Future<ApiResponse<User>> getUser(String id) async {
    final response = await http.get(
      Uri.parse('${ApiConstants.baseUrl}${ApiConstants.users}/$id'),
    );
    
    final json = jsonDecode(response.body);
    return ApiResponse<User>.fromJson(
      json,
      (data) => User.fromJson(data as Map<String, dynamic>),
    );
  }
}
```

### **Server Integration (Dart)**
```dart
// In Dart server
import 'package:shared/shared.dart';

Response getUserHandler(Request request) {
  final userId = request.params['id'];
  final user = findUserById(userId);
  
  if (user == null) {
    final response = ApiResponse<User>.error(
      message: 'User not found',
    );
    return Response.notFound(jsonEncode(response.toJson((data) => data)));
  }
  
  final response = ApiResponse<User>.success(
    data: user,
    message: 'User retrieved successfully',
  );
  
  return Response.ok(jsonEncode(response.toJson((data) => data.toJson())));
}
```

## 📖 Best Practices

### **Model Design**
- **Immutability**: Use `const` constructors and `copyWith` methods
- **JSON Serialization**: Always implement `fromJson` and `toJson`
- **Validation**: Include validation logic in model factories
- **Documentation**: Document all public APIs with comprehensive comments

### **Error Handling**
- **Consistent Exceptions**: Use custom exception types for different error categories
- **Detailed Messages**: Provide clear, actionable error messages
- **Error Codes**: Include error codes for programmatic handling
- **Context**: Include relevant context in exception details

### **Type Safety**
- **Null Safety**: Leverage Dart's null safety features
- **Generic Types**: Use generics for reusable components
- **Enums**: Use enums for fixed sets of values
- **Validation**: Validate all inputs at boundaries

## 📚 Additional Resources

### **Documentation Links**
- **[JSON Annotation](https://pub.dev/packages/json_annotation)**
- **[Build Runner](https://pub.dev/packages/build_runner)**
- **[Equatable](https://pub.dev/packages/equatable)**
- **[Dart Testing](https://dart.dev/guides/testing)**

### **Code Generation**
- **[JSON Serializable](https://pub.dev/packages/json_serializable)**
- **[Build System](https://github.com/dart-lang/build)**

---

**Last Updated**: June 30, 2025  
**Dart Version**: 3.8.1+  
**Package Version**: 1.0.0  
**Status**: ✅ Production Ready

### **Creating a New Shared Package from Scratch**

The Quester platform provides scaffolding capabilities to create new shared Dart packages with comprehensive utilities and models:

#### **Using Setup Script (Recommended)**
```bash
# Linux/macOS
./setup.sh create-shared my_shared_lib

# Windows
setup.bat create-shared my_shared_lib
```

#### **What Gets Created**
When you create a new shared package, you get:
- ✅ **Complete Dart package** with proper library structure
- ✅ **Pre-built models** with JSON serialization and equality support
- ✅ **Utility functions** for common operations (strings, dates, validation)
- ✅ **Constants management** for app and API configuration
- ✅ **Abstract service interfaces** for consistent architecture
- ✅ **Code generation setup** with build_runner and json_serializable
- ✅ **Comprehensive test suite** with 100% test coverage examples
- ✅ **Analysis options** with Dart linting rules and best practices
- ✅ **Ready to use** - immediately functional with proper exports

#### **Generated Project Structure**
```
my_shared_lib/
├── lib/
│   ├── my_shared_lib.dart          # Main library export file
│   └── src/
│       ├── models/
│       │   ├── models.dart         # Models export file
│       │   ├── user.dart           # User model with JSON serialization
│       │   └── quest.dart          # Quest model with status enum
│       ├── utils/
│       │   ├── utils.dart          # Utils export file
│       │   ├── string_utils.dart   # String manipulation utilities
│       │   └── date_utils.dart     # Date formatting and calculations
│       ├── constants/
│       │   ├── constants.dart      # Constants export file
│       │   ├── app_constants.dart  # Application constants
│       │   └── api_constants.dart  # API endpoints and configuration
│       └── services/
│           ├── services.dart       # Services export file
│           ├── api_service.dart    # Abstract API service interface
│           └── websocket_service.dart # Abstract WebSocket interface
├── test/
│   └── shared_test.dart           # Comprehensive test suite
├── pubspec.yaml                   # Dependencies and metadata
└── analysis_options.yaml         # Code analysis configuration
```

#### **Integrated Dependencies**
Each new shared package comes with:
- **`json_annotation`** - JSON serialization annotations
- **`equatable`** - Value equality for models
- **`lints`** - Code quality enforcement
- **`test`** - Testing framework
- **`build_runner`** & **`json_serializable`** - Code generation

#### **Pre-built Components**
The generated package includes:

**Models:**
- **User Model** - Complete user entity with JSON serialization
- **Quest Model** - Quest entity with status enum and lifecycle management
- **Equatable Support** - Proper equality comparison for all models

**Utilities:**
- **String Utils** - Capitalization, email validation, random string generation
- **Date Utils** - Formatting, time calculations, date operations
- **Validation** - Common validation patterns and rules

**Constants:**
- **App Constants** - Application-wide configuration values
- **API Constants** - API endpoints, base URLs, and configuration
- **Environment Config** - Environment-specific settings

**Services:**
- **API Service Interface** - Abstract class for HTTP operations
- **WebSocket Service Interface** - Abstract class for real-time communication

#### **Next Steps After Creation**
1. **Customize models** - Add your specific data models and business entities
2. **Extend utilities** - Add domain-specific helper functions
3. **Define constants** - Configure your application and API settings
4. **Implement services** - Create concrete implementations of service interfaces
5. **Add to projects** - Reference the shared package in your client and server applications
