// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaderboard.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeaderboardEntry _$LeaderboardEntryFromJson(Map<String, dynamic> json) =>
    LeaderboardEntry(
      userId: json['userId'] as String,
      username: json['username'] as String,
      displayName: json['displayName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      rank: (json['rank'] as num).toInt(),
      score: (json['score'] as num).toInt(),
      level: (json['level'] as num).toInt(),
      xp: (json['xp'] as num).toInt(),
      questsCompleted: (json['questsCompleted'] as num).toInt(),
      achievements: (json['achievements'] as num).toInt(),
      lastActive: DateTime.parse(json['lastActive'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$LeaderboardEntryToJson(LeaderboardEntry instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'displayName': instance.displayName,
      'avatarUrl': instance.avatarUrl,
      'rank': instance.rank,
      'score': instance.score,
      'level': instance.level,
      'xp': instance.xp,
      'questsCompleted': instance.questsCompleted,
      'achievements': instance.achievements,
      'lastActive': instance.lastActive.toIso8601String(),
      'metadata': instance.metadata,
    };

Leaderboard _$LeaderboardFromJson(Map<String, dynamic> json) => Leaderboard(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  type: $enumDecode(_$LeaderboardTypeEnumMap, json['type']),
  timeframe: $enumDecode(_$LeaderboardTimeframeEnumMap, json['timeframe']),
  entries: (json['entries'] as List<dynamic>)
      .map((e) => LeaderboardEntry.fromJson(e as Map<String, dynamic>))
      .toList(),
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  lastUpdated: DateTime.parse(json['lastUpdated'] as String),
  totalParticipants: (json['totalParticipants'] as num).toInt(),
  currentUserEntry: json['currentUserEntry'] == null
      ? null
      : LeaderboardEntry.fromJson(
          json['currentUserEntry'] as Map<String, dynamic>,
        ),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$LeaderboardToJson(Leaderboard instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$LeaderboardTypeEnumMap[instance.type]!,
      'timeframe': _$LeaderboardTimeframeEnumMap[instance.timeframe]!,
      'entries': instance.entries,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'totalParticipants': instance.totalParticipants,
      'currentUserEntry': instance.currentUserEntry,
      'metadata': instance.metadata,
    };

const _$LeaderboardTypeEnumMap = {
  LeaderboardType.global: 'global',
  LeaderboardType.category: 'category',
  LeaderboardType.regional: 'regional',
  LeaderboardType.team: 'team',
  LeaderboardType.custom: 'custom',
};

const _$LeaderboardTimeframeEnumMap = {
  LeaderboardTimeframe.daily: 'daily',
  LeaderboardTimeframe.weekly: 'weekly',
  LeaderboardTimeframe.monthly: 'monthly',
  LeaderboardTimeframe.quarterly: 'quarterly',
  LeaderboardTimeframe.yearly: 'yearly',
  LeaderboardTimeframe.allTime: 'all_time',
};

LeaderboardStats _$LeaderboardStatsFromJson(Map<String, dynamic> json) =>
    LeaderboardStats(
      leaderboardId: json['leaderboardId'] as String,
      totalParticipants: (json['totalParticipants'] as num).toInt(),
      activeParticipants: (json['activeParticipants'] as num).toInt(),
      averageScore: (json['averageScore'] as num).toDouble(),
      highestScore: (json['highestScore'] as num).toInt(),
      lowestScore: (json['lowestScore'] as num).toInt(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      scoreDistribution: Map<String, int>.from(
        json['scoreDistribution'] as Map,
      ),
    );

Map<String, dynamic> _$LeaderboardStatsToJson(LeaderboardStats instance) =>
    <String, dynamic>{
      'leaderboardId': instance.leaderboardId,
      'totalParticipants': instance.totalParticipants,
      'activeParticipants': instance.activeParticipants,
      'averageScore': instance.averageScore,
      'highestScore': instance.highestScore,
      'lowestScore': instance.lowestScore,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'scoreDistribution': instance.scoreDistribution,
    };
