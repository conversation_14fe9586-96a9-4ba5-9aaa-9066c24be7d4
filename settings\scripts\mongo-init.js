// MongoDB Initialization Script for Quester Application
// This script creates the application database and initial indexes

// Switch to the quester database
db = db.getSiblingDB('quester');

// Create application user with read/write permissions
db.createUser({
  user: 'quester_app',
  pwd: 'quester_app_password',
  roles: [
    { role: 'readWrite', db: 'quester' },
    { role: 'dbAdmin', db: 'quester' }
  ]
});

// Create collections with initial data structure
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email', 'passwordHash', 'role', 'createdAt'],
      properties: {
        username: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 20,
          description: 'Username must be 3-20 characters'
        },        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          description: 'Must be a valid email address'
        },
        role: {
          enum: ['guest', 'user', 'moderator', 'admin', 'super_admin'],
          description: 'User role must be one of the allowed values'
        },
        level: {
          bsonType: 'int',
          minimum: 1,
          description: 'User level must be at least 1'
        },
        xp: {
          bsonType: 'int',
          minimum: 0,
          description: 'XP must be non-negative'
        }
      }
    }
  }
});

db.createCollection('quests');
db.createCollection('achievements');
db.createCollection('notifications');
db.createCollection('user_progress');
db.createCollection('leaderboards');
db.createCollection('sessions');

// Create indexes for performance optimization

// Users collection indexes
db.users.createIndex({ 'email': 1 }, { unique: true });
db.users.createIndex({ 'username': 1 }, { unique: true });
db.users.createIndex({ 'role': 1 });
db.users.createIndex({ 'level': -1 });
db.users.createIndex({ 'xp': -1 });
db.users.createIndex({ 'createdAt': 1 });
db.users.createIndex({ 'lastLoginAt': -1 });

// Quests collection indexes
db.quests.createIndex({ 'category': 1 });
db.quests.createIndex({ 'difficulty': 1 });
db.quests.createIndex({ 'isActive': 1 });
db.quests.createIndex({ 'createdAt': -1 });
db.quests.createIndex({ 'xpReward': -1 });

// Achievements collection indexes
db.achievements.createIndex({ 'category': 1 });
db.achievements.createIndex({ 'rarity': 1 });
db.achievements.createIndex({ 'isActive': 1 });

// Notifications collection indexes
db.notifications.createIndex({ 'userId': 1, 'createdAt': -1 });
db.notifications.createIndex({ 'isRead': 1 });
db.notifications.createIndex({ 'type': 1 });
db.notifications.createIndex({ 'priority': 1 });

// User progress collection indexes
db.user_progress.createIndex({ 'userId': 1, 'questId': 1 }, { unique: true });
db.user_progress.createIndex({ 'userId': 1, 'startedAt': -1 });
db.user_progress.createIndex({ 'status': 1 });
db.user_progress.createIndex({ 'completedAt': -1 });

// Leaderboards collection indexes
db.leaderboards.createIndex({ 'category': 1, 'timeframe': 1 });
db.leaderboards.createIndex({ 'userId': 1 });
db.leaderboards.createIndex({ 'score': -1 });
db.leaderboards.createIndex({ 'rank': 1 });

// Sessions collection indexes (for Redis fallback)
db.sessions.createIndex({ 'sessionId': 1 }, { unique: true });
db.sessions.createIndex({ 'userId': 1 });
db.sessions.createIndex({ 'expiresAt': 1 }, { expireAfterSeconds: 0 });

// Create compound indexes for complex queries
db.users.createIndex({ 'role': 1, 'level': -1 });
db.users.createIndex({ 'isActive': 1, 'lastLoginAt': -1 });
db.quests.createIndex({ 'category': 1, 'difficulty': 1, 'isActive': 1 });
db.notifications.createIndex({ 'userId': 1, 'isRead': 1, 'priority': 1 });

// Create text indexes for search functionality
db.users.createIndex({ 
  'username': 'text', 
  'firstName': 'text', 
  'lastName': 'text' 
});
db.quests.createIndex({ 
  'title': 'text', 
  'description': 'text', 
  'category': 'text' 
});
db.achievements.createIndex({ 
  'title': 'text', 
  'description': 'text' 
});

// Insert sample admin user (password: admin123)
try {
  db.users.insertOne({
    username: 'admin',
    email: '<EMAIL>',
    passwordHash: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj86.KCGhRlC', // bcrypt hash for 'admin123'
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin', // Changed from super_admin to admin to match enum
    level: NumberInt(1), // Explicitly cast to int
    xp: NumberInt(0), // Explicitly cast to int
    isActive: true,
    emailVerified: true,
    twoFactorEnabled: false,
    preferences: {
      theme: 'dark',
      notifications: true,
      emailUpdates: true
    },
    createdAt: new Date(),
    lastLoginAt: null
  });
  print('Successfully created admin user');
} catch (error) {
  print('Error creating admin user: ' + error.message);
}

// Insert sample quest categories
db.quest_categories.insertMany([
  { name: 'health', description: 'Health and fitness related quests', color: '#4CAF50' },
  { name: 'education', description: 'Learning and skill development', color: '#2196F3' },
  { name: 'social', description: 'Community and social activities', color: '#FF9800' },
  { name: 'creativity', description: 'Creative and artistic challenges', color: '#9C27B0' },
  { name: 'productivity', description: 'Work and productivity goals', color: '#607D8B' },
  { name: 'adventure', description: 'Exploration and adventure', color: '#795548' }
]);

// Insert sample achievement rarities
db.achievement_rarities.insertMany([
  { name: 'common', description: 'Common achievements', color: '#9E9E9E', multiplier: 1.0 },
  { name: 'uncommon', description: 'Uncommon achievements', color: '#4CAF50', multiplier: 1.5 },
  { name: 'rare', description: 'Rare achievements', color: '#2196F3', multiplier: 2.0 },
  { name: 'epic', description: 'Epic achievements', color: '#9C27B0', multiplier: 3.0 },
  { name: 'legendary', description: 'Legendary achievements', color: '#FF9800', multiplier: 5.0 }
]);

print('MongoDB initialization completed successfully!');
print('Created database: quester');
print('Created user: quester_app');
print('Created collections with indexes and sample data');
