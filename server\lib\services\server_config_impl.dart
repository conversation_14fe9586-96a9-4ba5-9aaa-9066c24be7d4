import 'dart:io';
import 'dart:convert';
import 'package:shared/shared.dart';

/// Server environment enumeration
enum ServerEnvironment {
  development,
  staging,
  production,
  testing,
}

/// Server configuration management using shared constants and environment variables
class ServerConfig {
  static ServerConfig? _instance;
  static ServerConfig get instance => _instance ??= ServerConfig._();
  
  ServerConfig._();

  // Environment detection
  ServerEnvironment get environment {
    final env = Platform.environment['NODE_ENV']?.toLowerCase() ?? 'development';
    switch (env) {
      case 'production':
        return ServerEnvironment.production;
      case 'staging':
        return ServerEnvironment.staging;
      case 'testing':
        return ServerEnvironment.testing;
      default:
        return ServerEnvironment.development;
    }
  }

  bool get isDevelopment => environment == ServerEnvironment.development;
  bool get isProduction => environment == ServerEnvironment.production;
  bool get isStaging => environment == ServerEnvironment.staging;
  bool get isTesting => environment == ServerEnvironment.testing;

  // Server configuration using shared constants
  String get appName => AppConstants.appName;
  String get appVersion => AppConstants.appVersion;
  String get appDescription => AppConstants.appDescription;

  // Server network configuration
  String get host => Platform.environment['HOST'] ?? '0.0.0.0';
  int get port => int.parse(Platform.environment['PORT'] ?? '8080');
  String get baseUrl => Platform.environment['BASE_URL'] ?? 'http://localhost:$port';

  // CORS configuration
  List<String> get allowedOrigins {
    final origins = Platform.environment['ALLOWED_ORIGINS'];
    if (origins != null) {
      return origins.split(',').map((origin) => origin.trim()).toList();
    }
    
    // Default CORS origins based on environment
    switch (environment) {
      case ServerEnvironment.production:
        return ['https://quester.app', 'https://www.quester.app'];
      case ServerEnvironment.staging:
        return ['https://staging.quester.app'];
      case ServerEnvironment.development:
        return ['http://localhost:3000', 'http://localhost:8080', 'http://127.0.0.1:3000'];
      case ServerEnvironment.testing:
        return ['http://localhost:*'];
    }
  }

  List<String> get allowedMethods => ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
  List<String> get allowedHeaders => ['Content-Type', 'Authorization', 'X-Requested-With'];

  // Database configuration
  String get databaseHost => Platform.environment['DB_HOST'] ?? 'localhost';
  int get databasePort => int.parse(Platform.environment['DB_PORT'] ?? '27017');
  String get databaseName => Platform.environment['DB_NAME'] ?? 'quester_${environment.name}';
  String? get databaseUsername => Platform.environment['DB_USERNAME'];
  String? get databasePassword => Platform.environment['DB_PASSWORD'];
  bool get databaseUseSSL => Platform.environment['DB_USE_SSL']?.toLowerCase() == 'true';
  Duration get databaseTimeout => Duration(seconds: int.parse(Platform.environment['DB_TIMEOUT'] ?? '30'));
  int get databaseMaxConnections => int.parse(Platform.environment['DB_MAX_CONNECTIONS'] ?? '10');

  String get databaseConnectionString {
    final auth = databaseUsername != null && databasePassword != null 
        ? '$databaseUsername:$databasePassword@' 
        : '';
    final ssl = databaseUseSSL ? '?ssl=true' : '';
    return 'mongodb://$auth$databaseHost:$databasePort/$databaseName$ssl';
  }

  // Authentication configuration
  String get jwtSecret => Platform.environment['JWT_SECRET'] ?? 'your-super-secret-jwt-key-change-in-production';
  Duration get jwtAccessTokenExpiry => Duration(
    hours: int.parse(Platform.environment['JWT_ACCESS_EXPIRY_HOURS'] ?? '1'),
  );
  Duration get jwtRefreshTokenExpiry => Duration(
    days: int.parse(Platform.environment['JWT_REFRESH_EXPIRY_DAYS'] ?? '7'),
  );

  // Rate limiting configuration
  int get rateLimitMaxRequests => int.parse(Platform.environment['RATE_LIMIT_MAX_REQUESTS'] ?? '100');
  Duration get rateLimitWindow => Duration(
    minutes: int.parse(Platform.environment['RATE_LIMIT_WINDOW_MINUTES'] ?? '15'),
  );

  // File upload configuration
  int get maxFileSize => int.parse(Platform.environment['MAX_FILE_SIZE_MB'] ?? '10') * 1024 * 1024; // Convert to bytes
  List<String> get allowedFileTypes {
    final types = Platform.environment['ALLOWED_FILE_TYPES'];
    return types?.split(',').map((type) => type.trim()).toList() ?? 
           ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  }
  String get uploadDirectory => Platform.environment['UPLOAD_DIR'] ?? 'uploads';

  // WebSocket configuration
  Duration get websocketPingInterval => Duration(
    seconds: int.parse(Platform.environment['WS_PING_INTERVAL_SECONDS'] ?? '30'),
  );
  Duration get websocketTimeout => Duration(
    seconds: int.parse(Platform.environment['WS_TIMEOUT_SECONDS'] ?? '60'),
  );
  int get websocketMaxConnections => int.parse(Platform.environment['WS_MAX_CONNECTIONS'] ?? '1000');

  // Logging configuration
  String get logLevel => Platform.environment['LOG_LEVEL'] ?? (isDevelopment ? 'debug' : 'info');
  bool get enableRequestLogging => Platform.environment['ENABLE_REQUEST_LOGGING']?.toLowerCase() != 'false';
  bool get enableErrorLogging => Platform.environment['ENABLE_ERROR_LOGGING']?.toLowerCase() != 'false';
  String get logFormat => Platform.environment['LOG_FORMAT'] ?? 'json';

  // Cache configuration
  Duration get cacheDefaultTTL => Duration(
    minutes: int.parse(Platform.environment['CACHE_DEFAULT_TTL_MINUTES'] ?? '15'),
  );
  int get cacheMaxSize => int.parse(Platform.environment['CACHE_MAX_SIZE'] ?? '1000');

  // Email configuration (if needed)
  String? get smtpHost => Platform.environment['SMTP_HOST'];
  int get smtpPort => int.parse(Platform.environment['SMTP_PORT'] ?? '587');
  String? get smtpUsername => Platform.environment['SMTP_USERNAME'];
  String? get smtpPassword => Platform.environment['SMTP_PASSWORD'];
  bool get smtpUseSSL => Platform.environment['SMTP_USE_SSL']?.toLowerCase() == 'true';

  // External API configuration
  String? get externalApiKey => Platform.environment['EXTERNAL_API_KEY'];
  String? get externalApiUrl => Platform.environment['EXTERNAL_API_URL'];
  Duration get externalApiTimeout => Duration(
    seconds: int.parse(Platform.environment['EXTERNAL_API_TIMEOUT_SECONDS'] ?? '30'),
  );

  // Security configuration
  bool get enableHttps => Platform.environment['ENABLE_HTTPS']?.toLowerCase() == 'true';
  String? get sslCertPath => Platform.environment['SSL_CERT_PATH'];
  String? get sslKeyPath => Platform.environment['SSL_KEY_PATH'];
  bool get enableHSTS => Platform.environment['ENABLE_HSTS']?.toLowerCase() == 'true';
  bool get enableCSP => Platform.environment['ENABLE_CSP']?.toLowerCase() == 'true';

  // Feature flags
  bool get enableRegistration => Platform.environment['ENABLE_REGISTRATION']?.toLowerCase() != 'false';
  bool get enablePasswordReset => Platform.environment['ENABLE_PASSWORD_RESET']?.toLowerCase() != 'false';
  bool get enableEmailVerification => Platform.environment['ENABLE_EMAIL_VERIFICATION']?.toLowerCase() == 'true';
  bool get enableTwoFactorAuth => Platform.environment['ENABLE_2FA']?.toLowerCase() == 'true';
  bool get enableSocialLogin => Platform.environment['ENABLE_SOCIAL_LOGIN']?.toLowerCase() == 'true';

  // Performance configuration
  int get maxConcurrentRequests => int.parse(Platform.environment['MAX_CONCURRENT_REQUESTS'] ?? '100');
  Duration get requestTimeout => Duration(
    seconds: int.parse(Platform.environment['REQUEST_TIMEOUT_SECONDS'] ?? '30'),
  );
  bool get enableGzipCompression => Platform.environment['ENABLE_GZIP']?.toLowerCase() != 'false';

  // Monitoring configuration
  bool get enableHealthCheck => Platform.environment['ENABLE_HEALTH_CHECK']?.toLowerCase() != 'false';
  bool get enableMetrics => Platform.environment['ENABLE_METRICS']?.toLowerCase() == 'true';
  String get metricsEndpoint => Platform.environment['METRICS_ENDPOINT'] ?? '/metrics';
  String get healthCheckEndpoint => Platform.environment['HEALTH_CHECK_ENDPOINT'] ?? '/health';

  // Validation methods
  bool isValidConfiguration() {
    final errors = validateConfiguration();
    return errors.isEmpty;
  }

  List<String> validateConfiguration() {
    final errors = <String>[];

    // Validate required environment variables for production
    if (isProduction) {
      if (jwtSecret == 'your-super-secret-jwt-key-change-in-production') {
        errors.add('JWT_SECRET must be set in production');
      }
      if (databaseUsername == null || databasePassword == null) {
        errors.add('Database credentials must be set in production');
      }
      if (!enableHttps) {
        errors.add('HTTPS should be enabled in production');
      }
    }

    // Validate port range
    if (port < 1 || port > 65535) {
      errors.add('PORT must be between 1 and 65535');
    }

    // Validate database port
    if (databasePort < 1 || databasePort > 65535) {
      errors.add('DB_PORT must be between 1 and 65535');
    }

    // Validate file size limits
    if (maxFileSize <= 0) {
      errors.add('MAX_FILE_SIZE_MB must be greater than 0');
    }

    // Validate rate limiting
    if (rateLimitMaxRequests <= 0) {
      errors.add('RATE_LIMIT_MAX_REQUESTS must be greater than 0');
    }

    return errors;
  }

  // Configuration summary for debugging
  Map<String, dynamic> toMap({bool includeSensitive = false}) {
    return {
      'environment': environment.name,
      'appName': appName,
      'appVersion': appVersion,
      'host': host,
      'port': port,
      'baseUrl': baseUrl,
      'allowedOrigins': allowedOrigins,
      'database': {
        'host': databaseHost,
        'port': databasePort,
        'name': databaseName,
        'useSSL': databaseUseSSL,
        'timeout': databaseTimeout.inSeconds,
        'maxConnections': databaseMaxConnections,
        if (includeSensitive) 'connectionString': databaseConnectionString,
      },
      'auth': {
        'accessTokenExpiry': jwtAccessTokenExpiry.inHours,
        'refreshTokenExpiry': jwtRefreshTokenExpiry.inDays,
        if (includeSensitive) 'jwtSecret': jwtSecret,
      },
      'rateLimit': {
        'maxRequests': rateLimitMaxRequests,
        'windowMinutes': rateLimitWindow.inMinutes,
      },
      'upload': {
        'maxFileSizeMB': maxFileSize ~/ (1024 * 1024),
        'allowedTypes': allowedFileTypes,
        'directory': uploadDirectory,
      },
      'websocket': {
        'pingIntervalSeconds': websocketPingInterval.inSeconds,
        'timeoutSeconds': websocketTimeout.inSeconds,
        'maxConnections': websocketMaxConnections,
      },
      'features': {
        'enableRegistration': enableRegistration,
        'enablePasswordReset': enablePasswordReset,
        'enableEmailVerification': enableEmailVerification,
        'enableTwoFactorAuth': enableTwoFactorAuth,
        'enableSocialLogin': enableSocialLogin,
      },
      'security': {
        'enableHttps': enableHttps,
        'enableHSTS': enableHSTS,
        'enableCSP': enableCSP,
      },
      'performance': {
        'maxConcurrentRequests': maxConcurrentRequests,
        'requestTimeoutSeconds': requestTimeout.inSeconds,
        'enableGzipCompression': enableGzipCompression,
      },
      'monitoring': {
        'enableHealthCheck': enableHealthCheck,
        'enableMetrics': enableMetrics,
        'metricsEndpoint': metricsEndpoint,
        'healthCheckEndpoint': healthCheckEndpoint,
      },
    };
  }

  @override
  String toString() {
    return 'ServerConfig(environment: ${environment.name}, host: $host, port: $port)';
  }
}

/// Configuration loader that can load from multiple sources
class ConfigurationLoader {
  static const String _defaultConfigFile = 'config/server.json';
  static const String _envConfigFile = 'config/server.env';

  /// Load configuration from file and environment variables
  static Future<Map<String, String>> loadConfiguration({
    String? configFile,
    String? envFile,
  }) async {
    final config = <String, String>{};

    // Load from JSON config file
    await _loadFromJsonFile(config, configFile ?? _defaultConfigFile);

    // Load from .env file
    await _loadFromEnvFile(config, envFile ?? _envConfigFile);

    // Environment variables take precedence
    config.addAll(Platform.environment);

    return config;
  }

  /// Load configuration from JSON file
  static Future<void> _loadFromJsonFile(Map<String, String> config, String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final content = await file.readAsString();
        final jsonConfig = jsonDecode(content) as Map<String, dynamic>;

        // Flatten nested JSON structure
        _flattenJson(jsonConfig, config);

        print('📋 Loaded configuration from $filePath');
      }
    } catch (e) {
      print('⚠️ Failed to load configuration from $filePath: $e');
    }
  }

  /// Load configuration from .env file
  static Future<void> _loadFromEnvFile(Map<String, String> config, String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final lines = await file.readAsLines();

        for (final line in lines) {
          final trimmedLine = line.trim();
          if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) {
            continue; // Skip empty lines and comments
          }

          final parts = trimmedLine.split('=');
          if (parts.length >= 2) {
            final key = parts[0].trim();
            final value = parts.sublist(1).join('=').trim();

            // Remove quotes if present
            var cleanValue = value;
            if ((cleanValue.startsWith('"') && cleanValue.endsWith('"')) ||
                (cleanValue.startsWith("'") && cleanValue.endsWith("'"))) {
              cleanValue = cleanValue.substring(1, cleanValue.length - 1);
            }
            config[key] = cleanValue;
          }
        }

        print('📋 Loaded environment variables from $filePath');
      }
    } catch (e) {
      print('⚠️ Failed to load environment file $filePath: $e');
    }
  }

  /// Flatten nested JSON structure for environment variable style keys
  static void _flattenJson(Map<String, dynamic> json, Map<String, String> config, [String prefix = '']) {
    json.forEach((key, value) {
      final envKey = prefix.isEmpty ? key.toUpperCase() : '${prefix}_${key.toUpperCase()}';

      if (value is Map<String, dynamic>) {
        _flattenJson(value, config, envKey);
      } else {
        config[envKey] = value.toString();
      }
    });
  }

  /// Create sample configuration files
  static Future<void> createSampleConfigFiles() async {
    await _createSampleJsonConfig();
    await _createSampleEnvConfig();
  }

  /// Create sample JSON configuration file
  static Future<void> _createSampleJsonConfig() async {
    final configDir = Directory('config');
    if (!await configDir.exists()) {
      await configDir.create(recursive: true);
    }

    final configFile = File('config/server.json');
    if (!await configFile.exists()) {
      final sampleConfig = {
        'server': {
          'host': '0.0.0.0',
          'port': 8080,
          'environment': 'development',
        },
        'database': {
          'host': 'localhost',
          'port': 27017,
          'name': 'quester_dev',
          'useSSL': false,
          'timeout': 30,
          'maxConnections': 10,
        },
        'auth': {
          'jwtSecret': 'your-super-secret-jwt-key-change-in-production',
          'accessTokenExpiryHours': 1,
          'refreshTokenExpiryDays': 7,
        },
        'features': {
          'enableRegistration': true,
          'enablePasswordReset': true,
          'enableEmailVerification': false,
          'enableTwoFactorAuth': false,
        },
        'upload': {
          'maxFileSizeMB': 10,
          'allowedTypes': ['image/jpeg', 'image/png', 'image/gif'],
          'directory': 'uploads',
        },
      };

      await configFile.writeAsString(jsonEncode(sampleConfig));
      print('📋 Created sample configuration file: config/server.json');
    }
  }

  /// Create sample .env configuration file
  static Future<void> _createSampleEnvConfig() async {
    final configDir = Directory('config');
    if (!await configDir.exists()) {
      await configDir.create(recursive: true);
    }

    final envFile = File('config/server.env');
    if (!await envFile.exists()) {
      final sampleEnv = '''
# Quester Server Configuration
# Copy this file to .env and modify as needed

# Server Configuration
NODE_ENV=development
HOST=0.0.0.0
PORT=8080
BASE_URL=http://localhost:8080

# Database Configuration
DB_HOST=localhost
DB_PORT=27017
DB_NAME=quester_dev
# DB_USERNAME=
# DB_PASSWORD=
DB_USE_SSL=false
DB_TIMEOUT=30
DB_MAX_CONNECTIONS=10

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ACCESS_EXPIRY_HOURS=1
JWT_REFRESH_EXPIRY_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MINUTES=15

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp
UPLOAD_DIR=uploads

# WebSocket Configuration
WS_PING_INTERVAL_SECONDS=30
WS_TIMEOUT_SECONDS=60
WS_MAX_CONNECTIONS=1000

# Logging Configuration
LOG_LEVEL=debug
ENABLE_REQUEST_LOGGING=true
ENABLE_ERROR_LOGGING=true
LOG_FORMAT=json

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_2FA=false
ENABLE_SOCIAL_LOGIN=false

# Security Configuration
ENABLE_HTTPS=false
ENABLE_HSTS=false
ENABLE_CSP=false

# Performance Configuration
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_SECONDS=30
ENABLE_GZIP=true

# Monitoring Configuration
ENABLE_HEALTH_CHECK=true
ENABLE_METRICS=false
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health
''';

      await envFile.writeAsString(sampleEnv);
      print('📋 Created sample environment file: config/server.env');
    }
  }
}
