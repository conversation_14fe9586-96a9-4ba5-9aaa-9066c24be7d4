#!/bin/bash

# Quester Application Build and Deployment Script
# Usage: ./deploy.sh [dev|staging|prod] [build|up|down|restart|logs]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=${1:-dev}
ACTION=${2:-up}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        dev|development)
            ENVIRONMENT="dev"
            ;;
        staging|stage)
            ENVIRONMENT="staging"
            ;;
        prod|production)
            ENVIRONMENT="prod"
            ;;
        *)
            print_error "Invalid environment: $ENVIRONMENT"
            print_error "Valid options: dev, staging, prod"
            exit 1
            ;;
    esac
}

# Validate action
validate_action() {
    case $ACTION in
        build|up|down|restart|logs|status|clean|health)
            ;;
        *)
            print_error "Invalid action: $ACTION"
            print_error "Valid options: build, up, down, restart, logs, status, clean, health"
            exit 1
            ;;
    esac
}

# Set environment-specific configurations
setup_environment() {
    print_status "Setting up $ENVIRONMENT environment..."
    
    case $ENVIRONMENT in
        dev)
            COMPOSE_FILES="-f docker-compose.base.yml -f docker-compose.dev.yml"
            ENV_FILE=".env.dev"
            ;;
        staging)
            COMPOSE_FILES="-f docker-compose.base.yml -f docker-compose.prod.yml"
            ENV_FILE=".env.staging"
            ;;
        prod)
            COMPOSE_FILES="-f docker-compose.base.yml -f docker-compose.prod.yml"
            ENV_FILE=".env.prod"
            ;;
    esac

    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file $ENV_FILE not found!"
        exit 1
    fi

    export COMPOSE_FILE="$COMPOSE_FILES"
    export ENV_FILE="$ENV_FILE"
    
    print_success "Environment configured for $ENVIRONMENT"
}

# Build images
build_images() {
    print_status "Building Docker images for $ENVIRONMENT..."
    
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE build --no-cache
    
    if [ $? -eq 0 ]; then
        print_success "Images built successfully"
    else
        print_error "Failed to build images"
        exit 1
    fi
}

# Start services
start_services() {
    print_status "Starting services for $ENVIRONMENT..."
    
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE up -d
    
    if [ $? -eq 0 ]; then
        print_success "Services started successfully"
        comprehensive_health_check
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Stop services
stop_services() {
    print_status "Stopping services for $ENVIRONMENT..."
    
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE down
    
    if [ $? -eq 0 ]; then
        print_success "Services stopped successfully"
    else
        print_error "Failed to stop services"
        exit 1
    fi
}

# Restart services
restart_services() {
    print_status "Restarting services for $ENVIRONMENT..."
    
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE restart
    
    if [ $? -eq 0 ]; then
        print_success "Services restarted successfully"
        comprehensive_health_check
    else
        print_error "Failed to restart services"
        exit 1
    fi
}

# Show logs
show_logs() {
    print_status "Showing logs for $ENVIRONMENT..."
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE logs -f --tail=100
}

# Show status
show_status() {
    print_status "Service status for $ENVIRONMENT:"
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE ps
    
    echo ""
    print_status "Health checks:"
    
    # Check if services are responding
    if [ "$ENVIRONMENT" = "dev" ]; then
        NGINX_PORT=3000
        CLIENT_PORT=8000
    else
        NGINX_PORT=80
        CLIENT_PORT=80
    fi
    
    print_status "Waiting for services to be ready..."
    sleep 15 # Wait longer for services to be ready
    
    # Test nginx health endpoint
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/health 2>/dev/null | grep -q "200"; then
        print_success "✓ Nginx is healthy (http://localhost:$NGINX_PORT/health)"
    else
        print_warning "✗ Nginx health check failed (http://localhost:$NGINX_PORT/health)"
    fi
    
    # Test API health endpoint (through nginx)
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/api/health 2>/dev/null | grep -q "200"; then
        print_success "✓ API is healthy (http://localhost:$NGINX_PORT/api/health)"
    else
        print_warning "✗ API health check failed (http://localhost:$NGINX_PORT/api/health)"
    fi
    
    # Test frontend (through nginx)
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/ 2>/dev/null | grep -q "200"; then
        print_success "✓ Frontend is healthy (http://localhost:$NGINX_PORT/)"
    else
        print_warning "✗ Frontend health check failed (http://localhost:$NGINX_PORT/)"
    fi
    
    # Test MongoDB health using docker exec
    if docker exec app_mongodb mongo --eval "db.adminCommand('ping')" --quiet > /dev/null 2>&1; then
        print_success "✓ MongoDB is healthy"
    else
        print_warning "✗ MongoDB health check failed"
    fi
    
    # Show container health status
    echo ""
    print_status "Docker container health status:"
    docker ps --filter "name=app_" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# Clean up
clean_up() {
    print_status "Cleaning up Docker resources..."
    
    # Stop services
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE down --volumes --remove-orphans
    
    # Remove images
    print_status "Removing application images..."
    docker images | grep "quester" | awk '{print $3}' | xargs -r docker rmi -f
    
    # Clean up unused resources
    docker system prune -f
    
    print_success "Cleanup completed"
}

# Comprehensive health check function
comprehensive_health_check() {
    print_status "Running comprehensive health checks for $ENVIRONMENT..."
    
    # Set ports based on environment
    if [ "$ENVIRONMENT" = "dev" ]; then
        NGINX_PORT=3000
        CLIENT_PORT=8000
    else
        NGINX_PORT=80
        CLIENT_PORT=80
    fi
    
    local all_healthy=true
    local retry_count=0
    local max_retries=12
    
    while [ $retry_count -lt $max_retries ]; do
        retry_count=$((retry_count + 1))
        print_status "Health check attempt $retry_count/$max_retries..."
        
        # Check container health status
        print_status "Checking Docker container health..."
        
        # MongoDB health
        if docker inspect app_mongodb --format='{{.State.Health.Status}}' 2>/dev/null | grep -q "healthy"; then
            print_success "✓ MongoDB container is healthy"
        else
            print_warning "✗ MongoDB container not healthy"
            all_healthy=false
        fi
        
        # Server health
        if docker inspect app_server --format='{{.State.Health.Status}}' 2>/dev/null | grep -q "healthy"; then
            print_success "✓ Server container is healthy"
        else
            print_warning "✗ Server container not healthy"
            all_healthy=false
        fi
        
        # Client health
        if docker inspect app_client --format='{{.State.Health.Status}}' 2>/dev/null | grep -q "healthy"; then
            print_success "✓ Client container is healthy"
        else
            print_warning "✗ Client container not healthy"
            all_healthy=false
        fi
        
        # Nginx health
        if docker inspect app_nginx --format='{{.State.Health.Status}}' 2>/dev/null | grep -q "healthy"; then
            print_success "✓ Nginx container is healthy"
        else
            print_warning "✗ Nginx container not healthy"
            all_healthy=false
        fi
        
        # Network connectivity tests
        print_status "Testing network connectivity..."
        
        # Test nginx health endpoint
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/health 2>/dev/null | grep -q "200"; then
            print_success "✓ Nginx health endpoint responding"
        else
            print_warning "✗ Nginx health endpoint not responding"
            all_healthy=false
        fi
        
        # Test API health endpoint
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/api/health 2>/dev/null | grep -q "200"; then
            print_success "✓ API health endpoint responding"
        else
            print_warning "✗ API health endpoint not responding"
            all_healthy=false
        fi
        
        # Test frontend
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$NGINX_PORT/ 2>/dev/null | grep -q "200"; then
            print_success "✓ Frontend responding"
        else
            print_warning "✗ Frontend not responding"
            all_healthy=false
        fi
        
        if [ "$all_healthy" = true ]; then
            print_success "All health checks passed!"
            echo ""
            print_status "Application URLs:"
            print_success "Frontend: http://localhost:$NGINX_PORT/"
            print_success "API: http://localhost:$NGINX_PORT/api/"
            print_success "Health Check: http://localhost:$NGINX_PORT/health"
            if [ "$ENVIRONMENT" = "dev" ]; then
                print_success "Direct Server: http://localhost:8080/"
                print_success "Direct Client: http://localhost:8000/"
            fi
            return 0
        fi
        
        if [ $retry_count -lt $max_retries ]; then
            print_warning "Some health checks failed. Waiting 10 seconds before retry..."
            sleep 10
            all_healthy=true  # Reset for next iteration
        fi
    done
    
    print_error "Health checks failed after $max_retries attempts"
    print_status "Service logs:"
    docker-compose $COMPOSE_FILES --env-file $ENV_FILE logs --tail=20
    return 1
}

# Pre-deployment checks
pre_deployment_checks() {
    print_status "Running pre-deployment checks..."
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if required files exist
    required_files=("docker-compose.base.yml" "settings/docker/Dockerfile.server" "settings/docker/Dockerfile.client")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file $file not found"
            exit 1
        fi
    done
    
    print_success "Pre-deployment checks passed"
}

# Show usage
show_usage() {
    echo "Usage: $0 [ENVIRONMENT] [ACTION]"
    echo ""
    echo "ENVIRONMENT:"
    echo "  dev, development   - Development environment"
    echo "  staging, stage     - Staging environment"
    echo "  prod, production   - Production environment"
    echo ""
    echo "ACTION:"
    echo "  build    - Build Docker images"
    echo "  up       - Start services (default)"
    echo "  down     - Stop services"
    echo "  restart  - Restart services"
    echo "  logs     - Show service logs"
    echo "  status   - Show service status"
    echo "  health   - Run comprehensive health checks"
    echo "  clean    - Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 dev up           # Start development environment"
    echo "  $0 prod build       # Build production images"
    echo "  $0 staging logs     # Show staging logs"
}

# Main execution
main() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    validate_environment
    validate_action
    pre_deployment_checks
    setup_environment
    
    case $ACTION in
        build)
            build_images
            ;;
        up)
            build_images
            start_services
            ;;
        down)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        health)
            comprehensive_health_check
            ;;
        clean)
            clean_up
            ;;
    esac
}

# Run main function with all arguments
main "$@"
