import 'dart:async';
import 'package:shared/shared.dart';
import 'notification_repository_impl.dart';
import 'websocket_handler_impl.dart';

// WebSocket connection manager is now in websocket_handler_impl.dart

/// Server-side notification service implementation
class ServerNotificationService {
  final InMemoryNotificationRepository _notificationRepository;
  
  ServerNotificationService(this._notificationRepository);
  
  /// Send notification to a specific user
  Future<Notification> sendToUser({
    required String userId,
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    bool isPersistent = false,
  }) async {
    // Create notification in repository
    final notification = Notification(
      id: IdGenerator.generateNotificationId(),
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: DateTime.now(),
      userId: userId,
      actionUrl: actionUrl,
      metadata: metadata,
      isPersistent: isPersistent,
    );
    
    await _notificationRepository.create(notification);
    
    // Send via WebSocket if user is connected
    final wsMessage = WebSocketMessage.notification(
      id: IdGenerator.generateShortId(),
      notification: notification,
      userId: userId,
    );

    EnhancedWebSocketManager.sendToUser(userId, wsMessage);
    
    return notification;
  }
  
  /// Send notification to multiple users
  Future<List<Notification>> sendToUsers({
    required List<String> userIds,
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    bool isPersistent = false,
  }) async {
    final notifications = <Notification>[];
    
    for (final userId in userIds) {
      final notification = await sendToUser(
        userId: userId,
        title: title,
        message: message,
        type: type,
        priority: priority,
        actionUrl: actionUrl,
        metadata: metadata,
        isPersistent: isPersistent,
      );
      notifications.add(notification);
    }
    
    return notifications;
  }
  
  /// Send broadcast notification to all connected users
  Future<Notification> sendBroadcast({
    required String title,
    required String message,
    NotificationType type = NotificationType.system,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    bool isPersistent = true,
  }) async {
    // Create broadcast notification
    final notification = Notification(
      id: IdGenerator.generateNotificationId(),
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: DateTime.now(),
      actionUrl: actionUrl,
      metadata: metadata,
      isPersistent: isPersistent,
    );
    
    await _notificationRepository.create(notification);
    
    // Broadcast via WebSocket
    final wsMessage = WebSocketMessage.notification(
      id: IdGenerator.generateShortId(),
      notification: notification,
    );

    EnhancedWebSocketManager.broadcast(wsMessage);
    
    return notification;
  }
  
  /// Send quest completion notification
  Future<Notification> sendQuestCompleteNotification({
    required String userId,
    required String questTitle,
    required int xpReward,
    String? questId,
  }) async {
    return await sendToUser(
      userId: userId,
      title: 'Quest Complete! 🎉',
      message: 'You completed "$questTitle" and earned $xpReward XP!',
      type: NotificationType.questComplete,
      priority: NotificationPriority.high,
      actionUrl: questId != null ? '/quests/$questId' : null,
      metadata: {
        'questTitle': questTitle,
        'xpReward': xpReward,
        'questId': questId,
        'type': 'quest_completion',
      },
      isPersistent: true,
    );
  }
  
  /// Send quest assignment notification
  Future<Notification> sendQuestAssignedNotification({
    required String userId,
    required String questTitle,
    String? questId,
  }) async {
    return await sendToUser(
      userId: userId,
      title: 'New Quest Available! ⚔️',
      message: 'A new quest "$questTitle" has been assigned to you.',
      type: NotificationType.questAssigned,
      priority: NotificationPriority.normal,
      actionUrl: questId != null ? '/quests/$questId' : null,
      metadata: {
        'questTitle': questTitle,
        'questId': questId,
        'type': 'quest_assignment',
      },
    );
  }
  
  /// Send level up notification
  Future<Notification> sendLevelUpNotification({
    required String userId,
    required int newLevel,
  }) async {
    return await sendToUser(
      userId: userId,
      title: 'Level Up! 🚀',
      message: 'Congratulations! You reached level $newLevel!',
      type: NotificationType.levelUp,
      priority: NotificationPriority.high,
      actionUrl: '/profile',
      metadata: {
        'newLevel': newLevel,
        'type': 'level_up',
      },
      isPersistent: true,
    );
  }
  
  /// Send achievement unlocked notification
  Future<Notification> sendAchievementUnlockedNotification({
    required String userId,
    required String achievementTitle,
    String? achievementId,
  }) async {
    return await sendToUser(
      userId: userId,
      title: 'Achievement Unlocked! 🏆',
      message: 'You unlocked the "$achievementTitle" achievement!',
      type: NotificationType.achievementUnlocked,
      priority: NotificationPriority.high,
      actionUrl: achievementId != null ? '/achievements/$achievementId' : '/achievements',
      metadata: {
        'achievementTitle': achievementTitle,
        'achievementId': achievementId,
        'type': 'achievement_unlocked',
      },
      isPersistent: true,
    );
  }
  
  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    await _notificationRepository.markAsRead(notificationId);
  }
  
  /// Mark all notifications as read for user
  Future<void> markAllAsReadForUser(String userId) async {
    await _notificationRepository.markAllAsReadForUser(userId);
  }
  
  /// Get notifications for user
  Future<List<Notification>> getNotificationsForUser(String userId, {int page = 1, int pageSize = 20}) async {
    return await _notificationRepository.getForUser(userId, page: page, pageSize: pageSize);
  }
  
  /// Get unread notifications for user
  Future<List<Notification>> getUnreadNotificationsForUser(String userId, {int page = 1, int pageSize = 20}) async {
    return await _notificationRepository.getUnreadForUser(userId, page: page, pageSize: pageSize);
  }
  
  /// Get unread count for user
  Future<int> getUnreadCountForUser(String userId) async {
    return await _notificationRepository.getUnreadCountForUser(userId);
  }
  
  /// Clean up old notifications
  Future<void> cleanupOldNotifications({Duration olderThan = const Duration(days: 30)}) async {
    await _notificationRepository.deleteOldNotifications(olderThan);
  }
}
