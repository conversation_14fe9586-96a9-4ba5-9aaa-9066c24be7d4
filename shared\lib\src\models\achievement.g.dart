// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) => Achievement(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  iconUrl: json['iconUrl'] as String?,
  category: $enumDecode(_$AchievementCategoryEnumMap, json['category']),
  rarity: $enumDecode(_$AchievementRarityEnumMap, json['rarity']),
  xpReward: (json['xpReward'] as num).toInt(),
  coinReward: (json['coinReward'] as num?)?.toInt(),
  requirements: (json['requirements'] as List<dynamic>)
      .map((e) => AchievementRequirement.fromJson(e as Map<String, dynamic>))
      .toList(),
  isHidden: json['isHidden'] as bool? ?? false,
  isRepeatable: json['isRepeatable'] as bool? ?? false,
  createdAt: DateTime.parse(json['createdAt'] as String),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'iconUrl': instance.iconUrl,
      'category': _$AchievementCategoryEnumMap[instance.category]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'xpReward': instance.xpReward,
      'coinReward': instance.coinReward,
      'requirements': instance.requirements,
      'isHidden': instance.isHidden,
      'isRepeatable': instance.isRepeatable,
      'createdAt': instance.createdAt.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$AchievementCategoryEnumMap = {
  AchievementCategory.milestone: 'milestone',
  AchievementCategory.quest: 'quest',
  AchievementCategory.social: 'social',
  AchievementCategory.skill: 'skill',
  AchievementCategory.exploration: 'exploration',
  AchievementCategory.collection: 'collection',
  AchievementCategory.time: 'time',
  AchievementCategory.special: 'special',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.uncommon: 'uncommon',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
};

UserAchievement _$UserAchievementFromJson(Map<String, dynamic> json) =>
    UserAchievement(
      id: json['id'] as String,
      userId: json['userId'] as String,
      achievementId: json['achievementId'] as String,
      achievement: json['achievement'] == null
          ? null
          : Achievement.fromJson(json['achievement'] as Map<String, dynamic>),
      status: $enumDecode(_$AchievementStatusEnumMap, json['status']),
      progress: (json['progress'] as num).toDouble(),
      unlockedAt: json['unlockedAt'] == null
          ? null
          : DateTime.parse(json['unlockedAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      progressData: json['progressData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$UserAchievementToJson(UserAchievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'achievementId': instance.achievementId,
      'achievement': instance.achievement,
      'status': _$AchievementStatusEnumMap[instance.status]!,
      'progress': instance.progress,
      'unlockedAt': instance.unlockedAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'progressData': instance.progressData,
    };

const _$AchievementStatusEnumMap = {
  AchievementStatus.locked: 'locked',
  AchievementStatus.inProgress: 'in_progress',
  AchievementStatus.unlocked: 'unlocked',
  AchievementStatus.expired: 'expired',
};

AchievementRequirement _$AchievementRequirementFromJson(
  Map<String, dynamic> json,
) => AchievementRequirement(
  id: json['id'] as String,
  type: $enumDecode(_$AchievementRequirementTypeEnumMap, json['type']),
  description: json['description'] as String,
  criteria: json['criteria'] as Map<String, dynamic>,
  targetValue: (json['targetValue'] as num).toInt(),
  unit: json['unit'] as String?,
);

Map<String, dynamic> _$AchievementRequirementToJson(
  AchievementRequirement instance,
) => <String, dynamic>{
  'id': instance.id,
  'type': _$AchievementRequirementTypeEnumMap[instance.type]!,
  'description': instance.description,
  'criteria': instance.criteria,
  'targetValue': instance.targetValue,
  'unit': instance.unit,
};

const _$AchievementRequirementTypeEnumMap = {
  AchievementRequirementType.questCompletion: 'quest_completion',
  AchievementRequirementType.xpEarned: 'xp_earned',
  AchievementRequirementType.levelReached: 'level_reached',
  AchievementRequirementType.streakMaintained: 'streak_maintained',
  AchievementRequirementType.timeSpent: 'time_spent',
  AchievementRequirementType.socialInteraction: 'social_interaction',
  AchievementRequirementType.custom: 'custom',
};

AchievementSummary _$AchievementSummaryFromJson(Map<String, dynamic> json) =>
    AchievementSummary(
      totalAchievements: (json['totalAchievements'] as num).toInt(),
      unlockedAchievements: (json['unlockedAchievements'] as num).toInt(),
      inProgressAchievements: (json['inProgressAchievements'] as num).toInt(),
      lockedAchievements: (json['lockedAchievements'] as num).toInt(),
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      totalXpEarned: (json['totalXpEarned'] as num).toInt(),
      totalCoinsEarned: (json['totalCoinsEarned'] as num).toInt(),
      recentUnlocked: (json['recentUnlocked'] as List<dynamic>)
          .map((e) => UserAchievement.fromJson(e as Map<String, dynamic>))
          .toList(),
      categoryBreakdown: (json['categoryBreakdown'] as Map<String, dynamic>)
          .map(
            (k, e) => MapEntry(
              $enumDecode(_$AchievementCategoryEnumMap, k),
              (e as num).toInt(),
            ),
          ),
    );

Map<String, dynamic> _$AchievementSummaryToJson(AchievementSummary instance) =>
    <String, dynamic>{
      'totalAchievements': instance.totalAchievements,
      'unlockedAchievements': instance.unlockedAchievements,
      'inProgressAchievements': instance.inProgressAchievements,
      'lockedAchievements': instance.lockedAchievements,
      'completionPercentage': instance.completionPercentage,
      'totalXpEarned': instance.totalXpEarned,
      'totalCoinsEarned': instance.totalCoinsEarned,
      'recentUnlocked': instance.recentUnlocked,
      'categoryBreakdown': instance.categoryBreakdown.map(
        (k, e) => MapEntry(_$AchievementCategoryEnumMap[k]!, e),
      ),
    };
