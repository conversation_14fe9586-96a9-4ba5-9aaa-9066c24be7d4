import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// Cryptographic utility functions
class CryptoUtils {
  /// Generate SHA-256 hash of a string
  static String sha256Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate SHA-1 hash of a string
  static String sha1Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha1.convert(bytes);
    return digest.toString();
  }

  /// Generate MD5 hash of a string
  static String md5Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// Generate HMAC-SHA256 signature
  static String hmacSha256(String message, String key) {
    final keyBytes = utf8.encode(key);
    final messageBytes = utf8.encode(message);
    final hmac = Hmac(sha256, keyBytes);
    final digest = hmac.convert(messageBytes);
    return digest.toString();
  }

  /// Generate HMAC-SHA1 signature
  static String hmacSha1(String message, String key) {
    final keyBytes = utf8.encode(key);
    final messageBytes = utf8.encode(message);
    final hmac = Hmac(sha1, keyBytes);
    final digest = hmac.convert(messageBytes);
    return digest.toString();
  }

  /// Generate secure random bytes
  static List<int> generateRandomBytes(int length) {
    final random = Random.secure();
    return List.generate(length, (index) => random.nextInt(256));
  }

  /// Generate secure random string
  static String generateSecureRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate cryptographically secure random token
  static String generateSecureToken(int length) {
    final bytes = generateRandomBytes(length);
    return base64Url.encode(bytes).substring(0, length);
  }

  /// Generate UUID v4 (random)
  static String generateUuid() {
    final random = Random.secure();
    final bytes = List.generate(16, (index) => random.nextInt(256));
    
    // Set version (4) and variant bits
    bytes[6] = (bytes[6] & 0x0F) | 0x40;
    bytes[8] = (bytes[8] & 0x3F) | 0x80;
    
    final hex = bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    
    return '${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}';
  }

  /// Generate salt for password hashing
  static String generateSalt([int length = 32]) {
    return generateSecureRandomString(length);
  }

  /// Hash password with salt (simple implementation)
  /// Note: In production, use bcrypt or similar
  static String hashPassword(String password, String salt) {
    return sha256Hash(password + salt);
  }

  /// Verify password against hash
  static bool verifyPassword(String password, String salt, String hash) {
    return hashPassword(password, salt) == hash;
  }

  /// Generate API key
  static String generateApiKey([int length = 32]) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate session token
  static String generateSessionToken() {
    return generateSecureToken(64);
  }

  /// Generate refresh token
  static String generateRefreshToken() {
    return generateSecureToken(128);
  }

  /// Base64 encode string
  static String base64Encode(String input) {
    final bytes = utf8.encode(input);
    return base64.encode(bytes);
  }

  /// Base64 decode string
  static String base64Decode(String encoded) {
    final bytes = base64.decode(encoded);
    return utf8.decode(bytes);
  }

  /// Base64 URL-safe encode
  static String base64UrlEncode(String input) {
    final bytes = utf8.encode(input);
    return base64Url.encode(bytes);
  }

  /// Base64 URL-safe decode
  static String base64UrlDecode(String encoded) {
    final bytes = base64Url.decode(encoded);
    return utf8.decode(bytes);
  }

  /// Generate checksum for data integrity
  static String generateChecksum(String data) {
    return sha256Hash(data).substring(0, 8);
  }

  /// Verify data integrity with checksum
  static bool verifyChecksum(String data, String checksum) {
    return generateChecksum(data) == checksum;
  }

  /// Generate OTP (One-Time Password)
  static String generateOtp([int length = 6]) {
    final random = Random.secure();
    return List.generate(length, (index) => random.nextInt(10)).join();
  }

  /// Generate time-based OTP (simplified TOTP)
  static String generateTotp(String secret, {int timeStep = 30, int digits = 6}) {
    final time = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final counter = time ~/ timeStep;
    
    final hmac = hmacSha1(counter.toString(), secret);
    final hash = hmac.substring(0, 8);
    final code = int.parse(hash, radix: 16) % pow(10, digits).toInt();
    
    return code.toString().padLeft(digits, '0');
  }

  /// Constant-time string comparison to prevent timing attacks
  static bool constantTimeEquals(String a, String b) {
    if (a.length != b.length) return false;
    
    int result = 0;
    for (int i = 0; i < a.length; i++) {
      result |= a.codeUnitAt(i) ^ b.codeUnitAt(i);
    }
    
    return result == 0;
  }

  /// Generate nonce for cryptographic operations
  static String generateNonce([int length = 16]) {
    return generateSecureRandomString(length);
  }

  /// Generate state parameter for OAuth flows
  static String generateState() {
    return generateSecureToken(32);
  }

  /// Generate PKCE code verifier
  static String generateCodeVerifier() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    final random = Random.secure();
    final length = 43 + random.nextInt(85); // 43-128 characters
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate PKCE code challenge from verifier
  static String generateCodeChallenge(String verifier) {
    final bytes = utf8.encode(verifier);
    final digest = sha256.convert(bytes);
    return base64Url.encode(digest.bytes).replaceAll('=', '');
  }

  /// Mask sensitive data for logging
  static String maskSensitiveData(String data, {int visibleChars = 4}) {
    if (data.length <= visibleChars * 2) return '*' * data.length;
    
    final start = data.substring(0, visibleChars);
    final end = data.substring(data.length - visibleChars);
    final middle = '*' * (data.length - visibleChars * 2);
    
    return start + middle + end;
  }

  /// Generate fingerprint for data
  static String generateFingerprint(Map<String, dynamic> data) {
    final sortedKeys = data.keys.toList()..sort();
    final normalized = sortedKeys.map((key) => '$key:${data[key]}').join('|');
    return sha256Hash(normalized).substring(0, 16);
  }

  /// Generate secure random integer within range
  static int generateSecureRandomInt(int min, int max) {
    final random = Random.secure();
    return min + random.nextInt(max - min + 1);
  }

  /// Generate secure random double between 0.0 and 1.0
  static double generateSecureRandomDouble() {
    final random = Random.secure();
    return random.nextDouble();
  }
}
