import 'dart:async';
import 'package:shared/shared.dart';

/// In-memory implementation of QuestRepository for development/testing
class InMemoryQuestRepository implements QuestRepository {
  final Map<String, Quest> _quests = {};
  final Map<String, List<String>> _userQuests = {}; // userId -> questIds

  @override
  Future<Quest> create(Quest entity) async {
    _quests[entity.id] = entity;
    return entity;
  }

  @override
  Future<Quest?> getById(String id) async {
    return _quests[id];
  }

  @override
  Future<List<Quest>> getAll({int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.toList();
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<Quest> update(String id, Quest entity) async {
    if (!_quests.containsKey(id)) {
      throw Exception('Quest not found');
    }
    _quests[id] = entity;
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    _quests.remove(id);
    // Remove from user assignments
    _userQuests.forEach((userId, questIds) {
      questIds.remove(id);
    });
  }

  @override
  Future<bool> exists(String id) async {
    return _quests.containsKey(id);
  }

  @override
  Future<int> count() async {
    return _quests.length;
  }

  @override
  Future<List<Quest>> search(String query, {int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.where((quest) =>
        quest.title.toLowerCase().contains(query.toLowerCase()) ||
        quest.description.toLowerCase().contains(query.toLowerCase())
    ).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    var quests = _quests.values.toList();
    
    // Apply filters
    if (filters.containsKey('status')) {
      final status = QuestStatus.values.firstWhere(
        (s) => s.name == filters['status'],
        orElse: () => QuestStatus.pending,
      );
      quests = quests.where((quest) => quest.status == status).toList();
    }
    
    if (filters.containsKey('difficulty')) {
      final difficulty = QuestDifficulty.values.firstWhere(
        (d) => d.name == filters['difficulty'],
        orElse: () => QuestDifficulty.easy,
      );
      quests = quests.where((quest) => quest.difficulty == difficulty).toList();
    }
    
    if (filters.containsKey('category')) {
      final category = QuestCategory.values.firstWhere(
        (c) => c.name == filters['category'],
        orElse: () => QuestCategory.challenge,
      );
      quests = quests.where((quest) => quest.category == category).toList();
    }
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> createBatch(List<Quest> entities) async {
    for (final quest in entities) {
      _quests[quest.id] = quest;
    }
    return entities;
  }

  @override
  Future<List<Quest>> updateBatch(List<Quest> entities) async {
    for (final quest in entities) {
      if (_quests.containsKey(quest.id)) {
        _quests[quest.id] = quest;
      }
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      _quests.remove(id);
      // Remove from user assignments
      _userQuests.forEach((userId, questIds) {
        questIds.remove(id);
      });
    }
  }

  @override
  Future<List<Quest>> getByStatus(QuestStatus status, {int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.where((quest) => quest.status == status).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getByDifficulty(QuestDifficulty difficulty, {int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.where((quest) => quest.difficulty == difficulty).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getByCategory(QuestCategory category, {int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.where((quest) => quest.category == category).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getAssignedToUser(String userId, {int page = 1, int pageSize = 20}) async {
    final questIds = _userQuests[userId] ?? [];
    final quests = questIds.map((id) => _quests[id]).where((quest) => quest != null).cast<Quest>().toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getCreatedByUser(String userId, {int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.where((quest) => quest.assignedUserId == userId).toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<void> assignToUser(String questId, String userId) async {
    if (!_quests.containsKey(questId)) {
      throw Exception('Quest not found');
    }
    
    if (!_userQuests.containsKey(userId)) {
      _userQuests[userId] = [];
    }
    
    if (!_userQuests[userId]!.contains(questId)) {
      _userQuests[userId]!.add(questId);
    }
    
    // Update quest with assigned user
    final quest = _quests[questId]!;
    final updatedQuest = quest.copyWith(
      assignedUserId: userId,
      status: QuestStatus.inProgress,
      startedAt: DateTime.now(),
    );
    _quests[questId] = updatedQuest;
  }

  @override
  Future<void> unassignFromUser(String questId, String userId) async {
    if (_userQuests.containsKey(userId)) {
      _userQuests[userId]!.remove(questId);
    }
    
    // Update quest to remove assigned user
    if (_quests.containsKey(questId)) {
      final quest = _quests[questId]!;
      final updatedQuest = quest.copyWith(
        assignedUserId: null,
        status: QuestStatus.pending,
        startedAt: null,
      );
      _quests[questId] = updatedQuest;
    }
  }

  @override
  Future<Quest> completeQuest(String questId, String userId, Map<String, dynamic>? completionData) async {
    final quest = _quests[questId];
    if (quest == null) {
      throw Exception('Quest not found');
    }
    
    if (quest.assignedUserId != userId) {
      throw Exception('Quest not assigned to this user');
    }
    
    final completedQuest = quest.copyWith(
      status: QuestStatus.completed,
      completedAt: DateTime.now(),
      metadata: {...(quest.metadata ?? {}), ...?completionData},
    );
    
    _quests[questId] = completedQuest;
    return completedQuest;
  }

  @override
  Future<Map<String, dynamic>> getQuestStats(String questId) async {
    final quest = _quests[questId];
    if (quest == null) {
      throw Exception('Quest not found');
    }
    
    // Count assignments and completions
    int assignments = 0;
    int completions = 0;
    
    _userQuests.forEach((userId, questIds) {
      if (questIds.contains(questId)) {
        assignments++;
        if (quest.status == QuestStatus.completed) {
          completions++;
        }
      }
    });
    
    return {
      'questId': questId,
      'title': quest.title,
      'totalAssignments': assignments,
      'completions': completions,
      'completionRate': assignments > 0 ? completions / assignments : 0.0,
      'difficulty': quest.difficulty.name,
      'category': quest.category.name,
      'xpReward': quest.xpReward,
    };
  }

  @override
  Future<List<Quest>> getPopularQuests({int page = 1, int pageSize = 20}) async {
    // Sort by number of assignments (popularity)
    final questPopularity = <String, int>{};
    
    _userQuests.forEach((userId, questIds) {
      for (final questId in questIds) {
        questPopularity[questId] = (questPopularity[questId] ?? 0) + 1;
      }
    });
    
    final sortedQuestIds = questPopularity.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    
    final quests = sortedQuestIds
        .map((entry) => _quests[entry.key])
        .where((quest) => quest != null)
        .cast<Quest>()
        .toList();
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  @override
  Future<List<Quest>> getRecentQuests({int page = 1, int pageSize = 20}) async {
    final quests = _quests.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= quests.length) return [];
    
    return quests.sublist(
      startIndex,
      endIndex > quests.length ? quests.length : endIndex,
    );
  }

  /// Initialize with some sample quests
  Future<void> initializeSampleQuests() async {
    final sampleQuests = [
      Quest.create(
        id: 'quest_sample_1',
        title: 'Complete Daily Exercise',
        description: 'Exercise for at least 30 minutes today',
        difficulty: QuestDifficulty.easy,
        category: QuestCategory.daily,
        xpReward: 50,
        expiresAt: DateTime.now().add(const Duration(hours: 12)),
      ),
      Quest.create(
        id: 'quest_sample_2',
        title: 'Learn New Programming Concept',
        description: 'Study and implement a new programming concept or pattern',
        difficulty: QuestDifficulty.medium,
        category: QuestCategory.challenge,
        xpReward: 100,
        expiresAt: DateTime.now().add(const Duration(days: 3)),
      ),
      Quest.create(
        id: 'quest_sample_3',
        title: 'Complete Tutorial',
        description: 'Finish the getting started tutorial',
        difficulty: QuestDifficulty.easy,
        category: QuestCategory.tutorial,
        xpReward: 25,
      ),
    ];

    for (final quest in sampleQuests) {
      await create(quest);
    }
  }
}
