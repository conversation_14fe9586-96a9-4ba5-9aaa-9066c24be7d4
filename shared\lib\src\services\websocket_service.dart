import 'dart:async';
import '../models/models.dart';

/// Abstract WebSocket service interface for real-time communication
abstract class WebSocketService {
  /// Connection management
  void connect(String url);
  void disconnect();
  bool get isConnected;
  
  /// Connection state stream
  Stream<WebSocketConnectionState> get connectionStateStream;
  
  /// Message handling
  void sendMessage(Map<String, dynamic> message);
  Stream<Map<String, dynamic>> get messageStream;
  
  /// Notification handling
  void sendNotification(Notification notification);
  Stream<Notification> get notificationStream;
  
  /// Event subscriptions
  void subscribe(String event, Function(Map<String, dynamic>) callback);
  void unsubscribe(String event);
  
  /// Error handling
  Stream<WebSocketError> get errorStream;
  
  /// Reconnection settings
  void setReconnectionSettings({
    bool autoReconnect = true,
    Duration initialDelay = const Duration(seconds: 1),
    Duration maxDelay = const Duration(seconds: 30),
    int maxRetries = 5,
  });
}

// WebSocketConnectionState, WebSocketError, and WebSocketMessage are now imported from models.dart
