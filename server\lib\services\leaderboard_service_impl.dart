import 'dart:async';
import 'package:shared/shared.dart';
import 'leaderboard_repository_impl.dart';
import 'user_repository_impl.dart';
import 'notification_service_impl.dart';

/// Server-side leaderboard service implementation
class ServerLeaderboardService {
  final InMemoryLeaderboardRepository _leaderboardRepository;
  final InMemoryUserRepository _userRepository;
  final ServerNotificationService _notificationService;
  
  // Real-time update streams
  final StreamController<Map<String, dynamic>> _leaderboardUpdatesController = 
      StreamController<Map<String, dynamic>>.broadcast();
  
  // Rank change tracking
  final Map<String, Map<String, int>> _previousRanks = {}; // leaderboardId -> userId -> rank

  ServerLeaderboardService({
    required InMemoryLeaderboardRepository leaderboardRepository,
    required InMemoryUserRepository userRepository,
    required ServerNotificationService notificationService,
  })  : _leaderboardRepository = leaderboardRepository,
        _userRepository = userRepository,
        _notificationService = notificationService;

  /// Stream of leaderboard updates
  Stream<Map<String, dynamic>> get leaderboardUpdatesStream => _leaderboardUpdatesController.stream;

  /// Get all leaderboards
  Future<List<Leaderboard>> getAllLeaderboards({int page = 1, int pageSize = 20}) async {
    return await _leaderboardRepository.getAll(page: page, pageSize: pageSize);
  }

  /// Get leaderboard by ID
  Future<Leaderboard?> getLeaderboardById(String leaderboardId) async {
    return await _leaderboardRepository.getById(leaderboardId);
  }

  /// Get leaderboard by type
  Future<Leaderboard?> getLeaderboardByType(LeaderboardType type) async {
    return await _leaderboardRepository.getByType(type.name);
  }

  /// Get global leaderboard
  Future<Leaderboard?> getGlobalLeaderboard({LeaderboardTimeframe timeframe = LeaderboardTimeframe.allTime}) async {
    final leaderboards = await _leaderboardRepository.getFiltered({
      'type': LeaderboardType.global.name,
      'timeframe': timeframe.name,
    });
    
    return leaderboards.isNotEmpty ? leaderboards.first : null;
  }

  /// Get category leaderboard
  Future<Leaderboard?> getCategoryLeaderboard(String category, {LeaderboardTimeframe timeframe = LeaderboardTimeframe.weekly}) async {
    final leaderboards = await _leaderboardRepository.getFiltered({
      'type': LeaderboardType.category.name,
      'timeframe': timeframe.name,
    });
    
    // Find leaderboard with matching category in metadata
    for (final leaderboard in leaderboards) {
      if (leaderboard.metadata?['category'] == category) {
        return leaderboard;
      }
    }
    
    return null;
  }

  /// Get user's position in leaderboard
  Future<int?> getUserPosition(String leaderboardId, String userId) async {
    return await _leaderboardRepository.getUserPosition(leaderboardId, userId);
  }

  /// Get user's rank across all leaderboards
  Future<Map<String, int>> getUserRanksAcrossLeaderboards(String userId) async {
    final allLeaderboards = await _leaderboardRepository.getAll();
    final userRanks = <String, int>{};
    
    for (final leaderboard in allLeaderboards) {
      final position = await getUserPosition(leaderboard.id, userId);
      if (position != null) {
        userRanks[leaderboard.id] = position;
      }
    }
    
    return userRanks;
  }

  /// Get top users in leaderboard
  Future<List<LeaderboardEntry>> getTopUsers(String leaderboardId, {int limit = 10}) async {
    final leaderboard = await _leaderboardRepository.getById(leaderboardId);
    if (leaderboard == null) return [];
    
    return leaderboard.entries.take(limit).toList();
  }

  /// Get users around a specific position
  Future<List<LeaderboardEntry>> getUsersAroundPosition(String leaderboardId, int position, {int range = 5}) async {
    final leaderboard = await _leaderboardRepository.getById(leaderboardId);
    if (leaderboard == null) return [];
    
    final startIndex = (position - range - 1).clamp(0, leaderboard.entries.length);
    final endIndex = (position + range).clamp(0, leaderboard.entries.length);
    
    return leaderboard.entries.sublist(startIndex, endIndex);
  }

  /// Get leaderboard statistics
  Future<LeaderboardStats> getLeaderboardStats(String leaderboardId) async {
    final statsData = await _leaderboardRepository.getStats(leaderboardId);
    
    return LeaderboardStats(
      leaderboardId: leaderboardId,
      totalParticipants: statsData['totalParticipants'] ?? 0,
      activeParticipants: statsData['activeParticipants'] ?? 0,
      averageScore: (statsData['averageScore'] ?? 0.0).toDouble(),
      highestScore: statsData['highestScore'] ?? 0,
      lowestScore: statsData['lowestScore'] ?? 0,
      lastUpdated: DateTime.parse(statsData['lastUpdated'] ?? DateTime.now().toIso8601String()),
      scoreDistribution: Map<String, int>.from(statsData['scoreDistribution'] ?? {}),
    );
  }

  /// Update user score and recalculate rankings
  Future<void> updateUserScore(String leaderboardId, String userId, int newScore) async {
    // Store previous rank for comparison
    final previousRank = await getUserPosition(leaderboardId, userId);
    if (previousRank != null) {
      if (!_previousRanks.containsKey(leaderboardId)) {
        _previousRanks[leaderboardId] = {};
      }
      _previousRanks[leaderboardId]![userId] = previousRank;
    }
    
    // Update score
    await _leaderboardRepository.updateUserScore(leaderboardId, userId, newScore);
    
    // Get new rank
    final newRank = await getUserPosition(leaderboardId, userId);
    
    // Check for rank changes and send notifications
    if (previousRank != null && newRank != null && previousRank != newRank) {
      await _handleRankChange(leaderboardId, userId, previousRank, newRank);
    }
    
    // Broadcast leaderboard update
    await _broadcastLeaderboardUpdate(leaderboardId);
  }

  /// Handle user quest completion and update relevant leaderboards
  Future<void> handleQuestCompletion(String userId, String questId, int xpGained) async {
    // Update global XP leaderboard
    final globalLeaderboard = await getGlobalLeaderboard();
    if (globalLeaderboard != null) {
      final user = await _userRepository.getById(userId);
      if (user != null) {
        await updateUserScore(globalLeaderboard.id, userId, user.xp);
      }
    }
    
    // Update weekly quest leaderboard
    final weeklyQuestLeaderboard = await getCategoryLeaderboard('quests', timeframe: LeaderboardTimeframe.weekly);
    if (weeklyQuestLeaderboard != null) {
      // Calculate quest completion score
      final userQuests = await _userRepository.getUserActivity(userId);
      final questCompletions = userQuests.where((activity) => activity.action == 'quest_completed').length;
      await updateUserScore(weeklyQuestLeaderboard.id, userId, questCompletions * 100);
    }
  }

  /// Handle user level up and update relevant leaderboards
  Future<void> handleLevelUp(String userId, int newLevel) async {
    // Update all relevant leaderboards
    final allLeaderboards = await _leaderboardRepository.getAll();
    
    for (final leaderboard in allLeaderboards) {
      if (leaderboard.type == LeaderboardType.global) {
        final user = await _userRepository.getById(userId);
        if (user != null) {
          await updateUserScore(leaderboard.id, userId, user.xp);
        }
      }
    }
  }

  /// Handle achievement unlock and update relevant leaderboards
  Future<void> handleAchievementUnlock(String userId, String achievementId) async {
    // Update monthly achievement leaderboard
    final monthlyAchievementLeaderboard = await getCategoryLeaderboard('achievements', timeframe: LeaderboardTimeframe.monthly);
    if (monthlyAchievementLeaderboard != null) {
      // This would require getting user's achievement count
      // For now, we'll use a placeholder score
      await updateUserScore(monthlyAchievementLeaderboard.id, userId, 100);
    }
  }

  /// Reset leaderboard (for periodic resets)
  Future<void> resetLeaderboard(String leaderboardId) async {
    await _leaderboardRepository.reset(leaderboardId);
    
    // Clear previous ranks
    _previousRanks.remove(leaderboardId);
    
    // Broadcast reset notification
    await _notificationService.sendBroadcast(
      title: 'Leaderboard Reset',
      message: 'A leaderboard has been reset for a new period',
      type: NotificationType.system,
      metadata: {
        'type': 'leaderboard_reset',
        'leaderboardId': leaderboardId,
      },
    );
    
    await _broadcastLeaderboardUpdate(leaderboardId);
  }

  /// Get leaderboard summary for dashboard
  Future<Map<String, dynamic>> getLeaderboardSummary() async {
    final allLeaderboards = await _leaderboardRepository.getAll();
    final summary = <String, dynamic>{};
    
    for (final leaderboard in allLeaderboards) {
      final stats = await getLeaderboardStats(leaderboard.id);
      summary[leaderboard.id] = {
        'title': leaderboard.title,
        'type': leaderboard.type.name,
        'timeframe': leaderboard.timeframe.name,
        'totalParticipants': stats.totalParticipants,
        'lastUpdated': stats.lastUpdated.toIso8601String(),
      };
    }
    
    return summary;
  }

  /// Handle rank change and send appropriate notifications
  Future<void> _handleRankChange(String leaderboardId, String userId, int oldRank, int newRank) async {
    final user = await _userRepository.getById(userId);
    if (user == null) return;
    
    final leaderboard = await _leaderboardRepository.getById(leaderboardId);
    if (leaderboard == null) return;
    
    final rankImproved = newRank < oldRank;
    final rankChange = (oldRank - newRank).abs();
    
    // Send notification for significant rank changes
    if (rankChange >= 5 || (newRank <= 10 && rankImproved)) {
      final title = rankImproved ? 'Rank Improved! 📈' : 'Rank Changed 📊';
      final message = rankImproved
          ? 'You moved up $rankChange positions to rank #$newRank in ${leaderboard.title}!'
          : 'Your rank changed to #$newRank in ${leaderboard.title}';
      
      await _notificationService.sendToUser(
        userId: userId,
        title: title,
        message: message,
        type: NotificationType.info,
        metadata: {
          'type': 'rank_change',
          'leaderboardId': leaderboardId,
          'oldRank': oldRank,
          'newRank': newRank,
          'improved': rankImproved,
        },
      );
    }
    
    // Special notifications for top positions
    if (newRank == 1 && oldRank != 1) {
      await _notificationService.sendToUser(
        userId: userId,
        title: 'Champion! 👑',
        message: 'You reached #1 in ${leaderboard.title}!',
        type: NotificationType.success,
        priority: NotificationPriority.high,
        metadata: {
          'type': 'reached_first_place',
          'leaderboardId': leaderboardId,
        },
      );
    } else if (newRank <= 3 && oldRank > 3) {
      await _notificationService.sendToUser(
        userId: userId,
        title: 'Top 3! 🥉',
        message: 'You made it to the top 3 in ${leaderboard.title}!',
        type: NotificationType.success,
        priority: NotificationPriority.high,
        metadata: {
          'type': 'reached_top_three',
          'leaderboardId': leaderboardId,
          'rank': newRank,
        },
      );
    }
  }

  /// Broadcast leaderboard update to all connected clients
  Future<void> _broadcastLeaderboardUpdate(String leaderboardId) async {
    final leaderboard = await _leaderboardRepository.getById(leaderboardId);
    if (leaderboard == null) return;
    
    final updateData = {
      'type': 'leaderboard_update',
      'leaderboardId': leaderboardId,
      'leaderboard': leaderboard.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _leaderboardUpdatesController.add(updateData);
  }

  /// Initialize sample leaderboards
  Future<void> initializeSampleLeaderboards() async {
    await _leaderboardRepository.initializeSampleLeaderboards();
  }

  /// Create new leaderboard
  Future<Leaderboard> createLeaderboard(Leaderboard leaderboard) async {
    return await _leaderboardRepository.create(leaderboard);
  }

  /// Update leaderboard
  Future<Leaderboard> updateLeaderboard(String leaderboardId, Leaderboard leaderboard) async {
    return await _leaderboardRepository.update(leaderboardId, leaderboard);
  }

  /// Delete leaderboard
  Future<void> deleteLeaderboard(String leaderboardId) async {
    await _leaderboardRepository.delete(leaderboardId);
    _previousRanks.remove(leaderboardId);
  }

  /// Dispose of resources
  void dispose() {
    _leaderboardUpdatesController.close();
  }
}
