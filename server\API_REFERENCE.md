# Quester Server API Reference

## 🌐 Base URL
- **Development**: `http://localhost:8080`
- **Production**: `https://api.quester.app`

## 🔐 Authentication

All protected endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## 📋 API Endpoints

### Authentication (`/api/auth`)

#### `POST /api/auth/register`
Register a new user account.

**Request:**
```json
{
  "username": "string (required, 3-50 chars)",
  "email": "string (required, valid email)",
  "password": "string (required, min 8 chars)",
  "firstName": "string (required)",
  "lastName": "string (required)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "user|moderator|admin",
      "level": "number",
      "xp": "number",
      "isActive": "boolean",
      "createdAt": "ISO8601 string"
    },
    "accessToken": "string",
    "refreshToken": "string"
  },
  "message": "User registered successfully",
  "statusCode": 201,
  "timestamp": "ISO8601 string"
}
```

**Errors:**
- `400`: Validation failed (duplicate email/username, weak password)
- `409`: User already exists

---

#### `POST /api/auth/login`
Authenticate user and receive tokens.

**Request:**
```json
{
  "email": "string (required)",
  "password": "string (required)"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": "User object",
    "accessToken": "string",
    "refreshToken": "string"
  },
  "message": "Login successful",
  "statusCode": 200
}
```

**Errors:**
- `401`: Invalid credentials
- `403`: Account inactive

---

#### `POST /api/auth/refresh`
Refresh access token.

**Request:**
```json
{
  "refreshToken": "string (required)"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "accessToken": "string",
    "refreshToken": "string"
  }
}
```

**Errors:**
- `401`: Invalid refresh token

---

#### `POST /api/auth/logout`
Logout and invalidate tokens.

**Request:**
```json
{
  "refreshToken": "string (required)"
}
```

**Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

#### `GET /api/auth/validate`
Validate access token and get user info.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": "User object",
    "isValid": true
  }
}
```

**Errors:**
- `401`: Invalid or expired token

---

### User Management (`/api/users`)

#### `GET /api/users/profile`
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": "User object"
}
```

---

#### `PUT /api/users/profile`
Update user profile.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "firstName": "string (optional)",
  "lastName": "string (optional)",
  "bio": "string (optional)",
  "preferences": "object (optional)"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": "Updated User object",
  "message": "Profile updated successfully"
}
```

---

#### `GET /api/users/{userId}/stats`
Get user statistics.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "string",
    "level": "number",
    "xp": "number",
    "joinDate": "ISO8601 string",
    "lastLogin": "ISO8601 string",
    "isActive": "boolean",
    "role": "string",
    "fullName": "string"
  }
}
```

---

#### `PUT /api/users/{userId}/avatar`
Update user avatar.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "avatarUrl": "string (required, valid URL)"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": "Updated User object",
  "message": "Avatar updated successfully"
}
```

---

#### `GET /api/users/{userId}/activity`
Get user activity history.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: number (default: 1)
- `pageSize`: number (default: 20, max: 100)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "userId": "string",
      "action": "string",
      "description": "string",
      "timestamp": "ISO8601 string",
      "metadata": "object"
    }
  ]
}
```

---

### Quest Management (`/api/quests`)

#### `GET /api/quests`
Get list of quests.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: number (default: 1)
- `pageSize`: number (default: 20, max: 100)
- `status`: string (active|completed|expired|cancelled)
- `difficulty`: string (easy|medium|hard|expert)
- `category`: string
- `createdBy`: string (user ID)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "difficulty": "easy|medium|hard|expert",
      "category": "string",
      "status": "active|completed|expired|cancelled",
      "xpReward": "number",
      "createdBy": "string",
      "createdAt": "ISO8601 string",
      "updatedAt": "ISO8601 string",
      "deadline": "ISO8601 string (optional)"
    }
  ],
  "pagination": {
    "page": "number",
    "pageSize": "number",
    "total": "number",
    "totalPages": "number"
  }
}
```

---

#### `POST /api/quests`
Create a new quest.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "title": "string (required, 1-200 chars)",
  "description": "string (required, 1-1000 chars)",
  "difficulty": "easy|medium|hard|expert (required)",
  "category": "string (required)",
  "xpReward": "number (required, min: 1)",
  "deadline": "ISO8601 string (optional)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": "Quest object",
  "message": "Quest created successfully",
  "statusCode": 201
}
```

**Errors:**
- `400`: Validation failed
- `403`: Insufficient permissions

---

#### `GET /api/quests/{questId}`
Get specific quest details.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": "Quest object"
}
```

**Errors:**
- `404`: Quest not found

---

#### `PUT /api/quests/{questId}`
Update quest information.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "title": "string (optional)",
  "description": "string (optional)",
  "difficulty": "string (optional)",
  "xpReward": "number (optional)",
  "deadline": "ISO8601 string (optional)"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": "Updated Quest object",
  "message": "Quest updated successfully"
}
```

**Errors:**
- `403`: Not quest owner or insufficient permissions
- `404`: Quest not found

---

#### `DELETE /api/quests/{questId}`
Delete a quest.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Quest deleted successfully"
}
```

**Errors:**
- `403`: Not quest owner or insufficient permissions
- `404`: Quest not found

---

#### `POST /api/quests/{questId}/assign/{userId}`
Assign quest to a user.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Quest assigned successfully"
}
```

**Errors:**
- `403`: Insufficient permissions
- `404`: Quest or user not found
- `409`: Quest already assigned

---

#### `POST /api/quests/{questId}/complete`
Mark quest as completed.

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "completionData": {
    "notes": "string (optional)",
    "evidence": "string (optional)",
    "metadata": "object (optional)"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": "Updated Quest object",
  "message": "Quest completed successfully"
}
```

**Errors:**
- `403`: Quest not assigned to user
- `404`: Quest not found
- `409`: Quest already completed

---

#### `GET /api/quests/user/{userId}/assigned`
Get quests assigned to a user.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: number (default: 1)
- `pageSize`: number (default: 20)
- `status`: string (optional)

**Response (200):**
```json
{
  "success": true,
  "data": ["Quest objects"],
  "pagination": "Pagination object"
}
```

---

### Notifications (`/api/notifications`)

#### `GET /api/notifications`
Get user notifications.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page`: number (default: 1)
- `pageSize`: number (default: 20)
- `unreadOnly`: boolean (default: false)
- `type`: string (info|warning|error|success|achievement)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "userId": "string",
      "title": "string",
      "message": "string",
      "type": "info|warning|error|success|achievement",
      "isRead": "boolean",
      "createdAt": "ISO8601 string",
      "readAt": "ISO8601 string (optional)"
    }
  ]
}
```

---

#### `POST /api/notifications`
Send notification to user (admin only).

**Headers:** `Authorization: Bearer <token>`

**Request:**
```json
{
  "userId": "string (required)",
  "title": "string (required)",
  "message": "string (required)",
  "type": "info|warning|error|success|achievement (required)"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": "Notification object",
  "message": "Notification sent successfully"
}
```

**Errors:**
- `403`: Insufficient permissions
- `404`: User not found

---

#### `PUT /api/notifications/{notificationId}/read`
Mark notification as read.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

**Errors:**
- `403`: Not notification owner
- `404`: Notification not found

---

#### `GET /api/notifications/unread-count`
Get count of unread notifications.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "count": "number"
  }
}
```

---

### Achievements (`/api/achievements`)

#### `GET /api/achievements`
Get list of available achievements.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "title": "string",
      "description": "string",
      "category": "string",
      "xpReward": "number",
      "type": "milestone|progress|special",
      "requirements": "object",
      "createdAt": "ISO8601 string"
    }
  ]
}
```

---

#### `GET /api/achievements/user/{userId}`
Get user's earned achievements.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "achievementId": "string",
      "userId": "string",
      "unlockedAt": "ISO8601 string",
      "progress": "number (0-1)",
      "achievement": "Achievement object"
    }
  ]
}
```

---

#### `POST /api/achievements/{achievementId}/award/{userId}`
Award achievement to user (admin only).

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "message": "Achievement awarded successfully"
}
```

**Errors:**
- `403`: Insufficient permissions
- `404`: Achievement or user not found
- `409`: Achievement already awarded

---

### Leaderboards (`/api/leaderboard`)

#### `GET /api/leaderboard/{boardType}`
Get leaderboard rankings.

**Headers:** `Authorization: Bearer <token>`

**Parameters:**
- `boardType`: string (global|weekly|monthly|category)

**Query Parameters:**
- `limit`: number (default: 50, max: 100)
- `offset`: number (default: 0)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "userId": "string",
      "username": "string",
      "score": "number",
      "rank": "number",
      "lastUpdated": "ISO8601 string"
    }
  ]
}
```

---

#### `GET /api/leaderboard/{boardType}/user/{userId}/rank`
Get user's rank in leaderboard.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "rank": "number",
    "score": "number",
    "totalUsers": "number"
  }
}
```

---

### Dashboard (`/api/dashboard`)

#### `GET /api/dashboard/stats`
Get system dashboard statistics (admin only).

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "totalUsers": "number",
    "activeUsers": "number",
    "totalQuests": "number",
    "completedQuests": "number",
    "totalNotifications": "number",
    "unreadNotifications": "number",
    "generatedAt": "ISO8601 string"
  }
}
```

---

#### `GET /api/dashboard/user/{userId}`
Get user-specific dashboard data.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "string",
    "stats": "object",
    "recentActivity": "array",
    "activeQuests": "array",
    "achievements": "array"
  }
}
```

---

### System (`/api/system`)

#### `GET /api/health`
Health check endpoint.

**Response (200):**
```json
{
  "status": "healthy|unhealthy",
  "timestamp": "ISO8601 string",
  "uptime": "string",
  "version": "string",
  "services": {
    "database": "healthy|unhealthy",
    "websocket": "healthy|unhealthy"
  }
}
```

---

#### `GET /api/metrics`
System metrics (admin only, if enabled).

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "requests": "object",
    "errors": "object",
    "performance": "object",
    "connections": "object"
  }
}
```

## 🔌 WebSocket Events

### Connection
- **URL**: `ws://localhost:8080/ws`
- **Authentication**: Send auth message after connection

### Event Types
- `authentication`: Authenticate connection
- `subscribe`/`unsubscribe`: Channel management
- `notification`: Real-time notifications
- `ping`/`pong`: Connection heartbeat
- `systemMessage`: System announcements
- `error`: Error messages

### Message Format
```json
{
  "id": "string",
  "type": "string",
  "data": "object",
  "timestamp": "ISO8601 string",
  "userId": "string (optional)",
  "sessionId": "string (optional)"
}
```

## 📊 Rate Limits

- **Default**: 100 requests per 15 minutes per IP
- **Authentication**: 10 requests per minute per IP
- **WebSocket**: 1000 concurrent connections max

## 🚨 Error Responses

All errors follow this format:
```json
{
  "success": false,
  "message": "string",
  "errors": {
    "errorId": "string",
    "code": "string",
    "details": "object (optional)"
  },
  "statusCode": "number",
  "timestamp": "ISO8601 string"
}
```

## 📝 Notes

- All timestamps are in ISO8601 format
- All IDs are strings
- Pagination uses 1-based indexing
- Maximum page size is 100 items
- All endpoints support CORS for allowed origins
