import 'package:flutter/material.dart';

/// Empty state widget for when there's no content to display
class EmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? action;
  final Color? iconColor;
  final double? iconSize;

  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.action,
    this.iconColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize ?? 80,
              color: iconColor ?? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }

  /// Factory for no quests state
  factory EmptyState.noQuests({VoidCallback? onCreateQuest}) {
    return EmptyState(
      icon: Icons.assignment_outlined,
      title: 'No Quests Yet',
      subtitle: 'Start your adventure by creating your first quest!',
      action: onCreateQuest != null
          ? ElevatedButton.icon(
              onPressed: onCreateQuest,
              icon: const Icon(Icons.add),
              label: const Text('Create Quest'),
            )
          : null,
    );
  }

  /// Factory for no notifications state
  factory EmptyState.noNotifications() {
    return const EmptyState(
      icon: Icons.notifications_none,
      title: 'No Notifications',
      subtitle: 'You\'re all caught up!',
    );
  }

  /// Factory for no search results state
  factory EmptyState.noSearchResults({String? query}) {
    return EmptyState(
      icon: Icons.search_off,
      title: 'No Results Found',
      subtitle: query != null 
          ? 'No results found for "$query"'
          : 'Try adjusting your search terms',
    );
  }

  /// Factory for network error state
  factory EmptyState.networkError({VoidCallback? onRetry}) {
    return EmptyState(
      icon: Icons.wifi_off,
      title: 'Connection Error',
      subtitle: 'Please check your internet connection and try again',
      iconColor: Colors.red,
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            )
          : null,
    );
  }

  /// Factory for server error state
  factory EmptyState.serverError({VoidCallback? onRetry}) {
    return EmptyState(
      icon: Icons.error_outline,
      title: 'Something Went Wrong',
      subtitle: 'We\'re having trouble loading your content',
      iconColor: Colors.red,
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            )
          : null,
    );
  }

  /// Factory for maintenance state
  factory EmptyState.maintenance() {
    return const EmptyState(
      icon: Icons.build,
      title: 'Under Maintenance',
      subtitle: 'We\'re making improvements. Please check back later.',
      iconColor: Colors.orange,
    );
  }

  /// Factory for coming soon state
  factory EmptyState.comingSoon() {
    return const EmptyState(
      icon: Icons.upcoming,
      title: 'Coming Soon',
      subtitle: 'This feature is currently in development',
      iconColor: Colors.blue,
    );
  }
}

/// Error state widget with retry functionality
class ErrorState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onRetry;
  final IconData icon;
  final Color? iconColor;

  const ErrorState({
    super.key,
    required this.title,
    this.subtitle,
    this.onRetry,
    this.icon = Icons.error_outline,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: icon,
      title: title,
      subtitle: subtitle,
      iconColor: iconColor ?? Theme.of(context).colorScheme.error,
      action: onRetry != null
          ? ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            )
          : null,
    );
  }
}

/// Success state widget for completed actions
class SuccessState extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onContinue;
  final String? continueLabel;

  const SuccessState({
    super.key,
    required this.title,
    this.subtitle,
    this.onContinue,
    this.continueLabel,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyState(
      icon: Icons.check_circle,
      title: title,
      subtitle: subtitle,
      iconColor: Colors.green,
      action: onContinue != null
          ? ElevatedButton(
              onPressed: onContinue,
              child: Text(continueLabel ?? 'Continue'),
            )
          : null,
    );
  }
}
