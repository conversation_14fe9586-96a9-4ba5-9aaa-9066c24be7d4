import 'dart:math' as math;

/// String manipulation utility functions
class StringUtils {
  /// Capitalize first letter of a string
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Capitalize first letter of each word
  static String capitalizeWords(String text) {
    if (text.isEmpty) return text;
    return text.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// Convert string to title case
  static String toTitleCase(String text) {
    if (text.isEmpty) return text;
    return text.toLowerCase().split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  /// Convert camelCase to snake_case
  static String camelToSnake(String text) {
    return text.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// Convert snake_case to camelCase
  static String snakeToCamel(String text) {
    return text.replaceAllMapped(
      RegExp(r'_([a-z])'),
      (match) => match.group(1)!.toUpperCase(),
    );
  }

  /// Convert string to kebab-case
  static String toKebabCase(String text) {
    return text
        .replaceAllMapped(RegExp(r'[A-Z]'), (match) => '-${match.group(0)!.toLowerCase()}')
        .replaceAll(RegExp(r'[\s_]+'), '-')
        .toLowerCase();
  }

  /// Generate random string with specified length
  static String generateRandomString(int length, {bool includeNumbers = true, bool includeSymbols = false}) {
    const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    String chars = letters;
    if (includeNumbers) chars += numbers;
    if (includeSymbols) chars += symbols;
    
    final random = math.Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate random alphanumeric string
  static String generateAlphanumeric(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = math.Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate random numeric string
  static String generateNumeric(int length) {
    const chars = '0123456789';
    final random = math.Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Truncate text with ellipsis
  static String truncate(String text, int maxLength, {String ellipsis = '...'}) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - ellipsis.length)}$ellipsis';
  }

  /// Truncate text at word boundary
  static String truncateAtWord(String text, int maxLength, {String ellipsis = '...'}) {
    if (text.length <= maxLength) return text;
    
    final truncated = text.substring(0, maxLength - ellipsis.length);
    final lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace == -1) return truncated + ellipsis;
    return truncated.substring(0, lastSpace) + ellipsis;
  }

  /// Remove HTML tags from string
  static String stripHtml(String htmlText) {
    final regex = RegExp(r'<[^>]*>');
    return htmlText.replaceAll(regex, '');
  }

  /// Remove extra whitespace and normalize spaces
  static String normalizeWhitespace(String text) {
    return text.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Count words in text
  static int countWords(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }

  /// Count characters excluding whitespace
  static int countCharacters(String text, {bool includeSpaces = true}) {
    if (includeSpaces) return text.length;
    return text.replaceAll(RegExp(r'\s'), '').length;
  }

  /// Extract initials from name
  static String getInitials(String name, {int maxInitials = 2}) {
    final words = name.trim().split(RegExp(r'\s+'));
    final initials = words
        .take(maxInitials)
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
        .where((initial) => initial.isNotEmpty)
        .join();
    return initials;
  }

  /// Check if string contains only letters
  static bool isAlpha(String text) {
    return RegExp(r'^[a-zA-Z]+$').hasMatch(text);
  }

  /// Check if string contains only numbers
  static bool isNumeric(String text) {
    return RegExp(r'^[0-9]+$').hasMatch(text);
  }

  /// Check if string contains only letters and numbers
  static bool isAlphanumeric(String text) {
    return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(text);
  }

  /// Check if string is a valid email format
  static bool isEmail(String text) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(text);
  }

  /// Check if string is a valid URL
  static bool isUrl(String text) {
    try {
      final uri = Uri.parse(text);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Mask sensitive information (e.g., credit card, phone)
  static String mask(String text, {int visibleStart = 0, int visibleEnd = 4, String maskChar = '*'}) {
    if (text.length <= visibleStart + visibleEnd) return text;
    
    final start = text.substring(0, visibleStart);
    final end = text.substring(text.length - visibleEnd);
    final middle = maskChar * (text.length - visibleStart - visibleEnd);
    
    return start + middle + end;
  }

  /// Mask email address
  static String maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) return email;
    
    final maskedUsername = username[0] + '*' * (username.length - 2) + username[username.length - 1];
    return '$maskedUsername@$domain';
  }

  /// Format phone number
  static String formatPhoneNumber(String phone, {String format = '(###) ###-####'}) {
    final digits = phone.replaceAll(RegExp(r'[^\d]'), '');
    if (digits.length != 10) return phone;
    
    return format
        .replaceFirst('###', digits.substring(0, 3))
        .replaceFirst('###', digits.substring(3, 6))
        .replaceFirst('####', digits.substring(6));
  }

  /// Slugify string for URLs
  static String slugify(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'[\s_-]+'), '-')
        .replaceAll(RegExp(r'^-+|-+$'), '');
  }

  /// Calculate Levenshtein distance between two strings
  static int levenshteinDistance(String a, String b) {
    if (a.isEmpty) return b.length;
    if (b.isEmpty) return a.length;
    
    final matrix = List.generate(
      a.length + 1,
      (i) => List.generate(b.length + 1, (j) => 0),
    );
    
    for (int i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }
    
    for (int j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }
    
    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        final cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce(math.min);
      }
    }
    
    return matrix[a.length][b.length];
  }

  /// Calculate string similarity (0.0 to 1.0)
  static double similarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;
    
    final maxLength = math.max(a.length, b.length);
    final distance = levenshteinDistance(a, b);
    
    return (maxLength - distance) / maxLength;
  }

  /// Reverse string
  static String reverse(String text) {
    return text.split('').reversed.join('');
  }

  /// Check if string is palindrome
  static bool isPalindrome(String text) {
    final cleaned = text.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '');
    return cleaned == reverse(cleaned);
  }

  /// Wrap text to specified line length
  static String wrapText(String text, int lineLength) {
    if (text.length <= lineLength) return text;
    
    final words = text.split(' ');
    final lines = <String>[];
    String currentLine = '';
    
    for (final word in words) {
      if (currentLine.isEmpty) {
        currentLine = word;
      } else if (currentLine.length + word.length + 1 <= lineLength) {
        currentLine += ' $word';
      } else {
        lines.add(currentLine);
        currentLine = word;
      }
    }
    
    if (currentLine.isNotEmpty) {
      lines.add(currentLine);
    }
    
    return lines.join('\n');
  }
}
