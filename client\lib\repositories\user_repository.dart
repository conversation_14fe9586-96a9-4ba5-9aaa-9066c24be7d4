import 'package:shared/shared.dart' hide ApiService;

import '../services/api_service.dart';

/// Repository for user-related operations
class UserRepository {
  final ApiService _apiService;

  UserRepository({
    required ApiService apiService,
  }) : _apiService = apiService;

  /// Get user by ID
  Future<ApiResponse<Map<String, dynamic>>> getUserById(String userId) async {
    return await _apiService.get('/users/$userId');
  }

  /// Search users
  Future<ApiResponse<List<Map<String, dynamic>>>> searchUsers({
    required String query,
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/search', queryParameters: {
      'q': query,
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user profile
  Future<ApiResponse<Map<String, dynamic>>> getUserProfile(String userId) async {
    return await _apiService.get('/users/$userId/profile');
  }

  /// Get user statistics
  Future<ApiResponse<Map<String, dynamic>>> getUserStats(String userId) async {
    return await _apiService.get('/users/$userId/stats');
  }

  /// Get user achievements
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserAchievements(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/achievements', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserQuests(
    String userId, {
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (status != null) queryParams['status'] = status;

    return await _apiService.get('/users/$userId/quests', queryParameters: queryParams);
  }

  /// Follow user
  Future<ApiResponse<void>> followUser(String userId) async {
    return await _apiService.post('/users/$userId/follow');
  }

  /// Unfollow user
  Future<ApiResponse<void>> unfollowUser(String userId) async {
    return await _apiService.delete('/users/$userId/follow');
  }

  /// Get user followers
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserFollowers(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/followers', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user following
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserFollowing(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/following', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Block user
  Future<ApiResponse<void>> blockUser(String userId) async {
    return await _apiService.post('/users/$userId/block');
  }

  /// Unblock user
  Future<ApiResponse<void>> unblockUser(String userId) async {
    return await _apiService.delete('/users/$userId/block');
  }

  /// Get blocked users
  Future<ApiResponse<List<Map<String, dynamic>>>> getBlockedUsers({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/blocked', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Report user
  Future<ApiResponse<void>> reportUser({
    required String userId,
    required String reason,
    String? description,
  }) async {
    return await _apiService.post('/users/$userId/report', data: {
      'reason': reason,
      'description': description,
    });
  }

  /// Get user activity feed
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserActivity(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/activity', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user leaderboard position
  Future<ApiResponse<Map<String, dynamic>>> getUserLeaderboardPosition(
    String userId,
    String leaderboardId,
  ) async {
    return await _apiService.get('/users/$userId/leaderboard/$leaderboardId');
  }

  /// Update user preferences
  Future<ApiResponse<void>> updateUserPreferences({
    Map<String, dynamic>? notificationSettings,
    Map<String, dynamic>? privacySettings,
    Map<String, dynamic>? displaySettings,
  }) async {
    final data = <String, dynamic>{};
    if (notificationSettings != null) data['notificationSettings'] = notificationSettings;
    if (privacySettings != null) data['privacySettings'] = privacySettings;
    if (displaySettings != null) data['displaySettings'] = displaySettings;

    return await _apiService.put('/users/me/preferences', data: data);
  }

  /// Get user preferences
  Future<ApiResponse<Map<String, dynamic>>> getUserPreferences() async {
    return await _apiService.get('/users/me/preferences');
  }

  /// Upload user avatar
  Future<ApiResponse<Map<String, dynamic>>> uploadAvatar({
    required List<int> fileBytes,
    required String fileName,
  }) async {
    return await _apiService.uploadFile('/users/me/avatar', fileBytes, fileName);
  }

  /// Delete user avatar
  Future<ApiResponse<void>> deleteAvatar() async {
    return await _apiService.delete('/users/me/avatar');
  }

  /// Get user badges
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserBadges(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/badges', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user friends
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserFriends(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/friends', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Send friend request
  Future<ApiResponse<void>> sendFriendRequest(String userId) async {
    return await _apiService.post('/users/$userId/friend-request');
  }

  /// Accept friend request
  Future<ApiResponse<void>> acceptFriendRequest(String userId) async {
    return await _apiService.post('/users/$userId/friend-request/accept');
  }

  /// Decline friend request
  Future<ApiResponse<void>> declineFriendRequest(String userId) async {
    return await _apiService.post('/users/$userId/friend-request/decline');
  }

  /// Remove friend
  Future<ApiResponse<void>> removeFriend(String userId) async {
    return await _apiService.delete('/users/$userId/friend');
  }

  /// Get pending friend requests
  Future<ApiResponse<List<Map<String, dynamic>>>> getPendingFriendRequests({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/me/friend-requests', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get sent friend requests
  Future<ApiResponse<List<Map<String, dynamic>>>> getSentFriendRequests({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/me/friend-requests/sent', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user groups
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserGroups(
    String userId, {
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/users/$userId/groups', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Get user notifications settings
  Future<ApiResponse<Map<String, dynamic>>> getNotificationSettings() async {
    return await _apiService.get('/users/me/notification-settings');
  }

  /// Update user notification settings
  Future<ApiResponse<void>> updateNotificationSettings({
    bool? emailNotifications,
    bool? pushNotifications,
    bool? questReminders,
    bool? achievementNotifications,
    bool? socialNotifications,
  }) async {
    final data = <String, dynamic>{};
    if (emailNotifications != null) data['emailNotifications'] = emailNotifications;
    if (pushNotifications != null) data['pushNotifications'] = pushNotifications;
    if (questReminders != null) data['questReminders'] = questReminders;
    if (achievementNotifications != null) data['achievementNotifications'] = achievementNotifications;
    if (socialNotifications != null) data['socialNotifications'] = socialNotifications;

    return await _apiService.put('/users/me/notification-settings', data: data);
  }
}
