@echo off
REM Script to prepare client directory for Docker build by removing problematic symlinks

echo Preparing client directory for Docker build...

REM Navigate to the client directory
cd /d "%~dp0\..\client"

REM Remove ephemeral directories that might contain symlinks
echo Removing ephemeral directories...
if exist "linux\flutter\ephemeral" rmdir /s /q "linux\flutter\ephemeral" 2>nul
if exist "macos\Flutter\ephemeral" rmdir /s /q "macos\Flutter\ephemeral" 2>nul
if exist "windows\flutter\ephemeral" rmdir /s /q "windows\flutter\ephemeral" 2>nul

REM Remove build artifacts that might contain symlinks
echo Removing build artifacts...
if exist ".dart_tool" rmdir /s /q ".dart_tool" 2>nul
if exist "build" rmdir /s /q "build" 2>nul

REM Find and remove .plugin_symlinks directories
echo Removing plugin symlink directories...
for /d /r %%i in (.plugin_symlinks) do (
    if exist "%%i" rmdir /s /q "%%i" 2>nul
)

echo Client directory prepared for Docker build.
