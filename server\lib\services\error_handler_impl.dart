import 'dart:async';
import 'dart:convert';
import 'package:shared/shared.dart';

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Error context information
class ErrorContext {
  final String? userId;
  final String? sessionId;
  final String? requestId;
  final String? endpoint;
  final String? method;
  final Map<String, dynamic>? headers;
  final Map<String, dynamic>? body;
  final String? userAgent;
  final String? ipAddress;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const ErrorContext({
    this.userId,
    this.sessionId,
    this.requestId,
    this.endpoint,
    this.method,
    this.headers,
    this.body,
    this.userAgent,
    this.ipAddress,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'sessionId': sessionId,
      'requestId': requestId,
      'endpoint': endpoint,
      'method': method,
      'headers': headers,
      'body': body,
      'userAgent': userAgent,
      'ipAddress': ipAddress,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// Error log entry
class ErrorLogEntry {
  final String id;
  final String message;
  final String? stackTrace;
  final ErrorSeverity severity;
  final String errorType;
  final int statusCode;
  final ErrorContext context;
  final DateTime timestamp;
  final bool isResolved;
  final String? resolution;

  const ErrorLogEntry({
    required this.id,
    required this.message,
    this.stackTrace,
    required this.severity,
    required this.errorType,
    required this.statusCode,
    required this.context,
    required this.timestamp,
    this.isResolved = false,
    this.resolution,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'stackTrace': stackTrace,
      'severity': severity.name,
      'errorType': errorType,
      'statusCode': statusCode,
      'context': context.toJson(),
      'timestamp': timestamp.toIso8601String(),
      'isResolved': isResolved,
      'resolution': resolution,
    };
  }
}

/// Server error handler implementation
class ServerErrorHandler {
  static ServerErrorHandler? _instance;
  static ServerErrorHandler get instance => _instance ??= ServerErrorHandler._();
  
  ServerErrorHandler._();

  final List<ErrorLogEntry> _errorLog = [];
  final StreamController<ErrorLogEntry> _errorStreamController = StreamController<ErrorLogEntry>.broadcast();
  
  /// Stream of error events
  Stream<ErrorLogEntry> get errorStream => _errorStreamController.stream;

  /// Handle and log an error
  Future<ApiResponse<T>> handleError<T>(
    dynamic error, {
    ErrorContext? context,
    String? customMessage,
    ErrorSeverity? severity,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());
    final timestamp = DateTime.now();
    
    // Determine error type and details
    String message;
    String errorType;
    int statusCode;
    String? stackTrace;
    ErrorSeverity errorSeverity;

    if (error is ValidationException || error is AuthException || error is QuestException || error is ApiException) {
      message = customMessage ?? error.toString();
      errorType = error.runtimeType.toString();
      statusCode = _getStatusCodeFromException(error);
      stackTrace = StackTrace.current.toString();
      errorSeverity = severity ?? _getSeverityFromException(error);
    } else if (error is Exception) {
      message = customMessage ?? error.toString();
      errorType = error.runtimeType.toString();
      statusCode = 500;
      stackTrace = StackTrace.current.toString();
      errorSeverity = severity ?? ErrorSeverity.medium;
    } else {
      message = customMessage ?? error.toString();
      errorType = 'UnknownError';
      statusCode = 500;
      stackTrace = StackTrace.current.toString();
      errorSeverity = severity ?? ErrorSeverity.medium;
    }

    // Create error log entry
    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      stackTrace: stackTrace,
      severity: errorSeverity,
      errorType: errorType,
      statusCode: statusCode,
      context: errorContext,
      timestamp: timestamp,
    );

    // Log the error
    await _logError(logEntry);

    // Create API response
    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'timestamp': timestamp.toIso8601String(),
        'type': errorType,
        'code': _getErrorCodeFromException(error),
      },
      statusCode: statusCode,
    );
  }

  /// Handle validation errors
  Future<ApiResponse<T>> handleValidationError<T>(
    ValidationException validationException, {
    ErrorContext? context,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());
    final message = validationException.message;

    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      severity: ErrorSeverity.low,
      errorType: 'ValidationError',
      statusCode: 400,
      context: errorContext,
      timestamp: DateTime.now(),
    );

    await _logError(logEntry);

    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'code': validationException.code ?? 'VALIDATION_FAILED',
        'fieldErrors': validationException.fieldErrors,
      },
      statusCode: 400,
    );
  }

  /// Handle authentication errors
  Future<ApiResponse<T>> handleAuthError<T>(
    String message, {
    ErrorContext? context,
    String? errorCode,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());

    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      severity: ErrorSeverity.medium,
      errorType: 'AuthenticationError',
      statusCode: 401,
      context: errorContext,
      timestamp: DateTime.now(),
    );

    await _logError(logEntry);

    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'code': errorCode ?? 'UNAUTHORIZED',
      },
      statusCode: 401,
    );
  }

  /// Handle authorization errors
  Future<ApiResponse<T>> handleAuthorizationError<T>(
    String message, {
    ErrorContext? context,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());

    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      severity: ErrorSeverity.medium,
      errorType: 'AuthorizationError',
      statusCode: 403,
      context: errorContext,
      timestamp: DateTime.now(),
    );

    await _logError(logEntry);

    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'code': 'FORBIDDEN',
      },
      statusCode: 403,
    );
  }

  /// Handle not found errors
  Future<ApiResponse<T>> handleNotFoundError<T>(
    String resource, {
    ErrorContext? context,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());
    final message = '$resource not found';

    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      severity: ErrorSeverity.low,
      errorType: 'NotFoundError',
      statusCode: 404,
      context: errorContext,
      timestamp: DateTime.now(),
    );

    await _logError(logEntry);

    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'code': 'NOT_FOUND',
        'resource': resource,
      },
      statusCode: 404,
    );
  }

  /// Handle rate limit errors
  Future<ApiResponse<T>> handleRateLimitError<T>({
    ErrorContext? context,
    int? retryAfter,
  }) async {
    final errorContext = context ?? ErrorContext(timestamp: DateTime.now());
    const message = 'Rate limit exceeded';

    final logEntry = ErrorLogEntry(
      id: IdGenerator.generateShortId(),
      message: message,
      severity: ErrorSeverity.low,
      errorType: 'RateLimitError',
      statusCode: 429,
      context: errorContext,
      timestamp: DateTime.now(),
    );

    await _logError(logEntry);

    return ApiResponse<T>.error(
      message: message,
      errors: {
        'errorId': logEntry.id,
        'code': 'RATE_LIMIT_EXCEEDED',
        'retryAfter': retryAfter,
      },
      statusCode: 429,
    );
  }

  /// Get error logs
  List<ErrorLogEntry> getErrorLogs({
    ErrorSeverity? severity,
    String? errorType,
    DateTime? since,
    int? limit,
  }) {
    var logs = _errorLog.toList();

    // Apply filters
    if (severity != null) {
      logs = logs.where((log) => log.severity == severity).toList();
    }
    
    if (errorType != null) {
      logs = logs.where((log) => log.errorType == errorType).toList();
    }
    
    if (since != null) {
      logs = logs.where((log) => log.timestamp.isAfter(since)).toList();
    }

    // Sort by timestamp (most recent first)
    logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Apply limit
    if (limit != null && limit > 0) {
      logs = logs.take(limit).toList();
    }

    return logs;
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStats({DateTime? since}) {
    var logs = _errorLog.toList();
    
    if (since != null) {
      logs = logs.where((log) => log.timestamp.isAfter(since)).toList();
    }

    final totalErrors = logs.length;
    final severityBreakdown = <String, int>{};
    final typeBreakdown = <String, int>{};
    final statusCodeBreakdown = <String, int>{};

    for (final log in logs) {
      // Severity breakdown
      severityBreakdown[log.severity.name] = (severityBreakdown[log.severity.name] ?? 0) + 1;
      
      // Type breakdown
      typeBreakdown[log.errorType] = (typeBreakdown[log.errorType] ?? 0) + 1;
      
      // Status code breakdown
      statusCodeBreakdown[log.statusCode.toString()] = (statusCodeBreakdown[log.statusCode.toString()] ?? 0) + 1;
    }

    return {
      'totalErrors': totalErrors,
      'severityBreakdown': severityBreakdown,
      'typeBreakdown': typeBreakdown,
      'statusCodeBreakdown': statusCodeBreakdown,
      'period': since != null ? 'since ${since.toIso8601String()}' : 'all time',
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  /// Clear error logs
  void clearErrorLogs() {
    _errorLog.clear();
  }

  /// Log error entry
  Future<void> _logError(ErrorLogEntry logEntry) async {
    _errorLog.add(logEntry);
    _errorStreamController.add(logEntry);
    
    // Print to console for development
    _printError(logEntry);
    
    // Keep only last 1000 errors to prevent memory issues
    if (_errorLog.length > 1000) {
      _errorLog.removeRange(0, _errorLog.length - 1000);
    }
  }

  /// Print error to console
  void _printError(ErrorLogEntry logEntry) {
    final severityIcon = _getSeverityIcon(logEntry.severity);
    final timestamp = logEntry.timestamp.toIso8601String();
    
    print('$severityIcon [$timestamp] ${logEntry.errorType}: ${logEntry.message}');
    
    if (logEntry.context.endpoint != null) {
      print('   Endpoint: ${logEntry.context.method} ${logEntry.context.endpoint}');
    }
    
    if (logEntry.context.userId != null) {
      print('   User: ${logEntry.context.userId}');
    }
    
    if (logEntry.severity == ErrorSeverity.high || logEntry.severity == ErrorSeverity.critical) {
      print('   Stack trace: ${logEntry.stackTrace}');
    }
  }

  /// Get severity icon for console output
  String _getSeverityIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
        return '⚠️';
      case ErrorSeverity.medium:
        return '🔶';
      case ErrorSeverity.high:
        return '🔴';
      case ErrorSeverity.critical:
        return '💥';
    }
  }

  /// Get HTTP status code from exception
  int _getStatusCodeFromException(dynamic exception) {
    if (exception is ValidationException) return 400;
    if (exception is AuthException) return 401;
    if (exception is QuestException) return 400;
    if (exception is ApiException && exception.statusCode != null) return exception.statusCode!;
    if (exception is ApiException) return 500;
    return 500;
  }

  /// Get error code from exception
  String _getErrorCodeFromException(dynamic exception) {
    if (exception is ValidationException) return exception.code ?? 'VALIDATION_FAILED';
    if (exception is AuthException) return exception.code ?? 'AUTH_ERROR';
    if (exception is QuestException) return exception.code ?? 'QUEST_ERROR';
    if (exception is ApiException) return exception.code ?? 'API_ERROR';
    return 'INTERNAL_SERVER_ERROR';
  }

  /// Get severity from exception
  ErrorSeverity _getSeverityFromException(dynamic exception) {
    if (exception is ValidationException) return ErrorSeverity.low;
    if (exception is AuthException) return ErrorSeverity.medium;
    if (exception is QuestException) return ErrorSeverity.medium;
    if (exception is ApiException && exception.statusCode != null && exception.statusCode! >= 500) return ErrorSeverity.high;
    if (exception is ApiException) return ErrorSeverity.medium;
    return ErrorSeverity.medium;
  }

  /// Dispose of resources
  void dispose() {
    _errorStreamController.close();
  }
}

/// HTTP error handling middleware
class ErrorHandlingMiddleware {
  final ServerErrorHandler _errorHandler;

  ErrorHandlingMiddleware(this._errorHandler);

  /// Create middleware function for HTTP requests
  Function get middleware {
    return (handler) {
      return (request) async {
        try {
          final response = await handler(request);
          return response;
        } catch (error, stackTrace) {
          return await _handleHttpError(error, stackTrace, request);
        }
      };
    };
  }

  /// Handle HTTP errors and return appropriate response
  Future<dynamic> _handleHttpError(dynamic error, StackTrace stackTrace, dynamic request) async {
    // Extract context from request
    final context = ErrorContext(
      requestId: _extractRequestId(request),
      endpoint: _extractEndpoint(request),
      method: _extractMethod(request),
      headers: _extractHeaders(request),
      userAgent: _extractUserAgent(request),
      ipAddress: _extractIpAddress(request),
      timestamp: DateTime.now(),
    );

    // Handle the error
    final apiResponse = await _errorHandler.handleError(
      error,
      context: context,
    );

    // Convert to HTTP response
    return _createHttpResponse(apiResponse);
  }

  /// Extract request ID from request
  String? _extractRequestId(dynamic request) {
    // This would extract from headers or generate one
    return IdGenerator.generateShortId();
  }

  /// Extract endpoint from request
  String? _extractEndpoint(dynamic request) {
    // This would extract the URL path from the request
    return request?.url?.path;
  }

  /// Extract HTTP method from request
  String? _extractMethod(dynamic request) {
    // This would extract the HTTP method from the request
    return request?.method;
  }

  /// Extract headers from request
  Map<String, dynamic>? _extractHeaders(dynamic request) {
    // This would extract headers from the request
    return request?.headers?.map((key, value) => MapEntry(key, value.toString()));
  }

  /// Extract user agent from request
  String? _extractUserAgent(dynamic request) {
    // This would extract user agent from headers
    return request?.headers?['user-agent'];
  }

  /// Extract IP address from request
  String? _extractIpAddress(dynamic request) {
    // This would extract IP from request
    return request?.connectionInfo?.remoteAddress?.address;
  }

  /// Create HTTP response from API response
  dynamic _createHttpResponse(ApiResponse apiResponse) {
    final statusCode = apiResponse.statusCode ?? 500;
    final body = jsonEncode(apiResponse.toJson((data) => data));

    // This would create an actual HTTP response object
    // For now, return a map that represents the response
    return {
      'statusCode': statusCode,
      'headers': {
        'Content-Type': 'application/json',
        'X-Error-ID': apiResponse.errors?['errorId'],
      },
      'body': body,
    };
  }
}

/// Global exception handler for uncaught errors
class GlobalExceptionHandler {
  static void initialize() {
    // Handle uncaught exceptions
    runZonedGuarded(() {
      // Application code would run here
    }, (error, stackTrace) {
      _handleUncaughtError(error, stackTrace);
    });
  }

  /// Handle uncaught errors
  static void _handleUncaughtError(dynamic error, StackTrace stackTrace) {
    final errorHandler = ServerErrorHandler.instance;

    final context = ErrorContext(
      timestamp: DateTime.now(),
      metadata: {
        'uncaught': true,
        'stackTrace': stackTrace.toString(),
      },
    );

    errorHandler.handleError(
      error,
      context: context,
      severity: ErrorSeverity.critical,
      customMessage: 'Uncaught exception: ${error.toString()}',
    );

    // In production, you might want to restart the server or take other recovery actions
    print('💥 CRITICAL: Uncaught exception occurred');
    print('Error: $error');
    print('Stack trace: $stackTrace');
  }
}
