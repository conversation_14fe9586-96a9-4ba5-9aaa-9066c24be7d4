# Production environment configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  mongodb:
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    volumes:
      - mongodb_data:/settings/data/db
      - ./settings/backups:/backups  # Backup directory
  # Mongo Express - Restricted access in production
  mongo-express:
    environment:
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_ADMIN_USERNAME}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_ADMIN_PASSWORD}
    # Only accessible from localhost in production
    ports: []
    networks:
      - app_network

  redis:
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
  # Redis Commander - Restricted access in production
  redis-commander:
    environment:
      HTTP_USER: ${REDIS_ADMIN_USERNAME}
      HTTP_PASSWORD: ${REDIS_ADMIN_PASSWORD}
    # Only accessible from localhost in production
    ports: []
    networks:
      - app_network


  server:
    build:
      target: production
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    volumes:
      - ./settings/logs:/app/logs  # Application logs
      - ./settings/uploads:/app/uploads  # File uploads

  client:
    build:
      target: production
    environment:
      FLUTTER_WEB_PORT: 80
      FLUTTER_WEB_HOSTNAME: 0.0.0.0
      API_BASE_URL: https://${DOMAIN}/api
      WS_URL: wss://${DOMAIN}/ws
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M

  nginx:
    ports:
      - "80:80"
      - "443:443"  # HTTPS support
    volumes:
      - ./settings/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./settings/ssl:/etc/nginx/ssl:ro  # SSL certificates
      - ./settings/uploads:/var/www/uploads:ro  # Static file serving
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 64M
