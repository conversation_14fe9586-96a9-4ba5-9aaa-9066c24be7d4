# Server Configuration
NODE_ENV=development
PORT=3000
WS_PORT=8080

# Client Configuration
CLIENT_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Database Configuration (when implemented)
# DATABASE_URL=postgresql://username:password@localhost:5432/quester
# REDIS_URL=redis://localhost:6379

# Email Configuration (when implemented)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# File Upload Configuration
# UPLOAD_MAX_SIZE=10485760
# UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# External APIs (when implemented)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# Monitoring and Logging
# SENTRY_DSN=your-sentry-dsn
# LOG_LEVEL=info
