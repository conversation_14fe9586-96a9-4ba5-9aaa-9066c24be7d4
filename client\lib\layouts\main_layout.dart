import 'package:flutter/material.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:go_router/go_router.dart';

import '../components/quester_app_bar.dart';
import '../components/responsive_navigation.dart';
import '../components/account_sidebar.dart';

/// Main application layout with responsive navigation
class MainLayout extends StatefulWidget {
  final Widget child;

  const MainLayout({
    super.key,
    required this.child,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  bool _isAccountSidebarOpen = false;

  @override
  Widget build(BuildContext context) {
    return ResponsiveBreakpoints.builder(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isDesktop = ResponsiveBreakpoints.of(context).isDesktop;
          final isTablet = ResponsiveBreakpoints.of(context).isTablet;
          final isMobile = ResponsiveBreakpoints.of(context).isMobile;

          return Scaffold(
            backgroundColor: const Color(0xFFFAFAFA), // Light background
            appBar: QuesterAppBar(
              onAccountTap: () {
                setState(() {
                  _isAccountSidebarOpen = !_isAccountSidebarOpen;
                });
              },
            ),
            body: Row(
              children: [
                // Side navigation for tablet and desktop
                if (isTablet || isDesktop)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(2, 0),
                        ),
                      ],
                    ),
                    child: ResponsiveNavigation(
                      isExpanded: isDesktop,
                      isVertical: true,
                    ),
                  ),

                // Main content area
                Expanded(
                  child: Container(
                    margin: EdgeInsets.all(isDesktop ? 16.0 : 8.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: widget.child,
                    ),
                  ),
                ),

                // Account sidebar
                if (_isAccountSidebarOpen)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 16,
                          offset: const Offset(-4, 0),
                        ),
                      ],
                    ),
                    child: AccountSidebar(
                      onClose: () {
                        setState(() {
                          _isAccountSidebarOpen = false;
                        });
                      },
                    ),
                  ),
              ],
            ),

            // Bottom navigation for mobile
            bottomNavigationBar: isMobile
                ? Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: ResponsiveNavigation(
                      isExpanded: false,
                      isVertical: false,
                    ),
                  )
                : null,
          );
        },
      ),
      breakpoints: [
        const Breakpoint(start: 0, end: 450, name: MOBILE),
        const Breakpoint(start: 451, end: 800, name: TABLET),
        const Breakpoint(start: 801, end: 1920, name: DESKTOP),
        const Breakpoint(start: 1921, end: double.infinity, name: '4K'),
      ],
    );
  }
}

/// Navigation item data
class NavigationItem {
  final String label;
  final IconData icon;
  final IconData? selectedIcon;
  final String route;

  const NavigationItem({
    required this.label,
    required this.icon,
    this.selectedIcon,
    required this.route,
  });
}

/// Navigation items configuration
class NavigationItems {
  static const List<NavigationItem> items = [
    NavigationItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      route: '/dashboard',
    ),
    NavigationItem(
      label: 'Quests',
      icon: Icons.assignment_outlined,
      selectedIcon: Icons.assignment,
      route: '/quests',
    ),
    NavigationItem(
      label: 'Notifications',
      icon: Icons.notifications_outlined,
      selectedIcon: Icons.notifications,
      route: '/notifications',
    ),
    NavigationItem(
      label: 'Profile',
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      route: '/profile',
    ),
  ];

  /// Get current navigation index based on route
  static int getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    
    for (int i = 0; i < items.length; i++) {
      if (location.startsWith(items[i].route)) {
        return i;
      }
    }
    
    return 0; // Default to dashboard
  }

  /// Navigate to item by index
  static void navigateToIndex(BuildContext context, int index) {
    if (index >= 0 && index < items.length) {
      context.go(items[index].route);
    }
  }
}
