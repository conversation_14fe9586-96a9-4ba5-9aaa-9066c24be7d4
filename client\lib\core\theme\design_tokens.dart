import 'package:flutter/material.dart';

/// Material 3 Design Tokens for Quester Application
/// 
/// Comprehensive design system implementation with:
/// - Material 3 color system
/// - Typography scale
/// - Spacing system
/// - Elevation levels
/// - Animation durations
/// - Component specifications
class DesignTokens {
  // ============================================================================
  // COLOR SYSTEM - Material 3 Dynamic Color
  // ============================================================================
  
  /// Primary brand colors
  static const Color primarySeed = Color(0xFF6366F1); // Indigo
  static const Color secondarySeed = Color(0xFF8B5CF6); // Purple
  static const Color tertiarySeed = Color(0xFF06B6D4); // Cyan
  
  /// Semantic colors
  static const Color successColor = Color(0xFF10B981);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color infoColor = Color(0xFF3B82F6);
  
  /// Grey palette for elevated app bar
  static const Color greyPrimary = Color(0xFF6B7280);
  static const Color greySecondary = Color(0xFF9CA3AF);
  static const Color greyLight = Color(0xFFF3F4F6);
  static const Color greyDark = Color(0xFF374151);
  
  /// Surface colors
  static const Color surfaceLight = Color(0xFFFAFAFA);
  static const Color surfaceDark = Color(0xFF121212);
  
  // ============================================================================
  // TYPOGRAPHY SYSTEM - Material 3 Type Scale
  // ============================================================================
  
  /// Display styles (largest text)
  static const double displayLarge = 57.0;
  static const double displayMedium = 45.0;
  static const double displaySmall = 36.0;
  
  /// Headline styles
  static const double headlineLarge = 32.0;
  static const double headlineMedium = 28.0;
  static const double headlineSmall = 24.0;
  
  /// Title styles
  static const double titleLarge = 22.0;
  static const double titleMedium = 16.0;
  static const double titleSmall = 14.0;
  
  /// Label styles
  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 11.0;
  
  /// Body styles
  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;
  
  // ============================================================================
  // SPACING SYSTEM - Material 3 Spacing Scale
  // ============================================================================
  
  /// Base spacing unit (4dp grid)
  static const double spaceUnit = 4.0;
  
  /// Spacing scale
  static const double space0 = 0.0;
  static const double space1 = spaceUnit * 1;    // 4dp
  static const double space2 = spaceUnit * 2;    // 8dp
  static const double space3 = spaceUnit * 3;    // 12dp
  static const double space4 = spaceUnit * 4;    // 16dp
  static const double space5 = spaceUnit * 5;    // 20dp
  static const double space6 = spaceUnit * 6;    // 24dp
  static const double space8 = spaceUnit * 8;    // 32dp
  static const double space10 = spaceUnit * 10;  // 40dp
  static const double space12 = spaceUnit * 12;  // 48dp
  static const double space16 = spaceUnit * 16;  // 64dp
  static const double space20 = spaceUnit * 20;  // 80dp
  static const double space24 = spaceUnit * 24;  // 96dp
  
  /// Semantic spacing
  static const double spaceTiny = space1;
  static const double spaceXSmall = space2;
  static const double spaceSmall = space3;
  static const double spaceMedium = space4;
  static const double spaceLarge = space6;
  static const double spaceXLarge = space8;
  static const double spaceXXLarge = space12;
  static const double spaceXXXLarge = space16;
  
  // ============================================================================
  // BORDER RADIUS SYSTEM - Material 3 Shape Scale
  // ============================================================================
  
  /// Shape scale
  static const double radiusNone = 0.0;
  static const double radiusXSmall = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  static const double radiusXXLarge = 32.0;
  static const double radiusFull = 999.0;
  
  // ============================================================================
  // ELEVATION SYSTEM - Material 3 Elevation Levels
  // ============================================================================
  
  /// Elevation levels
  static const double elevationLevel0 = 0.0;
  static const double elevationLevel1 = 1.0;
  static const double elevationLevel2 = 3.0;
  static const double elevationLevel3 = 6.0;
  static const double elevationLevel4 = 8.0;
  static const double elevationLevel5 = 12.0;
  
  // ============================================================================
  // ICON SYSTEM - Material 3 Icon Sizes
  // ============================================================================
  
  /// Icon sizes
  static const double iconXSmall = 12.0;
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  static const double iconXXLarge = 64.0;
  
  // ============================================================================
  // ANIMATION SYSTEM - Material 3 Motion Tokens
  // ============================================================================
  
  /// Duration tokens
  static const Duration durationInstant = Duration(milliseconds: 0);
  static const Duration durationFast = Duration(milliseconds: 150);
  static const Duration durationMedium = Duration(milliseconds: 300);
  static const Duration durationSlow = Duration(milliseconds: 500);
  static const Duration durationSlower = Duration(milliseconds: 800);
  
  /// Easing curves
  static const Curve easeStandard = Curves.easeInOut;
  static const Curve easeDecelerate = Curves.easeOut;
  static const Curve easeAccelerate = Curves.easeIn;
  static const Curve easeEmphasized = Curves.easeInOutCubic;
  
  // ============================================================================
  // COMPONENT SPECIFICATIONS - Material 3 Components
  // ============================================================================
  
  /// App Bar
  static const double appBarHeightMobile = 56.0;
  static const double appBarHeightDesktop = 64.0;
  
  /// Navigation
  static const double bottomNavHeight = 80.0;
  static const double navigationRailWidth = 80.0;
  static const double navigationRailWidthExpanded = 256.0;
  
  /// Buttons
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonHeightXLarge = 56.0;
  
  /// Cards
  static const double cardMinWidth = 280.0;
  static const double cardMaxWidth = 400.0;
  static const double cardAspectRatio = 16 / 9;
  
  /// List items
  static const double listItemHeight = 56.0;
  static const double listItemHeightDense = 48.0;
  static const double listItemHeightCompact = 40.0;
  
  /// Dialogs
  static const double dialogMaxWidth = 560.0;
  static const double dialogMinWidth = 280.0;
  
  /// Snackbars
  static const double snackbarMaxWidth = 600.0;
  
  // ============================================================================
  // BREAKPOINT SYSTEM - Material 3 Responsive Breakpoints
  // ============================================================================
  
  /// Responsive breakpoints
  static const double breakpointXS = 0;      // Mobile portrait
  static const double breakpointSM = 576;    // Mobile landscape
  static const double breakpointMD = 768;    // Tablet portrait
  static const double breakpointLG = 992;    // Tablet landscape
  static const double breakpointXL = 1200;   // Desktop
  static const double breakpointXXL = 1400;  // Large desktop
  
  // ============================================================================
  // UTILITY METHODS
  // ============================================================================
  
  /// Get responsive spacing based on screen width
  static double getResponsiveSpacing(double screenWidth) {
    if (screenWidth < breakpointMD) {
      return spaceMedium;
    } else if (screenWidth < breakpointXL) {
      return spaceLarge;
    } else {
      return spaceXLarge;
    }
  }
  
  /// Get responsive border radius based on screen width
  static double getResponsiveRadius(double screenWidth) {
    if (screenWidth < breakpointMD) {
      return radiusSmall;
    } else if (screenWidth < breakpointXL) {
      return radiusMedium;
    } else {
      return radiusLarge;
    }
  }
  
  /// Get responsive elevation based on screen width
  static double getResponsiveElevation(double screenWidth) {
    if (screenWidth < breakpointMD) {
      return elevationLevel2;
    } else if (screenWidth < breakpointXL) {
      return elevationLevel3;
    } else {
      return elevationLevel4;
    }
  }
}

/// Material 3 Color Scheme Extensions
extension ColorSchemeExtensions on ColorScheme {
  /// Success color
  Color get success => DesignTokens.successColor;
  
  /// Warning color
  Color get warning => DesignTokens.warningColor;
  
  /// Info color
  Color get info => DesignTokens.infoColor;
  
  /// Grey colors
  Color get greyPrimary => DesignTokens.greyPrimary;
  Color get greySecondary => DesignTokens.greySecondary;
  Color get greyLight => DesignTokens.greyLight;
  Color get greyDark => DesignTokens.greyDark;
}

/// Material 3 Text Theme Extensions
extension TextThemeExtensions on TextTheme {
  /// Custom text styles
  TextStyle get displayXLarge => displayLarge!.copyWith(fontSize: 64.0);
  TextStyle get headlineXLarge => headlineLarge!.copyWith(fontSize: 40.0);
  TextStyle get titleXLarge => titleLarge!.copyWith(fontSize: 28.0);
  TextStyle get bodyXLarge => bodyLarge!.copyWith(fontSize: 18.0);
  TextStyle get labelXLarge => labelLarge!.copyWith(fontSize: 16.0);
}
