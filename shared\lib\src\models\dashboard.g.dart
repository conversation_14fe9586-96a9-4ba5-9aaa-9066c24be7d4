// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardStats _$DashboardStatsFromJson(Map<String, dynamic> json) =>
    DashboardStats(
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      totalQuests: (json['totalQuests'] as num).toInt(),
      activeQuests: (json['activeQuests'] as num).toInt(),
      completedQuests: (json['completedQuests'] as num).toInt(),
      totalXpEarned: (json['totalXpEarned'] as num).toInt(),
      averageQuestCompletion: (json['averageQuestCompletion'] as num)
          .toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$DashboardStatsToJson(DashboardStats instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'totalQuests': instance.totalQuests,
      'activeQuests': instance.activeQuests,
      'completedQuests': instance.completedQuests,
      'totalXpEarned': instance.totalXpEarned,
      'averageQuestCompletion': instance.averageQuestCompletion,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

UserActivity _$UserActivityFromJson(Map<String, dynamic> json) => UserActivity(
  id: json['id'] as String,
  userId: json['userId'] as String,
  username: json['username'] as String,
  action: json['action'] as String,
  description: json['description'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  details: json['details'] as Map<String, dynamic>?,
  questId: json['questId'] as String?,
  xpGained: (json['xpGained'] as num?)?.toInt(),
);

Map<String, dynamic> _$UserActivityToJson(UserActivity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'username': instance.username,
      'action': instance.action,
      'description': instance.description,
      'timestamp': instance.timestamp.toIso8601String(),
      'details': instance.details,
      'questId': instance.questId,
      'xpGained': instance.xpGained,
    };

QuestStats _$QuestStatsFromJson(Map<String, dynamic> json) => QuestStats(
  questId: json['questId'] as String,
  title: json['title'] as String,
  totalAssignments: (json['totalAssignments'] as num).toInt(),
  completions: (json['completions'] as num).toInt(),
  failures: (json['failures'] as num).toInt(),
  completionRate: (json['completionRate'] as num).toDouble(),
  averageCompletionTime: (json['averageCompletionTime'] as num).toDouble(),
  lastCompleted: DateTime.parse(json['lastCompleted'] as String),
);

Map<String, dynamic> _$QuestStatsToJson(QuestStats instance) =>
    <String, dynamic>{
      'questId': instance.questId,
      'title': instance.title,
      'totalAssignments': instance.totalAssignments,
      'completions': instance.completions,
      'failures': instance.failures,
      'completionRate': instance.completionRate,
      'averageCompletionTime': instance.averageCompletionTime,
      'lastCompleted': instance.lastCompleted.toIso8601String(),
    };

UserPerformance _$UserPerformanceFromJson(Map<String, dynamic> json) =>
    UserPerformance(
      userId: json['userId'] as String,
      username: json['username'] as String,
      totalQuests: (json['totalQuests'] as num).toInt(),
      completedQuests: (json['completedQuests'] as num).toInt(),
      failedQuests: (json['failedQuests'] as num).toInt(),
      completionRate: (json['completionRate'] as num).toDouble(),
      totalXp: (json['totalXp'] as num).toInt(),
      currentLevel: (json['currentLevel'] as num).toInt(),
      rank: (json['rank'] as num).toInt(),
      lastActive: DateTime.parse(json['lastActive'] as String),
    );

Map<String, dynamic> _$UserPerformanceToJson(UserPerformance instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'username': instance.username,
      'totalQuests': instance.totalQuests,
      'completedQuests': instance.completedQuests,
      'failedQuests': instance.failedQuests,
      'completionRate': instance.completionRate,
      'totalXp': instance.totalXp,
      'currentLevel': instance.currentLevel,
      'rank': instance.rank,
      'lastActive': instance.lastActive.toIso8601String(),
    };

SystemHealth _$SystemHealthFromJson(Map<String, dynamic> json) => SystemHealth(
  status: json['status'] as String,
  timestamp: DateTime.parse(json['timestamp'] as String),
  cpuUsage: (json['cpuUsage'] as num).toDouble(),
  memoryUsage: (json['memoryUsage'] as num).toDouble(),
  activeConnections: (json['activeConnections'] as num).toInt(),
  totalRequests: (json['totalRequests'] as num).toInt(),
  averageResponseTime: (json['averageResponseTime'] as num).toDouble(),
  services: json['services'] as Map<String, dynamic>,
);

Map<String, dynamic> _$SystemHealthToJson(SystemHealth instance) =>
    <String, dynamic>{
      'status': instance.status,
      'timestamp': instance.timestamp.toIso8601String(),
      'cpuUsage': instance.cpuUsage,
      'memoryUsage': instance.memoryUsage,
      'activeConnections': instance.activeConnections,
      'totalRequests': instance.totalRequests,
      'averageResponseTime': instance.averageResponseTime,
      'services': instance.services,
    };
