import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Reusable button component with consistent styling and responsive design
class QuesterButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final QuesterButtonType type;
  final QuesterButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final Color? customColor;
  final Color? customTextColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const QuesterButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = QuesterButtonType.primary,
    this.size = QuesterButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.customColor,
    this.customTextColor,
    this.padding,
    this.borderRadius,
  });

  /// Primary button factory
  factory QuesterButton.primary({
    required String text,
    VoidCallback? onPressed,
    QuesterButtonSize size = QuesterButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return QuesterButton(
      text: text,
      onPressed: onPressed,
      type: QuesterButtonType.primary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Secondary button factory
  factory QuesterButton.secondary({
    required String text,
    VoidCallback? onPressed,
    QuesterButtonSize size = QuesterButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return QuesterButton(
      text: text,
      onPressed: onPressed,
      type: QuesterButtonType.secondary,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Outline button factory
  factory QuesterButton.outline({
    required String text,
    VoidCallback? onPressed,
    QuesterButtonSize size = QuesterButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return QuesterButton(
      text: text,
      onPressed: onPressed,
      type: QuesterButtonType.outline,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Text button factory
  factory QuesterButton.text({
    required String text,
    VoidCallback? onPressed,
    QuesterButtonSize size = QuesterButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return QuesterButton(
      text: text,
      onPressed: onPressed,
      type: QuesterButtonType.text,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// Danger button factory
  factory QuesterButton.danger({
    required String text,
    VoidCallback? onPressed,
    QuesterButtonSize size = QuesterButtonSize.medium,
    IconData? icon,
    bool isLoading = false,
    bool isFullWidth = false,
  }) {
    return QuesterButton(
      text: text,
      onPressed: onPressed,
      type: QuesterButtonType.danger,
      size: size,
      icon: icon,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get button dimensions based on size
    final buttonHeight = _getButtonHeight();
    final fontSize = _getFontSize();
    final iconSize = _getIconSize();
    final horizontalPadding = _getHorizontalPadding();

    // Get colors based on type
    final colors = _getButtonColors(colorScheme);

    Widget buttonChild = _buildButtonContent(fontSize, iconSize);

    Widget button;

    switch (type) {
      case QuesterButtonType.primary:
      case QuesterButtonType.secondary:
      case QuesterButtonType.danger:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: customColor ?? colors.backgroundColor,
            foregroundColor: customTextColor ?? colors.textColor,
            disabledBackgroundColor: colors.disabledColor,
            elevation: 2,
            shadowColor: Colors.black26,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8.r),
            ),
            padding: padding ?? EdgeInsets.symmetric(horizontal: horizontalPadding),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonChild,
        );
        break;

      case QuesterButtonType.outline:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: customTextColor ?? colors.textColor,
            side: BorderSide(
              color: customColor ?? colors.borderColor,
              width: 1.5,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8.r),
            ),
            padding: padding ?? EdgeInsets.symmetric(horizontal: horizontalPadding),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonChild,
        );
        break;

      case QuesterButtonType.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: customTextColor ?? colors.textColor,
            shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(8.r),
            ),
            padding: padding ?? EdgeInsets.symmetric(horizontal: horizontalPadding),
            minimumSize: Size(0, buttonHeight),
          ),
          child: buttonChild,
        );
        break;
    }

    if (isFullWidth) {
      return SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  Widget _buildButtonContent(double fontSize, double iconSize) {
    if (isLoading) {
      return SizedBox(
        height: iconSize,
        width: iconSize,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            customTextColor ?? Colors.white,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: iconSize),
          SizedBox(width: 8.w),
          Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  double _getButtonHeight() {
    switch (size) {
      case QuesterButtonSize.small:
        return 36.h;
      case QuesterButtonSize.medium:
        return 48.h;
      case QuesterButtonSize.large:
        return 56.h;
    }
  }

  double _getFontSize() {
    switch (size) {
      case QuesterButtonSize.small:
        return 14.sp;
      case QuesterButtonSize.medium:
        return 16.sp;
      case QuesterButtonSize.large:
        return 18.sp;
    }
  }

  double _getIconSize() {
    switch (size) {
      case QuesterButtonSize.small:
        return 16.sp;
      case QuesterButtonSize.medium:
        return 20.sp;
      case QuesterButtonSize.large:
        return 24.sp;
    }
  }

  double _getHorizontalPadding() {
    switch (size) {
      case QuesterButtonSize.small:
        return 16.w;
      case QuesterButtonSize.medium:
        return 24.w;
      case QuesterButtonSize.large:
        return 32.w;
    }
  }

  _ButtonColors _getButtonColors(ColorScheme colorScheme) {
    switch (type) {
      case QuesterButtonType.primary:
        return _ButtonColors(
          backgroundColor: colorScheme.primary,
          textColor: colorScheme.onPrimary,
          borderColor: colorScheme.primary,
          disabledColor: colorScheme.primary.withValues(alpha: 0.3),
        );
      case QuesterButtonType.secondary:
        return _ButtonColors(
          backgroundColor: colorScheme.secondary,
          textColor: colorScheme.onSecondary,
          borderColor: colorScheme.secondary,
          disabledColor: colorScheme.secondary.withValues(alpha: 0.3),
        );
      case QuesterButtonType.outline:
        return _ButtonColors(
          backgroundColor: Colors.transparent,
          textColor: colorScheme.primary,
          borderColor: colorScheme.primary,
          disabledColor: colorScheme.primary.withValues(alpha: 0.3),
        );
      case QuesterButtonType.text:
        return _ButtonColors(
          backgroundColor: Colors.transparent,
          textColor: colorScheme.primary,
          borderColor: Colors.transparent,
          disabledColor: colorScheme.primary.withValues(alpha: 0.3),
        );
      case QuesterButtonType.danger:
        return _ButtonColors(
          backgroundColor: colorScheme.error,
          textColor: colorScheme.onError,
          borderColor: colorScheme.error,
          disabledColor: colorScheme.error.withValues(alpha: 0.3),
        );
    }
  }
}

class _ButtonColors {
  final Color backgroundColor;
  final Color textColor;
  final Color borderColor;
  final Color disabledColor;

  _ButtonColors({
    required this.backgroundColor,
    required this.textColor,
    required this.borderColor,
    required this.disabledColor,
  });
}

enum QuesterButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
}

enum QuesterButtonSize {
  small,
  medium,
  large,
}
