import 'package:flutter/material.dart';

/// Responsive breakpoints
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
  static const double wideDesktop = 1800;
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  wideDesktop,
}

/// Responsive builder widget that provides different layouts based on screen size
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = _getDeviceType(constraints.maxWidth);
        return builder(context, deviceType);
      },
    );
  }

  static DeviceType _getDeviceType(double width) {
    if (width >= Breakpoints.wideDesktop) {
      return DeviceType.wideDesktop;
    } else if (width >= Breakpoints.desktop) {
      return DeviceType.desktop;
    } else if (width >= Breakpoints.tablet) {
      return DeviceType.tablet;
    } else {
      return DeviceType.mobile;
    }
  }

  /// Get device type for current context
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return _getDeviceType(width);
  }

  /// Check if current device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if current device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if current device is desktop
  static bool isDesktop(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.desktop || deviceType == DeviceType.wideDesktop;
  }

  /// Check if current device is wide desktop
  static bool isWideDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.wideDesktop;
  }
}

/// Responsive value that changes based on screen size
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;
  final T? wideDesktop;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
    this.wideDesktop,
  });

  /// Get value for current device type
  T getValue(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
      case DeviceType.wideDesktop:
        return wideDesktop ?? desktop ?? tablet ?? mobile;
    }
  }

  /// Get value for current context
  T getValueForContext(BuildContext context) {
    final deviceType = ResponsiveBuilder.getDeviceType(context);
    return getValue(deviceType);
  }
}

/// Responsive widget that shows different widgets based on screen size
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? wideDesktop;

  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.wideDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        switch (deviceType) {
          case DeviceType.mobile:
            return mobile;
          case DeviceType.tablet:
            return tablet ?? mobile;
          case DeviceType.desktop:
            return desktop ?? tablet ?? mobile;
          case DeviceType.wideDesktop:
            return wideDesktop ?? desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}

/// Responsive grid that adjusts column count based on screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final ResponsiveValue<int> columnCount;
  final double spacing;
  final double runSpacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    required this.columnCount,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final columns = columnCount.getValue(deviceType);
        
        return Padding(
          padding: padding ?? EdgeInsets.zero,
          child: Wrap(
            spacing: spacing,
            runSpacing: runSpacing,
            children: children.map((child) {
              final width = (MediaQuery.of(context).size.width - 
                  (padding?.horizontal ?? 0) - 
                  (spacing * (columns - 1))) / columns;
              
              return SizedBox(
                width: width,
                child: child,
              );
            }).toList(),
          ),
        );
      },
    );
  }
}

/// Responsive padding that adjusts based on screen size
class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final ResponsiveValue<EdgeInsetsGeometry> padding;

  const ResponsivePadding({
    super.key,
    required this.child,
    required this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Padding(
          padding: padding.getValue(deviceType),
          child: child,
        );
      },
    );
  }
}

/// Responsive sized box that adjusts dimensions based on screen size
class ResponsiveSizedBox extends StatelessWidget {
  final Widget? child;
  final ResponsiveValue<double>? width;
  final ResponsiveValue<double>? height;

  const ResponsiveSizedBox({
    super.key,
    this.child,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return SizedBox(
          width: width?.getValue(deviceType),
          height: height?.getValue(deviceType),
          child: child,
        );
      },
    );
  }
}

/// Responsive text that adjusts style based on screen size
class ResponsiveText extends StatelessWidget {
  final String text;
  final ResponsiveValue<TextStyle> style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText({
    super.key,
    required this.text,
    required this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Text(
          text,
          style: style.getValue(deviceType),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }
}
