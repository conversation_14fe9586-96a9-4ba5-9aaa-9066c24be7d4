# Base docker-compose configuration
services:
  mongodb:
    image: mongo:4.4
    container_name: app_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    volumes:
      - mongodb_data:/data/db
      - ./settings/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - app_network    
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: app_redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - ./settings/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - app_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  mongo-express:
    image: mongo-express:latest
    container_name: app_mongo_express
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_USERNAME}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD}
      ME_CONFIG_MONGODB_URL: mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/
      ME_CONFIG_MONGODB_SERVER: mongodb
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - app_network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: app_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: redis:redis:6379
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - app_network

  server:
    build:
      context: .
      dockerfile: ./settings/docker/Dockerfile.server
      target: ${BUILD_TARGET:-development}
    container_name: app_server
    restart: unless-stopped
    environment:
      MONGO_URL: mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/${MONGO_DATABASE}?authSource=admin
      JWT_SECRET: ${JWT_SECRET}
      PORT: 8080
      NODE_ENV: ${NODE_ENV:-development}
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - app_network
    expose:
      - "8080"    
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:8080/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  client:
    build:
      context: .
      dockerfile: ./settings/docker/Dockerfile.client
      target: ${BUILD_TARGET:-development}
    container_name: app_client
    restart: unless-stopped
    environment:
      FLUTTER_WEB_PORT: ${CLIENT_PORT:-8000}
      FLUTTER_WEB_HOSTNAME: 0.0.0.0
    networks:
      - app_network
    expose:
      - "${CLIENT_PORT:-8000}"
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:${CLIENT_PORT:-8000}/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: app_nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "${NGINX_PORT:-80}:80"
    depends_on:
      server:
        condition: service_healthy
      client:
        condition: service_healthy
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
  redis_data:
  server_pub_cache:

networks:
  app_network:
    driver: bridge
