import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'api_response.g.dart';

/// Generic API response wrapper for consistent server responses
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;
  final int? statusCode;
  final DateTime timestamp;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
    this.statusCode,
    required this.timestamp,
  });

  /// Success response factory
  factory ApiResponse.success({
    required T data,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message ?? 'Operation successful',
      statusCode: statusCode ?? 200,
      timestamp: DateTime.now(),
    );
  }

  /// Error response factory
  factory ApiResponse.error({
    required String message,
    Map<String, dynamic>? errors,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      statusCode: statusCode ?? 400,
      timestamp: DateTime.now(),
    );
  }

  /// Type-safe JSON serialization
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  @override
  List<Object?> get props => [success, message, data, errors, statusCode, timestamp];
}

/// Pagination metadata for paginated API responses
@JsonSerializable()
class PaginationMeta extends Equatable {
  final int page;
  final int pageSize;
  final int totalItems;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  const PaginationMeta({
    required this.page,
    required this.pageSize,
    required this.totalItems,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);

  @override
  List<Object?> get props => [page, pageSize, totalItems, totalPages, hasNext, hasPrevious];
}

/// Paginated API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class PaginatedApiResponse<T> extends Equatable {
  final bool success;
  final String? message;
  final List<T> data;
  final PaginationMeta pagination;
  final Map<String, dynamic>? errors;
  final int? statusCode;
  final DateTime timestamp;

  const PaginatedApiResponse({
    required this.success,
    this.message,
    required this.data,
    required this.pagination,
    this.errors,
    this.statusCode,
    required this.timestamp,
  });

  /// Success response factory
  factory PaginatedApiResponse.success({
    required List<T> data,
    required PaginationMeta pagination,
    String? message,
    int? statusCode,
  }) {
    return PaginatedApiResponse<T>(
      success: true,
      data: data,
      pagination: pagination,
      message: message ?? 'Operation successful',
      statusCode: statusCode ?? 200,
      timestamp: DateTime.now(),
    );
  }

  /// Error response factory
  factory PaginatedApiResponse.error({
    required String message,
    Map<String, dynamic>? errors,
    int? statusCode,
  }) {
    return PaginatedApiResponse<T>(
      success: false,
      data: [],
      pagination: const PaginationMeta(
        page: 1,
        pageSize: 0,
        totalItems: 0,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false,
      ),
      message: message,
      errors: errors,
      statusCode: statusCode ?? 400,
      timestamp: DateTime.now(),
    );
  }

  factory PaginatedApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PaginatedApiResponseToJson(this, toJsonT);

  @override
  List<Object?> get props => [success, message, data, pagination, errors, statusCode, timestamp];
}

/// Paginated response wrapper
@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> extends Equatable {
  final List<T> data;
  final int page;
  final int pageSize;
  final int totalItems;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  const PaginatedResponse({
    required this.data,
    required this.page,
    required this.pageSize,
    required this.totalItems,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  /// Create paginated response from data
  factory PaginatedResponse.create({
    required List<T> allData,
    required int page,
    required int pageSize,
  }) {
    final totalItems = allData.length;
    final totalPages = (totalItems / pageSize).ceil();
    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, totalItems);
    
    final data = allData.sublist(
      startIndex.clamp(0, totalItems),
      endIndex,
    );

    return PaginatedResponse<T>(
      data: data,
      page: page,
      pageSize: pageSize,
      totalItems: totalItems,
      totalPages: totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    );
  }

  @override
  List<Object?> get props => [
        data,
        page,
        pageSize,
        totalItems,
        totalPages,
        hasNext,
        hasPrevious,
      ];
}

/// Error details model
@JsonSerializable()
class ErrorDetail extends Equatable {
  final String field;
  final String message;
  final String? code;

  const ErrorDetail({
    required this.field,
    required this.message,
    this.code,
  });

  factory ErrorDetail.fromJson(Map<String, dynamic> json) =>
      _$ErrorDetailFromJson(json);

  Map<String, dynamic> toJson() => _$ErrorDetailToJson(this);

  @override
  List<Object?> get props => [field, message, code];
}

/// Validation error response
@JsonSerializable()
class ValidationErrorResponse extends Equatable {
  final String message;
  final List<ErrorDetail> errors;

  const ValidationErrorResponse({
    required this.message,
    required this.errors,
  });

  factory ValidationErrorResponse.fromJson(Map<String, dynamic> json) =>
      _$ValidationErrorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ValidationErrorResponseToJson(this);

  @override
  List<Object?> get props => [message, errors];
}
