# Multi-stage Dockerfile for Dart server
FROM dart:stable AS base

# Set pub cache to be local to the container
ENV PUB_CACHE=/app/.pub-cache

WORKDIR /app

# Copy shared package first (complete package structure)
COPY shared/pubspec.yaml ./shared/
COPY shared/pubspec.lock ./shared/
COPY shared/lib/ ./shared/lib/
COPY shared/analysis_options.yaml ./shared/

# Install shared package dependencies first
WORKDIR /app/shared
RUN dart pub get

# Copy server pubspec files for better caching
WORKDIR /app
COPY server/pubspec.yaml ./server/
COPY server/pubspec.lock ./server/

WORKDIR /app/server

# Get server dependencies (which includes the shared package)
RUN dart pub get

# Development stage
FROM base AS development

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Go back to app directory to preserve proper relative paths
WORKDIR /app

# Copy remaining shared package files
COPY shared/ ./shared/

# Copy server source code
COPY server/ ./server/

# Set working directory back to server for CMD
WORKDIR /app/server

EXPOSE 8080

# Run with hot reload for development
CMD ["dart", "run", "bin/server.dart"]

# Build stage
FROM base AS build

# Go back to app directory to preserve proper relative paths
WORKDIR /app

# Copy remaining shared package files
COPY shared/ ./shared/

# Copy server source code
COPY server/ ./server/

# Set working directory to server for compilation
WORKDIR /app/server

# Compile the Dart application for production
RUN dart compile exe bin/server.dart -o bin/server

# Production stage
FROM ubuntu:22.04 AS production

# Install necessary runtime dependencies including curl for health checks
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy compiled binary from build stage
COPY --from=build /app/server/bin/server ./server

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8080

# Run the compiled binary
CMD ["./server"]
