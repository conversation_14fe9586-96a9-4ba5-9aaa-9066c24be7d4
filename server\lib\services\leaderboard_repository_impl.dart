import 'dart:async';
import 'package:shared/shared.dart';
import 'user_repository_impl.dart';
import 'quest_repository_impl.dart';
import 'achievement_repository_impl.dart';

/// In-memory implementation of LeaderboardRepository for development/testing
class InMemoryLeaderboardRepository implements LeaderboardRepository {
  final Map<String, Leaderboard> _leaderboards = {};
  final InMemoryUserRepository _userRepository;
  final InMemoryQuestRepository _questRepository;
  final InMemoryAchievementRepository _achievementRepository;
  
  // Cache for performance
  final Map<String, List<LeaderboardEntry>> _entriesCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  InMemoryLeaderboardRepository({
    required InMemoryUserRepository userRepository,
    required InMemoryQuestRepository questRepository,
    required InMemoryAchievementRepository achievementRepository,
  })  : _userRepository = userRepository,
        _questRepository = questRepository,
        _achievementRepository = achievementRepository;

  @override
  Future<Leaderboard> create(Leaderboard entity) async {
    _leaderboards[entity.id] = entity;
    return entity;
  }

  @override
  Future<Leaderboard?> getById(String id) async {
    final leaderboard = _leaderboards[id];
    if (leaderboard == null) return null;
    
    // Refresh entries if needed
    final refreshedLeaderboard = await _refreshLeaderboardEntries(leaderboard);
    return refreshedLeaderboard;
  }

  @override
  Future<List<Leaderboard>> getAll({int page = 1, int pageSize = 20}) async {
    final leaderboards = _leaderboards.values.toList()
      ..sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= leaderboards.length) return [];
    
    final paginatedLeaderboards = leaderboards.sublist(
      startIndex,
      endIndex > leaderboards.length ? leaderboards.length : endIndex,
    );
    
    // Refresh entries for all leaderboards
    final refreshedLeaderboards = <Leaderboard>[];
    for (final leaderboard in paginatedLeaderboards) {
      final refreshed = await _refreshLeaderboardEntries(leaderboard);
      refreshedLeaderboards.add(refreshed);
    }
    
    return refreshedLeaderboards;
  }

  @override
  Future<Leaderboard> update(String id, Leaderboard entity) async {
    if (!_leaderboards.containsKey(id)) {
      throw Exception('Leaderboard not found');
    }
    _leaderboards[id] = entity;
    _invalidateCache(id);
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    _leaderboards.remove(id);
    _invalidateCache(id);
  }

  @override
  Future<bool> exists(String id) async {
    return _leaderboards.containsKey(id);
  }

  @override
  Future<int> count() async {
    return _leaderboards.length;
  }

  @override
  Future<List<Leaderboard>> search(String query, {int page = 1, int pageSize = 20}) async {
    final leaderboards = _leaderboards.values.where((leaderboard) =>
        leaderboard.title.toLowerCase().contains(query.toLowerCase()) ||
        leaderboard.description.toLowerCase().contains(query.toLowerCase())
    ).toList()
      ..sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= leaderboards.length) return [];
    
    return leaderboards.sublist(
      startIndex,
      endIndex > leaderboards.length ? leaderboards.length : endIndex,
    );
  }

  @override
  Future<List<Leaderboard>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    var leaderboards = _leaderboards.values.toList();
    
    // Apply filters
    if (filters.containsKey('type')) {
      final type = LeaderboardType.values.firstWhere(
        (t) => t.name == filters['type'],
        orElse: () => LeaderboardType.global,
      );
      leaderboards = leaderboards.where((leaderboard) => leaderboard.type == type).toList();
    }
    
    if (filters.containsKey('timeframe')) {
      final timeframe = LeaderboardTimeframe.values.firstWhere(
        (t) => t.name == filters['timeframe'],
        orElse: () => LeaderboardTimeframe.allTime,
      );
      leaderboards = leaderboards.where((leaderboard) => leaderboard.timeframe == timeframe).toList();
    }
    
    leaderboards.sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));
    
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= leaderboards.length) return [];
    
    return leaderboards.sublist(
      startIndex,
      endIndex > leaderboards.length ? leaderboards.length : endIndex,
    );
  }

  @override
  Future<List<Leaderboard>> createBatch(List<Leaderboard> entities) async {
    for (final leaderboard in entities) {
      _leaderboards[leaderboard.id] = leaderboard;
    }
    return entities;
  }

  @override
  Future<List<Leaderboard>> updateBatch(List<Leaderboard> entities) async {
    for (final leaderboard in entities) {
      if (_leaderboards.containsKey(leaderboard.id)) {
        _leaderboards[leaderboard.id] = leaderboard;
        _invalidateCache(leaderboard.id);
      }
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      await delete(id);
    }
  }

  @override
  Future<Leaderboard?> getByType(String type) async {
    try {
      final leaderboard = _leaderboards.values.firstWhere(
        (lb) => lb.type.name == type,
      );

      return await _refreshLeaderboardEntries(leaderboard);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<int?> getUserPosition(String leaderboardId, String userId) async {
    final leaderboard = await getById(leaderboardId);
    if (leaderboard == null) return null;
    
    final userEntryIndex = leaderboard.entries.indexWhere((entry) => entry.userId == userId);
    return userEntryIndex >= 0 ? userEntryIndex + 1 : null;
  }

  @override
  Future<List<Map<String, dynamic>>> getTopUsers(String leaderboardId, {int limit = 10}) async {
    final leaderboard = await getById(leaderboardId);
    if (leaderboard == null) return [];
    
    final topEntries = leaderboard.entries.take(limit).toList();
    return topEntries.map((entry) => entry.toJson()).toList();
  }

  @override
  Future<List<Map<String, dynamic>>> getUsersAroundPosition(String leaderboardId, int position, {int range = 5}) async {
    final leaderboard = await getById(leaderboardId);
    if (leaderboard == null) return [];
    
    final startIndex = (position - range - 1).clamp(0, leaderboard.entries.length);
    final endIndex = (position + range).clamp(0, leaderboard.entries.length);
    
    final entries = leaderboard.entries.sublist(startIndex, endIndex);
    return entries.map((entry) => entry.toJson()).toList();
  }

  @override
  Future<void> updateUserScore(String leaderboardId, String userId, int score) async {
    final leaderboard = await getById(leaderboardId);
    if (leaderboard == null) return;
    
    // This would trigger a recalculation of the leaderboard
    _invalidateCache(leaderboardId);
    
    // In a real implementation, this would update the user's score in the database
    // and trigger a leaderboard recalculation
  }

  @override
  Future<void> reset(String leaderboardId) async {
    final leaderboard = _leaderboards[leaderboardId];
    if (leaderboard == null) return;
    
    final resetLeaderboard = Leaderboard(
      id: leaderboard.id,
      title: leaderboard.title,
      description: leaderboard.description,
      type: leaderboard.type,
      timeframe: leaderboard.timeframe,
      entries: [],
      startDate: DateTime.now(),
      endDate: leaderboard.endDate,
      lastUpdated: DateTime.now(),
      totalParticipants: 0,
      metadata: leaderboard.metadata,
    );
    
    _leaderboards[leaderboardId] = resetLeaderboard;
    _invalidateCache(leaderboardId);
  }

  @override
  Future<Map<String, dynamic>> getStats(String leaderboardId) async {
    final leaderboard = await getById(leaderboardId);
    if (leaderboard == null) return {};
    
    final entries = leaderboard.entries;
    if (entries.isEmpty) {
      return {
        'totalParticipants': 0,
        'activeParticipants': 0,
        'averageScore': 0.0,
        'highestScore': 0,
        'lowestScore': 0,
        'lastUpdated': leaderboard.lastUpdated.toIso8601String(),
        'scoreDistribution': <String, int>{},
      };
    }
    
    final scores = entries.map((entry) => entry.score).toList();
    final averageScore = scores.reduce((a, b) => a + b) / scores.length;
    final highestScore = scores.reduce((a, b) => a > b ? a : b);
    final lowestScore = scores.reduce((a, b) => a < b ? a : b);
    
    // Calculate score distribution (simplified)
    final scoreDistribution = <String, int>{};
    for (final score in scores) {
      final range = '${(score ~/ 1000) * 1000}-${((score ~/ 1000) + 1) * 1000}';
      scoreDistribution[range] = (scoreDistribution[range] ?? 0) + 1;
    }
    
    return {
      'totalParticipants': leaderboard.totalParticipants,
      'activeParticipants': entries.length,
      'averageScore': averageScore,
      'highestScore': highestScore,
      'lowestScore': lowestScore,
      'lastUpdated': leaderboard.lastUpdated.toIso8601String(),
      'scoreDistribution': scoreDistribution,
    };
  }

  /// Refresh leaderboard entries with current user data
  Future<Leaderboard> _refreshLeaderboardEntries(Leaderboard leaderboard) async {
    final cacheKey = leaderboard.id;
    final now = DateTime.now();
    
    // Check cache validity
    if (_entriesCache.containsKey(cacheKey) && 
        _cacheTimestamps.containsKey(cacheKey) &&
        now.difference(_cacheTimestamps[cacheKey]!).compareTo(_cacheExpiry) < 0) {
      return Leaderboard(
        id: leaderboard.id,
        title: leaderboard.title,
        description: leaderboard.description,
        type: leaderboard.type,
        timeframe: leaderboard.timeframe,
        entries: _entriesCache[cacheKey]!,
        startDate: leaderboard.startDate,
        endDate: leaderboard.endDate,
        lastUpdated: now,
        totalParticipants: _entriesCache[cacheKey]!.length,
        currentUserEntry: leaderboard.currentUserEntry,
        metadata: leaderboard.metadata,
      );
    }
    
    // Generate fresh entries
    final entries = await _generateLeaderboardEntries(leaderboard);
    
    // Update cache
    _entriesCache[cacheKey] = entries;
    _cacheTimestamps[cacheKey] = now;
    
    return Leaderboard(
      id: leaderboard.id,
      title: leaderboard.title,
      description: leaderboard.description,
      type: leaderboard.type,
      timeframe: leaderboard.timeframe,
      entries: entries,
      startDate: leaderboard.startDate,
      endDate: leaderboard.endDate,
      lastUpdated: now,
      totalParticipants: entries.length,
      currentUserEntry: leaderboard.currentUserEntry,
      metadata: leaderboard.metadata,
    );
  }

  /// Generate leaderboard entries based on current user data
  Future<List<LeaderboardEntry>> _generateLeaderboardEntries(Leaderboard leaderboard) async {
    final allUsers = await _userRepository.getAll();
    final entries = <LeaderboardEntry>[];
    
    for (final user in allUsers) {
      // Get user's quest completion count
      final userQuests = await _questRepository.getAssignedToUser(user.id);
      final completedQuests = userQuests.where((q) => q.status == QuestStatus.completed).length;
      
      // Get user's achievement count
      final userAchievements = await _achievementRepository.getAllUserAchievements(user.id);
      final unlockedAchievements = userAchievements.where((ua) => ua.status == AchievementStatus.unlocked).length;
      
      // Calculate score based on leaderboard type
      int score;
      switch (leaderboard.type) {
        case LeaderboardType.global:
          score = user.xp; // Global leaderboard based on XP
          break;
        case LeaderboardType.category:
          // Category-specific scoring (would be based on metadata)
          score = completedQuests * 100; // Example: quest completion score
          break;
        default:
          score = user.xp;
      }
      
      final entry = LeaderboardEntry(
        userId: user.id,
        username: user.username,
        displayName: '${user.firstName} ${user.lastName}'.trim(),
        avatarUrl: user.avatarUrl,
        rank: 0, // Will be set after sorting
        score: score,
        level: user.level,
        xp: user.xp,
        questsCompleted: completedQuests,
        achievements: unlockedAchievements,
        lastActive: user.lastLoginAt ?? user.createdAt,
      );
      
      entries.add(entry);
    }
    
    // Sort by score (descending) and assign ranks
    entries.sort((a, b) => b.score.compareTo(a.score));
    
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      entries[i] = LeaderboardEntry(
        userId: entry.userId,
        username: entry.username,
        displayName: entry.displayName,
        avatarUrl: entry.avatarUrl,
        rank: i + 1,
        score: entry.score,
        level: entry.level,
        xp: entry.xp,
        questsCompleted: entry.questsCompleted,
        achievements: entry.achievements,
        lastActive: entry.lastActive,
        metadata: entry.metadata,
      );
    }
    
    return entries;
  }

  /// Invalidate cache for a leaderboard
  void _invalidateCache(String leaderboardId) {
    _entriesCache.remove(leaderboardId);
    _cacheTimestamps.remove(leaderboardId);
  }

  /// Initialize with sample leaderboards
  Future<void> initializeSampleLeaderboards() async {
    final now = DateTime.now();
    
    final sampleLeaderboards = [
      Leaderboard(
        id: 'global_xp_all_time',
        title: 'Global XP Leaderboard',
        description: 'Top users by total XP earned',
        type: LeaderboardType.global,
        timeframe: LeaderboardTimeframe.allTime,
        entries: [],
        startDate: now.subtract(const Duration(days: 365)),
        endDate: now.add(const Duration(days: 365)),
        lastUpdated: now,
        totalParticipants: 0,
      ),
      Leaderboard(
        id: 'weekly_quests',
        title: 'Weekly Quest Champions',
        description: 'Top quest completers this week',
        type: LeaderboardType.category,
        timeframe: LeaderboardTimeframe.weekly,
        entries: [],
        startDate: now.subtract(const Duration(days: 7)),
        endDate: now,
        lastUpdated: now,
        totalParticipants: 0,
        metadata: {'category': 'quests'},
      ),
      Leaderboard(
        id: 'monthly_achievements',
        title: 'Monthly Achievement Masters',
        description: 'Top achievement earners this month',
        type: LeaderboardType.category,
        timeframe: LeaderboardTimeframe.monthly,
        entries: [],
        startDate: now.subtract(const Duration(days: 30)),
        endDate: now,
        lastUpdated: now,
        totalParticipants: 0,
        metadata: {'category': 'achievements'},
      ),
    ];

    for (final leaderboard in sampleLeaderboards) {
      await create(leaderboard);
    }
  }
}
