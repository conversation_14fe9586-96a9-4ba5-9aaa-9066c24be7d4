import 'package:test/test.dart';

// Import all test files
import 'services/auth_service_test.dart' as auth_tests;
import 'services/user_repository_test.dart' as user_repo_tests;
import 'services/error_handler_test.dart' as error_handler_tests;
import 'integration/server_integration_test.dart' as integration_tests;

/// Test runner for all server tests
/// 
/// This file imports and runs all test suites for the Quester server.
/// It provides a centralized way to execute all tests and generate
/// comprehensive test reports.
void main() {
  group('Quester Server Test Suite', () {
    group('🔐 Authentication Service Tests', () {
      auth_tests.main();
    });

    group('👥 User Repository Tests', () {
      user_repo_tests.main();
    });

    group('🚨 Error Handler Tests', () {
      error_handler_tests.main();
    });

    group('🔗 Integration Tests', () {
      integration_tests.main();
    });
  });
}

/// Test configuration and utilities
class TestConfig {
  static const bool enableVerboseLogging = false;
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const int maxConcurrentTests = 10;

  /// Setup test environment
  static void setupTestEnvironment() {
    // Configure test environment settings
    if (enableVerboseLogging) {
      print('🧪 Setting up test environment...');
      print('📊 Test timeout: $defaultTimeout');
      print('🔄 Max concurrent tests: $maxConcurrentTests');
    }
  }

  /// Cleanup test environment
  static void cleanupTestEnvironment() {
    if (enableVerboseLogging) {
      print('🧹 Cleaning up test environment...');
    }
  }
}

/// Test statistics and reporting
class TestReporter {
  static int _totalTests = 0;
  static int _passedTests = 0;
  static int _failedTests = 0;
  static final List<String> _failedTestNames = [];

  /// Record test result
  static void recordTest(String testName, bool passed) {
    _totalTests++;
    if (passed) {
      _passedTests++;
    } else {
      _failedTests++;
      _failedTestNames.add(testName);
    }
  }

  /// Generate test report
  static String generateReport() {
    final buffer = StringBuffer();
    buffer.writeln('📊 Test Report');
    buffer.writeln('=' * 50);
    buffer.writeln('Total Tests: $_totalTests');
    buffer.writeln('Passed: $_passedTests');
    buffer.writeln('Failed: $_failedTests');
    buffer.writeln('Success Rate: ${(_passedTests / _totalTests * 100).toStringAsFixed(1)}%');
    
    if (_failedTestNames.isNotEmpty) {
      buffer.writeln('\n❌ Failed Tests:');
      for (final testName in _failedTestNames) {
        buffer.writeln('  - $testName');
      }
    }
    
    return buffer.toString();
  }

  /// Reset statistics
  static void reset() {
    _totalTests = 0;
    _passedTests = 0;
    _failedTests = 0;
    _failedTestNames.clear();
  }
}

/// Test utilities for common test operations
class TestHelpers {
  /// Run a test with timeout
  static Future<T> withTimeout<T>(
    Future<T> future, {
    Duration? timeout,
  }) async {
    return await future.timeout(
      timeout ?? TestConfig.defaultTimeout,
      onTimeout: () => throw TimeoutException(
        'Test timed out after ${timeout ?? TestConfig.defaultTimeout}',
        timeout ?? TestConfig.defaultTimeout,
      ),
    );
  }

  /// Run multiple tests concurrently
  static Future<List<T>> runConcurrently<T>(
    List<Future<T>> futures, {
    int? maxConcurrency,
  }) async {
    final concurrency = maxConcurrency ?? TestConfig.maxConcurrentTests;
    final results = <T>[];
    
    for (int i = 0; i < futures.length; i += concurrency) {
      final batch = futures.skip(i).take(concurrency);
      final batchResults = await Future.wait(batch);
      results.addAll(batchResults);
    }
    
    return results;
  }

  /// Measure test execution time
  static Future<TestResult<T>> measureExecutionTime<T>(
    Future<T> future,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await future;
      stopwatch.stop();
      return TestResult.success(result, stopwatch.elapsed);
    } catch (error) {
      stopwatch.stop();
      return TestResult.failure(error, stopwatch.elapsed);
    }
  }

  /// Create test data cleanup function
  static Future<void> Function() createCleanupFunction(
    List<Future<void> Function()> cleanupTasks,
  ) {
    return () async {
      for (final task in cleanupTasks) {
        try {
          await task();
        } catch (error) {
          print('⚠️ Cleanup task failed: $error');
        }
      }
    };
  }
}

/// Test result wrapper
class TestResult<T> {
  final T? result;
  final dynamic error;
  final Duration executionTime;
  final bool isSuccess;

  const TestResult.success(this.result, this.executionTime)
      : error = null,
        isSuccess = true;

  const TestResult.failure(this.error, this.executionTime)
      : result = null,
        isSuccess = false;
}

/// Custom test exception
class TestException implements Exception {
  final String message;
  final dynamic cause;

  const TestException(this.message, [this.cause]);

  @override
  String toString() {
    if (cause != null) {
      return 'TestException: $message (Caused by: $cause)';
    }
    return 'TestException: $message';
  }
}

/// Test timeout exception
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message';
}

/// Test data builder for creating consistent test data
class TestDataBuilder {
  static const List<String> _sampleUsernames = [
    'alice_wonder', 'bob_builder', 'charlie_brown', 'diana_prince',
    'edward_cullen', 'fiona_shrek', 'george_jungle', 'helen_troy',
  ];

  static const List<String> _sampleEmails = [
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
  ];

  static const List<String> _sampleQuestTitles = [
    'Complete Daily Exercise', 'Read 30 Minutes', 'Learn New Skill',
    'Help a Friend', 'Organize Workspace', 'Practice Meditation',
  ];

  /// Get random sample username
  static String getRandomUsername() {
    final index = DateTime.now().millisecondsSinceEpoch % _sampleUsernames.length;
    return _sampleUsernames[index];
  }

  /// Get random sample email
  static String getRandomEmail() {
    final index = DateTime.now().millisecondsSinceEpoch % _sampleEmails.length;
    return _sampleEmails[index];
  }

  /// Get random sample quest title
  static String getRandomQuestTitle() {
    final index = DateTime.now().millisecondsSinceEpoch % _sampleQuestTitles.length;
    return _sampleQuestTitles[index];
  }

  /// Generate unique test identifier
  static String generateTestId([String? prefix]) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp % 10000;
    return '${prefix ?? 'test'}_${timestamp}_$random';
  }
}

/// Test environment setup and teardown
class TestEnvironment {
  static bool _isSetup = false;

  /// Setup test environment
  static Future<void> setup() async {
    if (_isSetup) return;

    TestConfig.setupTestEnvironment();
    TestReporter.reset();
    
    // Additional setup tasks can be added here
    // e.g., database initialization, mock server setup, etc.
    
    _isSetup = true;
    
    if (TestConfig.enableVerboseLogging) {
      print('✅ Test environment setup complete');
    }
  }

  /// Teardown test environment
  static Future<void> teardown() async {
    if (!_isSetup) return;

    TestConfig.cleanupTestEnvironment();
    
    // Additional cleanup tasks can be added here
    // e.g., database cleanup, mock server shutdown, etc.
    
    _isSetup = false;
    
    if (TestConfig.enableVerboseLogging) {
      print('✅ Test environment teardown complete');
      print(TestReporter.generateReport());
    }
  }
}
