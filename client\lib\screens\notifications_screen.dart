import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart' as shared;

import '../providers/notification_cubit.dart';

/// Notifications screen showing all user notifications
class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Notifications',
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              const Spacer(),
              BlocBuilder<NotificationCubit, NotificationState>(
                builder: (context, state) {
                  final hasUnread = context.read<NotificationCubit>().unreadCount > 0;
                  return TextButton.icon(
                    onPressed: hasUnread
                        ? () => context.read<NotificationCubit>().markAllAsRead()
                        : null,
                    icon: const Icon(Icons.done_all),
                    label: const Text('Mark all read'),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Notifications list
          Expanded(
            child: BlocBuilder<NotificationCubit, NotificationState>(
              builder: (context, state) {
                if (state is NotificationLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }
                
                if (state is NotificationError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading notifications',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          state.message,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => context.read<NotificationCubit>().refreshNotifications(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }
                
                if (state is NotificationLoaded) {
                  final notifications = state.notifications;
                  
                  if (notifications.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.notifications_none,
                            size: 64,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No notifications',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'You\'re all caught up!',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  
                  return RefreshIndicator(
                    onRefresh: () => context.read<NotificationCubit>().refreshNotifications(),
                    child: ListView.builder(
                      itemCount: notifications.length,
                      itemBuilder: (context, index) {
                        return _NotificationCard(
                          notification: notifications[index],
                          onTap: () {
                            if (!notifications[index].isRead) {
                              context.read<NotificationCubit>().markAsRead(notifications[index].id);
                            }
                          },
                          onDismiss: () {
                            context.read<NotificationCubit>().deleteNotification(notifications[index].id);
                          },
                        );
                      },
                    ),
                  );
                }
                
                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _NotificationCard extends StatelessWidget {
  final shared.Notification notification;
  final VoidCallback onTap;
  final VoidCallback onDismiss;

  const _NotificationCard({
    required this.notification,
    required this.onTap,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onDismiss(),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        color: Theme.of(context).colorScheme.error,
        child: Icon(
          Icons.delete,
          color: Theme.of(context).colorScheme.onError,
        ),
      ),
      child: Card(
        margin: const EdgeInsets.only(bottom: 8),
        color: notification.isRead 
            ? null 
            : Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Notification icon
                CircleAvatar(
                  radius: 20,
                  backgroundColor: _getNotificationColor(notification.type).withValues(alpha: 0.1),
                  child: Icon(
                    _getNotificationIcon(notification.type),
                    color: _getNotificationColor(notification.type),
                    size: 20,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Notification content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification.title,
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w600,
                              ),
                            ),
                          ),
                          if (!notification.isRead)
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        notification.message,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _formatTime(notification.createdAt),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(shared.NotificationType type) {
    switch (type) {
      case shared.NotificationType.questComplete:
        return Icons.check_circle;
      case shared.NotificationType.questAssigned:
        return Icons.assignment;
      case shared.NotificationType.achievementUnlocked:
        return Icons.emoji_events;
      case shared.NotificationType.levelUp:
        return Icons.star;
      case shared.NotificationType.friendRequest:
        return Icons.person_add;
      case shared.NotificationType.system:
        return Icons.info;
      case shared.NotificationType.success:
        return Icons.check_circle;
      case shared.NotificationType.warning:
        return Icons.warning;
      case shared.NotificationType.error:
        return Icons.error;
      case shared.NotificationType.info:
        return Icons.info;
    }
  }

  Color _getNotificationColor(shared.NotificationType type) {
    switch (type) {
      case shared.NotificationType.questComplete:
      case shared.NotificationType.success:
        return Colors.green;
      case shared.NotificationType.questAssigned:
      case shared.NotificationType.info:
        return Colors.blue;
      case shared.NotificationType.achievementUnlocked:
      case shared.NotificationType.levelUp:
        return Colors.orange;
      case shared.NotificationType.warning:
        return Colors.orange;
      case shared.NotificationType.error:
        return Colors.red;
      case shared.NotificationType.friendRequest:
        return Colors.purple;
      case shared.NotificationType.system:
        return Colors.grey;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
