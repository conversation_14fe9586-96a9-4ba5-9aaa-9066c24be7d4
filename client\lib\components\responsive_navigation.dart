import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../layouts/main_layout.dart';
import '../providers/notification_cubit.dart';
import '../components/notification_badge.dart';

/// Responsive navigation component that adapts to screen size
class ResponsiveNavigation extends StatelessWidget {
  final bool isExpanded;
  final bool isVertical;

  const ResponsiveNavigation({
    super.key,
    required this.isExpanded,
    required this.isVertical,
  });

  @override
  Widget build(BuildContext context) {
    if (isVertical) {
      return _VerticalNavigation(isExpanded: isExpanded);
    } else {
      return _BottomNavigation();
    }
  }
}

class _VerticalNavigation extends StatelessWidget {
  final bool isExpanded;

  const _VerticalNavigation({required this.isExpanded});

  @override
  Widget build(BuildContext context) {
    final currentIndex = NavigationItems.getCurrentIndex(context);

    return NavigationRail(
      extended: isExpanded,
      selectedIndex: currentIndex,
      onDestinationSelected: (index) {
        NavigationItems.navigateToIndex(context, index);
      },
      destinations: NavigationItems.items.map((item) {
        return NavigationRailDestination(
          icon: _buildIcon(context, item, false),
          selectedIcon: _buildIcon(context, item, true),
          label: Text(item.label),
        );
      }).toList(),
    );
  }

  Widget _buildIcon(BuildContext context, NavigationItem item, bool isSelected) {
    final icon = Icon(isSelected ? (item.selectedIcon ?? item.icon) : item.icon);
    
    // Add notification badge for notifications tab
    if (item.route == '/notifications') {
      return BlocBuilder<NotificationCubit, NotificationState>(
        builder: (context, state) {
          final unreadCount = context.read<NotificationCubit>().unreadCount;
          return NotificationBadge(
            count: unreadCount,
            size: 16,
            child: icon,
          );
        },
      );
    }
    
    return icon;
  }
}

class _BottomNavigation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final currentIndex = NavigationItems.getCurrentIndex(context);

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: (index) {
        NavigationItems.navigateToIndex(context, index);
      },
      items: NavigationItems.items.map((item) {
        return BottomNavigationBarItem(
          icon: _buildIcon(context, item, false),
          activeIcon: _buildIcon(context, item, true),
          label: item.label,
        );
      }).toList(),
    );
  }

  Widget _buildIcon(BuildContext context, NavigationItem item, bool isSelected) {
    final icon = Icon(isSelected ? (item.selectedIcon ?? item.icon) : item.icon);
    
    // Add notification badge for notifications tab
    if (item.route == '/notifications') {
      return BlocBuilder<NotificationCubit, NotificationState>(
        builder: (context, state) {
          final unreadCount = context.read<NotificationCubit>().unreadCount;
          return NotificationBadge(
            count: unreadCount,
            size: 16,
            child: icon,
          );
        },
      );
    }
    
    return icon;
  }
}
