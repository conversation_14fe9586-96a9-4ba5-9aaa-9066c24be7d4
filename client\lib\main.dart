import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Core imports
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/constants/app_constants.dart';
import 'components/responsive_builder.dart' as responsive;

// Providers/Cubits
import 'providers/theme_cubit.dart';
import 'providers/auth_cubit.dart';
import 'providers/notification_cubit.dart';
import 'providers/websocket_cubit.dart';

// Services
import 'services/api_service.dart';
import 'services/websocket_service.dart';

/// Main entry point for the Quester Flutter application
///
/// Initializes the app with comprehensive setup including:
/// - Local storage initialization
/// - System UI configuration
/// - Orientation settings
/// - Universal responsive framework
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize local storage
  await _initializeStorage();

  // Configure system UI for universal platform support
  await _configureSystemUI();

  // Set preferred orientations for responsive design
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  runApp(const QuesterApp());
}

/// Initialize local storage systems
Future<void> _initializeStorage() async {
  try {
    // Initialize SharedPreferences for app settings
    await SharedPreferences.getInstance();

    // Initialize Hive for complex data storage (if available)
    // await Hive.initFlutter();
  } catch (e) {
    debugPrint('Storage initialization warning: $e');
  }
}

/// Configure system UI for universal platform support
Future<void> _configureSystemUI() async {
  // Set system UI overlay style with adaptive theming
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
}

/// Universal Quester Application
///
/// Provides comprehensive BLoC providers and Material 3 theming
/// for the entire application with universal responsive design
class QuesterApp extends StatelessWidget {
  const QuesterApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => ThemeCubit()),
        BlocProvider(create: (_) => AuthCubit(
          apiService: ApiService(),
          webSocketService: WebSocketService(),
        )),
        BlocProvider(create: (_) => NotificationCubit()),
        BlocProvider(create: (_) => WebSocketCubit()),
      ],
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp.router(
            title: ClientConstants.appName,
            debugShowCheckedModeBanner: false,

            // Material 3 theming
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeState.themeMode,

            // Router configuration
            routerConfig: AppRouter.router,

            // Localization support
            supportedLocales: const [
              Locale('en', 'US'),
              Locale('es', 'ES'),
              Locale('fr', 'FR'),
            ],
          );
        },
      ),
    );
  }
}
