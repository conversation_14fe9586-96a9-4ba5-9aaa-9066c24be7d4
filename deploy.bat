@echo off
REM Quester Application Build and Deployment Script for Windows
REM Usage: deploy.bat [dev|staging|prod] [build|up|down|restart|logs]

setlocal enabledelayedexpansion

REM Default values
set ENVIRONMENT=%1
set ACTION=%2

if "%ENVIRONMENT%"=="" set ENVIRONMENT=dev
if "%ACTION%"=="" set ACTION=up

REM Validate environment
if "%ENVIRONMENT%"=="dev" goto :valid_env
if "%ENVIRONMENT%"=="development" (
    set ENVIRONMENT=dev
    goto :valid_env
)
if "%ENVIRONMENT%"=="staging" goto :valid_env
if "%ENVIRONMENT%"=="stage" (
    set ENVIRONMENT=staging
    goto :valid_env
)
if "%ENVIRONMENT%"=="prod" goto :valid_env
if "%ENVIRONMENT%"=="production" (
    set ENVIRONMENT=prod
    goto :valid_env
)

echo [ERROR] Invalid environment: %ENVIRONMENT%
echo Valid options: dev, staging, prod
exit /b 1

:valid_env

REM Validate action
if "%ACTION%"=="build" goto :valid_action
if "%ACTION%"=="up" goto :valid_action
if "%ACTION%"=="down" goto :valid_action
if "%ACTION%"=="restart" goto :valid_action
if "%ACTION%"=="logs" goto :valid_action
if "%ACTION%"=="status" goto :valid_action
if "%ACTION%"=="clean" goto :valid_action

echo [ERROR] Invalid action: %ACTION%
echo Valid options: build, up, down, restart, logs, status, clean
exit /b 1

:valid_action

REM Set environment-specific configurations
echo [INFO] Setting up %ENVIRONMENT% environment...

if "%ENVIRONMENT%"=="dev" (
    set COMPOSE_FILES=-f docker-compose.base.yml -f docker-compose.dev.yml
    set ENV_FILE=.env.dev
)
if "%ENVIRONMENT%"=="staging" (
    set COMPOSE_FILES=-f docker-compose.base.yml -f docker-compose.prod.yml
    set ENV_FILE=.env.staging
)
if "%ENVIRONMENT%"=="prod" (
    set COMPOSE_FILES=-f docker-compose.base.yml -f docker-compose.prod.yml
    set ENV_FILE=.env.prod
)

REM Check if environment file exists
if not exist "%ENV_FILE%" (
    echo [ERROR] Environment file %ENV_FILE% not found!
    exit /b 1
)

echo [SUCCESS] Environment configured for %ENVIRONMENT%

REM Execute action
if "%ACTION%"=="build" goto :build_images
if "%ACTION%"=="up" goto :start_services
if "%ACTION%"=="down" goto :stop_services
if "%ACTION%"=="restart" goto :restart_services
if "%ACTION%"=="logs" goto :show_logs
if "%ACTION%"=="status" goto :show_status
if "%ACTION%"=="clean" goto :clean_up

:build_images
echo [INFO] Building Docker images for %ENVIRONMENT%...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% build --no-cache
if %errorlevel% equ 0 (
    echo [SUCCESS] Images built successfully
) else (
    echo [ERROR] Failed to build images
    exit /b 1
)
goto :end

:start_services
echo [INFO] Building and starting services for %ENVIRONMENT%...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% build --no-cache
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% up -d
if %errorlevel% equ 0 (
    echo [SUCCESS] Services started successfully
    goto :show_status
) else (
    echo [ERROR] Failed to start services
    exit /b 1
)
goto :end

:stop_services
echo [INFO] Stopping services for %ENVIRONMENT%...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% down
if %errorlevel% equ 0 (
    echo [SUCCESS] Services stopped successfully
) else (
    echo [ERROR] Failed to stop services
    exit /b 1
)
goto :end

:restart_services
echo [INFO] Restarting services for %ENVIRONMENT%...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% restart
if %errorlevel% equ 0 (
    echo [SUCCESS] Services restarted successfully
    goto :show_status
) else (
    echo [ERROR] Failed to restart services
    exit /b 1
)
goto :end

:show_logs
echo [INFO] Showing logs for %ENVIRONMENT%...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% logs -f --tail=100
goto :end

:show_status
echo [INFO] Service status for %ENVIRONMENT%:
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% ps
echo.
echo [INFO] Health checks:

REM Wait for services to be ready
timeout /t 5 /nobreak >nul

REM Set port based on environment
if "%ENVIRONMENT%"=="dev" (
    set NGINX_PORT=3000
) else (
    set NGINX_PORT=80
)

REM Test nginx health
curl -s -o nul -w "%%{http_code}" http://localhost:%NGINX_PORT%/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] ✓ Nginx is healthy
) else (
    echo [WARNING] ✗ Nginx health check failed
)

REM Test API health
curl -s -o nul -w "%%{http_code}" http://localhost:%NGINX_PORT%/api/health | findstr "200" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] ✓ API is healthy
) else (
    echo [WARNING] ✗ API health check failed
)
goto :end

:clean_up
echo [INFO] Cleaning up Docker resources...
docker-compose %COMPOSE_FILES% --env-file %ENV_FILE% down --volumes --remove-orphans
echo [INFO] Removing application images...
for /f "tokens=3" %%i in ('docker images ^| findstr "quester"') do docker rmi -f %%i
docker system prune -f
echo [SUCCESS] Cleanup completed
goto :end

:end
endlocal
