import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared/shared.dart';

/// API service for HTTP communication with the server
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal() {
    _initializeDio();
  }

  late final Dio _dio;
  String? _authToken;

  // Configuration - Use shared constants
  static const String _baseUrl = ApiConstants.apiBaseUrl;
  static const Duration _connectTimeout = ApiConstants.connectTimeout;
  static const Duration _receiveTimeout = ApiConstants.receiveTimeout;

  /// Initialize Dio HTTP client
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: _connectTimeout,
      receiveTimeout: _receiveTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add request interceptor for authentication
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }
        debugPrint('🌐 ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        debugPrint('✅ ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        debugPrint('❌ ${error.response?.statusCode} ${error.requestOptions.path}: ${error.message}');
        handler.next(error);
      },
    ));
  }

  /// Set authentication token
  void setAuthToken(String? token) {
    _authToken = token;
  }

  /// Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  // Authentication endpoints

  /// Register new user
  Future<ApiResponse<AuthResponse>> register(RegisterRequest request) async {
    try {
      final response = await _dio.post('/auth/register', data: request.toJson());
      return ApiResponse.fromJson(
        response.data,
        (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Login user
  Future<ApiResponse<AuthResponse>> login(LoginRequest request) async {
    try {
      final response = await _dio.post('/auth/login', data: request.toJson());
      return ApiResponse.fromJson(
        response.data,
        (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Refresh authentication token
  Future<ApiResponse<TokenResponse>> refreshToken(String refreshToken) async {
    try {
      final response = await _dio.post('/auth/refresh', data: {
        'refreshToken': refreshToken,
      });
      return ApiResponse.fromJson(
        response.data,
        (data) => TokenResponse.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout(String refreshToken) async {
    try {
      final response = await _dio.post('/auth/logout', data: {
        'refreshToken': refreshToken,
      });
      return ApiResponse<void>.fromJson(response.data, (_) {});
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Get current user
  Future<ApiResponse<User>> getCurrentUser() async {
    try {
      final response = await _dio.get('/auth/me');
      return ApiResponse.fromJson(
        response.data,
        (data) => User.fromJson((data as Map<String, dynamic>)['user']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // User endpoints

  /// Get user by ID
  Future<ApiResponse<User>> getUser(String userId) async {
    try {
      final response = await _dio.get('/users/$userId');
      return ApiResponse.fromJson(
        response.data,
        (data) => User.fromJson((data as Map<String, dynamic>)['user']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Update user
  Future<ApiResponse<User>> updateUser(String userId, UpdateUserRequest request) async {
    try {
      final response = await _dio.put('/users/$userId', data: request.toJson());
      return ApiResponse.fromJson(
        response.data,
        (data) => User.fromJson((data as Map<String, dynamic>)['user']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Get user statistics
  Future<ApiResponse<UserStats>> getUserStats(String userId) async {
    try {
      final response = await _dio.get('/users/$userId/stats');
      return ApiResponse.fromJson(
        response.data,
        (data) => UserStats.fromJson((data as Map<String, dynamic>)['stats']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Quest endpoints

  /// Get all quests
  Future<ApiResponse<PaginatedResponse<Quest>>> getQuests({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? difficulty,
    String? category,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'pageSize': pageSize,
        if (status != null) 'status': status,
        if (difficulty != null) 'difficulty': difficulty,
        if (category != null) 'category': category,
      };

      final response = await _dio.get('/quests', queryParameters: queryParams);
      return ApiResponse.fromJson(
        response.data,
        (data) => PaginatedResponse.fromJson(
          data as Map<String, dynamic>,
          (questData) => Quest.fromJson(questData as Map<String, dynamic>),
        ),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Get quest by ID
  Future<ApiResponse<Quest>> getQuest(String questId) async {
    try {
      final response = await _dio.get('/quests/$questId');
      return ApiResponse.fromJson(
        response.data,
        (data) => Quest.fromJson((data as Map<String, dynamic>)['quest']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Create new quest
  Future<ApiResponse<Quest>> createQuest(CreateQuestRequest request) async {
    try {
      final response = await _dio.post('/quests', data: request.toJson());
      return ApiResponse.fromJson(
        response.data,
        (data) => Quest.fromJson((data as Map<String, dynamic>)['quest']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Update quest
  Future<ApiResponse<Quest>> updateQuest(String questId, UpdateQuestRequest request) async {
    try {
      final response = await _dio.put('/quests/$questId', data: request.toJson());
      return ApiResponse.fromJson(
        response.data,
        (data) => Quest.fromJson((data as Map<String, dynamic>)['quest']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Delete quest
  Future<ApiResponse<void>> deleteQuest(String questId) async {
    try {
      final response = await _dio.delete('/quests/$questId');
      return ApiResponse<void>.fromJson(response.data, (_) {});
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Assign quest to user
  Future<ApiResponse<QuestAssignment>> assignQuest(String questId, String userId) async {
    try {
      final response = await _dio.post('/quests/$questId/assign', data: {
        'userId': userId,
      });
      return ApiResponse.fromJson(
        response.data,
        (data) => QuestAssignment.fromJson((data as Map<String, dynamic>)['assignment']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Complete quest
  Future<ApiResponse<QuestCompletion>> completeQuest(String questId, [String? userId]) async {
    try {
      final requestData = <String, dynamic>{};
      if (userId != null) requestData['userId'] = userId;

      final response = await _dio.post('/quests/$questId/complete', data: requestData);
      return ApiResponse.fromJson(
        response.data,
        (data) => QuestCompletion.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Get user's quests
  Future<ApiResponse<List<QuestAssignment>>> getUserQuests(String userId, {String? status}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (status != null) queryParams['status'] = status;

      final response = await _dio.get('/quests/user/$userId', queryParameters: queryParams);
      return ApiResponse.fromJson(
        response.data,
        (data) => (data as Map<String, dynamic>)['assignments']
            .map<QuestAssignment>((assignment) => QuestAssignment.fromJson(assignment))
            .toList(),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Notification endpoints

  /// Get user notifications
  Future<ApiResponse<PaginatedResponse<Notification>>> getNotifications({
    int page = 1,
    int pageSize = 20,
    bool? isRead,
    String? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'pageSize': pageSize,
        if (isRead != null) 'isRead': isRead,
        if (type != null) 'type': type,
      };

      final response = await _dio.get('/notifications', queryParameters: queryParams);
      return ApiResponse.fromJson(
        response.data,
        (data) => PaginatedResponse.fromJson(
          data as Map<String, dynamic>,
          (notificationData) => Notification.fromJson(notificationData as Map<String, dynamic>),
        ),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Mark notification as read
  Future<ApiResponse<Notification>> markNotificationAsRead(String notificationId) async {
    try {
      final response = await _dio.patch('/notifications/$notificationId/read');
      return ApiResponse.fromJson(
        response.data,
        (data) => Notification.fromJson((data as Map<String, dynamic>)['notification']),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Mark all notifications as read
  Future<ApiResponse<void>> markAllNotificationsAsRead() async {
    try {
      final response = await _dio.patch('/notifications/read-all');
      return ApiResponse<void>.fromJson(response.data, (_) {});
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Delete notification
  Future<ApiResponse<void>> deleteNotification(String notificationId) async {
    try {
      final response = await _dio.delete('/notifications/$notificationId');
      return ApiResponse<void>.fromJson(response.data, (_) {});
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Dashboard endpoints

  /// Get dashboard data
  Future<ApiResponse<DashboardStats>> getDashboard() async {
    try {
      final response = await _dio.get('/dashboard');
      return ApiResponse.fromJson(
        response.data,
        (data) => DashboardStats.fromJson(data as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic HTTP methods for repository compatibility

  /// Generic GET request
  Future<ApiResponse<T>> get<T>(String path, {Map<String, dynamic>? queryParameters, T Function(dynamic)? fromJson}) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      if (fromJson != null) {
        return ApiResponse.fromJson(response.data, fromJson);
      }
      return ApiResponse.fromJson(response.data, (data) => data as T);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Generic POST request
  Future<ApiResponse<T>> post<T>(String path, {dynamic data, T Function(dynamic)? fromJson}) async {
    try {
      final response = await _dio.post(path, data: data);
      if (fromJson != null) {
        return ApiResponse.fromJson(response.data, fromJson);
      }
      return ApiResponse.fromJson(response.data, (data) => data as T);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Generic PUT request
  Future<ApiResponse<T>> put<T>(String path, {dynamic data, T Function(dynamic)? fromJson}) async {
    try {
      final response = await _dio.put(path, data: data);
      if (fromJson != null) {
        return ApiResponse.fromJson(response.data, fromJson);
      }
      return ApiResponse.fromJson(response.data, (data) => data as T);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Generic DELETE request
  Future<ApiResponse<T>> delete<T>(String path, {dynamic data, T Function(dynamic)? fromJson}) async {
    try {
      final response = await _dio.delete(path, data: data);
      if (fromJson != null) {
        return ApiResponse.fromJson(response.data, fromJson);
      }
      return ApiResponse.fromJson(response.data, (data) => data as T);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Generic file upload
  Future<ApiResponse<T>> uploadFile<T>(String path, List<int> fileBytes, String fileName, {T Function(dynamic)? fromJson}) async {
    try {
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(fileBytes, filename: fileName),
      });
      final response = await _dio.post(path, data: formData);
      if (fromJson != null) {
        return ApiResponse.fromJson(response.data, fromJson);
      }
      return ApiResponse.fromJson(response.data, (data) => data as T);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// Handle Dio errors and convert to ApiException
  ApiException _handleDioError(DioException error) {
    if (error.response != null) {
      final statusCode = error.response!.statusCode!;
      final responseData = error.response!.data;
      
      String message = 'Request failed';
      Map<String, dynamic>? errors;
      
      if (responseData is Map<String, dynamic>) {
        message = responseData['message'] ?? message;
        errors = responseData['errors'];
      }
      
      return ApiException.fromResponse(
        statusCode: statusCode,
        message: message,
        errors: errors,
      );
    } else if (error.type == DioExceptionType.connectionTimeout ||
               error.type == DioExceptionType.receiveTimeout) {
      return ApiException.timeout();
    } else if (error.type == DioExceptionType.connectionError) {
      return ApiException.network();
    } else {
      return ApiException.unknown(originalError: error);
    }
  }
}
