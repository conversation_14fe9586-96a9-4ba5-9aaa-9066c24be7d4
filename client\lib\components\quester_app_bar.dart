import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared/shared.dart';

import '../providers/notification_cubit.dart';
import '../components/notification_badge.dart';
import '../core/constants/app_constants.dart';
import '../components/responsive_builder.dart' as responsive;

/// Elevated grey app bar with Quester logo and action icons
class QuesterAppBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onAccountTap;

  const QuesterAppBar({
    super.key,
    this.onAccountTap,
  });

  @override
  Widget build(BuildContext context) {
    return responsive.ResponsiveBuilder(
      builder: (context, deviceType) {
        final isMobile = deviceType == responsive.DeviceType.mobile;
        final isTablet = deviceType == responsive.DeviceType.tablet;

        // Responsive sizing without ScreenUtil for better desktop experience
        final appBarHeight = isMobile ? AppConstants.mobileAppBarHeight : AppConstants.desktopAppBarHeight;
        final logoSize = isMobile ? 20.0 : (isTablet ? 22.0 : 24.0);
        final titleFontSize = isMobile ? 18.0 : (isTablet ? 20.0 : 22.0);
        final iconSize = isMobile ? 20.0 : (isTablet ? 22.0 : 24.0);
        final containerPadding = isMobile ? 3.0 : 4.0;
        final horizontalSpacing = isMobile ? 8.0 : 12.0;
        final actionSpacing = isMobile ? 2.0 : 4.0;
        final endSpacing = isMobile ? 8.0 : 16.0;

        return AppBar(
          automaticallyImplyLeading: false,
          elevation: ClientConstants.appBarElevation,
          // Use theme colors instead of hardcoded colors
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
          surfaceTintColor: Colors.transparent,
          shadowColor: Colors.black26,
          toolbarHeight: appBarHeight,

          title: Row(
            children: [
              // Quester logo and title
              Container(
                padding: EdgeInsets.all(containerPadding),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  Icons.explore,
                  size: logoSize,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: horizontalSpacing),
              Text(
                ClientConstants.appBarTitle,
                style: Theme.of(context).appBarTheme.titleTextStyle?.copyWith(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ) ?? TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).appBarTheme.foregroundColor,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          actions: [
            // Search icon
            Container(
              margin: EdgeInsets.symmetric(horizontal: actionSpacing),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  Icons.search,
                  size: iconSize,
                  color: Theme.of(context).appBarTheme.actionsIconTheme?.color ??
                         Theme.of(context).appBarTheme.foregroundColor,
                ),
                onPressed: () {
                  _showSearchDialog(context);
                },
                tooltip: 'Search',
              ),
            ),

            // Notification icon with badge
            Container(
              margin: EdgeInsets.symmetric(horizontal: actionSpacing),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: BlocBuilder<NotificationCubit, NotificationState>(
                builder: (context, state) {
                  final unreadCount = context.read<NotificationCubit>().unreadCount;

                  return NotificationBadge(
                    count: unreadCount,
                    child: IconButton(
                      icon: Icon(
                        Icons.notifications_outlined,
                        size: iconSize,
                        color: Theme.of(context).appBarTheme.actionsIconTheme?.color ??
                               Theme.of(context).appBarTheme.foregroundColor,
                      ),
                      onPressed: () {
                        context.go('/notifications');
                      },
                      tooltip: 'Notifications',
                    ),
                  );
                },
              ),
            ),

            // Account icon
            Container(
              margin: EdgeInsets.symmetric(horizontal: actionSpacing),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  Icons.account_circle_outlined,
                  size: iconSize,
                  color: Theme.of(context).appBarTheme.actionsIconTheme?.color ??
                         Theme.of(context).appBarTheme.foregroundColor,
                ),
                onPressed: onAccountTap,
                tooltip: 'Account',
              ),
            ),

            SizedBox(width: endSpacing),
          ],
        );
      },
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const _SearchDialog(),
    );
  }

  @override
  Size get preferredSize {
    // Use responsive height based on device type
    final width = WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width /
                  WidgetsBinding.instance.platformDispatcher.views.first.devicePixelRatio;
    final isMobile = width < AppConstants.tabletBreakpoint;
    return Size.fromHeight(isMobile ? AppConstants.mobileAppBarHeight : AppConstants.desktopAppBarHeight);
  }
}

/// Responsive App Layout with Horizontal App Bar and Bottom Navigation
///
/// Provides a complete layout solution with:
/// - Horizontal app bar at the top
/// - Bottom navigation for mobile and tablet
/// - Side navigation rail for desktop
/// - Responsive behavior based on screen size
class QuesterAppLayout extends StatelessWidget {
  final Widget child;
  final List<NavigationDestination>? destinations;
  final int? selectedIndex;
  final ValueChanged<int>? onDestinationSelected;
  final VoidCallback? onAccountTap;
  final Widget? floatingActionButton;

  const QuesterAppLayout({
    super.key,
    required this.child,
    this.destinations,
    this.selectedIndex,
    this.onDestinationSelected,
    this.onAccountTap,
    this.floatingActionButton,
  });

  @override
  Widget build(BuildContext context) {
    return responsive.ResponsiveBuilder(
      builder: (context, deviceType) {
        final isMobile = deviceType == responsive.DeviceType.mobile;
        final isTablet = deviceType == responsive.DeviceType.tablet;

        // For mobile and tablet: use bottom navigation
        if (isMobile || isTablet) {
          return _buildMobileTabletLayout(context);
        }

        // For desktop: use side navigation rail
        return _buildDesktopLayout(context);
      },
    );
  }

  /// Build layout for mobile and tablet with bottom navigation
  Widget _buildMobileTabletLayout(BuildContext context) {
    return Scaffold(
      appBar: QuesterAppBar(onAccountTap: onAccountTap),
      body: child,
      bottomNavigationBar: destinations != null ? _buildBottomNavigation(context) : null,
      floatingActionButton: floatingActionButton,
    );
  }

  /// Build layout for desktop with side navigation rail
  Widget _buildDesktopLayout(BuildContext context) {
    return Scaffold(
      appBar: QuesterAppBar(onAccountTap: onAccountTap),
      body: Row(
        children: [
          if (destinations != null) _buildNavigationRail(context),
          Expanded(child: child),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  /// Build bottom navigation bar for mobile and tablet
  Widget _buildBottomNavigation(BuildContext context) {
    return responsive.ResponsiveBuilder(
      builder: (context, deviceType) {
        final isMobile = deviceType == responsive.DeviceType.mobile;

        return NavigationBar(
          selectedIndex: selectedIndex ?? 0,
          onDestinationSelected: onDestinationSelected,
          destinations: destinations!,
          height: isMobile ? 70.0 : 80.0,
          elevation: 8.0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          indicatorColor: Theme.of(context).colorScheme.primaryContainer,
          labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        );
      },
    );
  }

  /// Build navigation rail for desktop
  Widget _buildNavigationRail(BuildContext context) {
    return NavigationRail(
      selectedIndex: selectedIndex ?? 0,
      onDestinationSelected: onDestinationSelected,
      destinations: destinations!
          .map((dest) => NavigationRailDestination(
                icon: dest.icon,
                selectedIcon: dest.selectedIcon,
                label: Text(dest.label),
              ))
          .toList(),
      extended: true,
      minWidth: 80.0,
      minExtendedWidth: 200.0,
      elevation: 4.0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      indicatorColor: Theme.of(context).colorScheme.primaryContainer,
      labelType: NavigationRailLabelType.none,
    );
  }
}

class _SearchDialog extends StatefulWidget {
  const _SearchDialog();

  @override
  State<_SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<_SearchDialog> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus the search field when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search header
            Row(
              children: [
                Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Search',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Search field
            TextField(
              controller: _searchController,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: 'Search quests, users, or content...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _performSearch(value.trim());
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // Quick search suggestions
            if (_searchController.text.isEmpty) ...[
              Text(
                'Quick Search',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _SearchChip(
                    label: 'Active Quests',
                    icon: Icons.assignment,
                    onTap: () => _performSearch('active quests'),
                  ),
                  _SearchChip(
                    label: 'Completed',
                    icon: Icons.check_circle,
                    onTap: () => _performSearch('completed'),
                  ),
                  _SearchChip(
                    label: 'Notifications',
                    icon: Icons.notifications,
                    onTap: () => _performSearch('notifications'),
                  ),
                  _SearchChip(
                    label: 'Profile',
                    icon: Icons.person,
                    onTap: () => _performSearch('profile'),
                  ),
                ],
              ),
            ],
            
            // Search results (placeholder)
            if (_searchController.text.isNotEmpty) ...[
              Text(
                'Search Results',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Search functionality coming soon!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _performSearch(String query) {
    // TODO: Implement actual search functionality
    Navigator.of(context).pop();
    
    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Searching for: $query'),
      ),
    );
  }
}

class _SearchChip extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback onTap;

  const _SearchChip({
    required this.label,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      onPressed: onTap,
    );
  }
}

/// Usage Instructions for QuesterAppLayout
///
/// The QuesterAppLayout provides a responsive layout that automatically adapts
/// the navigation based on screen size:
///
/// **Mobile & Tablet**:
/// - Horizontal app bar at the top
/// - Bottom navigation bar with destinations
///
/// **Desktop**:
/// - Horizontal app bar at the top
/// - Extended side navigation rail
///
/// **Example Usage**:
/// ```dart
/// QuesterAppLayout(
///   destinations: [
///     NavigationDestination(icon: Icon(Icons.home), label: 'Home'),
///     NavigationDestination(icon: Icon(Icons.explore), label: 'Explore'),
///     // ... more destinations
///   ],
///   selectedIndex: currentIndex,
///   onDestinationSelected: (index) => setState(() => currentIndex = index),
///   child: YourPageContent(),
/// )
/// ```
///
/// See `examples/quester_app_layout_example.dart` for a complete implementation.


