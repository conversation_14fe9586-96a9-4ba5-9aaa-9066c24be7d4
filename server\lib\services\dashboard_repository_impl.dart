import 'dart:async';
import 'dart:io';
import 'package:shared/shared.dart';
import 'user_repository_impl.dart';
import 'quest_repository_impl.dart';
import 'notification_repository_impl.dart';

/// In-memory implementation of DashboardRepository for development/testing
class InMemoryDashboardRepository implements DashboardRepository {
  final InMemoryUserRepository _userRepository;
  final InMemoryQuestRepository _questRepository;
  final InMemoryNotificationRepository _notificationRepository;
  
  // Cache for performance metrics
  final Map<String, dynamic> _metricsCache = {};
  DateTime? _lastMetricsUpdate;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  InMemoryDashboardRepository({
    required InMemoryUserRepository userRepository,
    required InMemoryQuestRepository questRepository,
    required InMemoryNotificationRepository notificationRepository,
  })  : _userRepository = userRepository,
        _questRepository = questRepository,
        _notificationRepository = notificationRepository;

  @override
  Future<DashboardStats> getStats() async {
    // Get user statistics
    final totalUsers = await _userRepository.count();
    final activeUsers = (await _userRepository.getActiveUsers()).length;
    
    // Get quest statistics
    final totalQuests = await _questRepository.count();
    final activeQuests = (await _questRepository.getByStatus(QuestStatus.inProgress)).length;
    final completedQuests = (await _questRepository.getByStatus(QuestStatus.completed)).length;
    
    // Calculate total XP earned
    final allUsers = await _userRepository.getAll();
    final totalXpEarned = allUsers.fold<int>(0, (sum, user) => sum + user.xp);
    
    // Calculate average quest completion rate
    final averageQuestCompletion = totalQuests > 0 ? (completedQuests / totalQuests) * 100 : 0.0;
    
    return DashboardStats(
      totalUsers: totalUsers,
      activeUsers: activeUsers,
      totalQuests: totalQuests,
      activeQuests: activeQuests,
      completedQuests: completedQuests,
      totalXpEarned: totalXpEarned,
      averageQuestCompletion: averageQuestCompletion,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  Future<Map<String, dynamic>> getUserDashboard(String userId) async {
    final user = await _userRepository.getById(userId);
    if (user == null) {
      throw Exception('User not found');
    }

    // Get user's quest statistics
    final userQuests = await _questRepository.getAssignedToUser(userId);
    final completedUserQuests = userQuests.where((q) => q.status == QuestStatus.completed).length;
    final activeUserQuests = userQuests.where((q) => q.status == QuestStatus.inProgress).length;
    final failedUserQuests = userQuests.where((q) => q.status == QuestStatus.failed).length;
    
    // Get user's recent activity
    final recentActivity = await _userRepository.getUserActivity(userId, pageSize: 10);
    
    // Get user's notifications
    final unreadNotifications = await _notificationRepository.getUnreadCountForUser(userId);
    
    // Calculate completion rate
    final totalUserQuests = userQuests.length;
    final completionRate = totalUserQuests > 0 ? (completedUserQuests / totalUserQuests) * 100 : 0.0;
    
    // Calculate user rank (simplified - based on XP)
    final allUsers = await _userRepository.getAll();
    allUsers.sort((a, b) => b.xp.compareTo(a.xp));
    final userRank = allUsers.indexWhere((u) => u.id == userId) + 1;
    
    return {
      'user': user.toJson(),
      'stats': {
        'totalQuests': totalUserQuests,
        'completedQuests': completedUserQuests,
        'activeQuests': activeUserQuests,
        'failedQuests': failedUserQuests,
        'completionRate': completionRate,
        'totalXp': user.xp,
        'level': user.level,
        'rank': userRank,
        'unreadNotifications': unreadNotifications,
      },
      'recentActivity': recentActivity.map((activity) => activity.toJson()).toList(),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<List<UserActivity>> getRecentActivity({int page = 1, int pageSize = 20}) async {
    // Collect all user activities from all users
    final allUsers = await _userRepository.getAll();
    final allActivities = <UserActivity>[];
    
    for (final user in allUsers) {
      final userActivities = await _userRepository.getUserActivity(user.id);
      allActivities.addAll(userActivities);
    }
    
    // Sort by timestamp (most recent first)
    allActivities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    // Apply pagination
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= allActivities.length) return [];
    
    return allActivities.sublist(
      startIndex,
      endIndex > allActivities.length ? allActivities.length : endIndex,
    );
  }

  @override
  Future<SystemHealth> getSystemHealth() async {
    final now = DateTime.now();
    
    // Get basic system metrics (simplified for demo)
    final processInfo = ProcessInfo.currentRss;
    final memoryUsage = processInfo / (1024 * 1024); // Convert to MB
    
    // Get connection counts (would be from WebSocket manager in real implementation)
    final activeConnections = 0; // WebSocketConnectionManager.connectionCount;
    
    // Calculate some basic metrics
    final totalUsers = await _userRepository.count();
    final totalQuests = await _questRepository.count();
    final totalNotifications = await _notificationRepository.count();
    
    // Service status checks
    final services = {
      'database': 'healthy',
      'authentication': 'healthy',
      'notifications': 'healthy',
      'websockets': 'healthy',
      'api': 'healthy',
    };
    
    return SystemHealth(
      status: 'healthy',
      timestamp: now,
      cpuUsage: 0.0, // Would need platform-specific implementation
      memoryUsage: memoryUsage,
      activeConnections: activeConnections,
      totalRequests: 0, // Would track in middleware
      averageResponseTime: 0.0, // Would track in middleware
      services: {
        ...services,
        'metrics': {
          'totalUsers': totalUsers,
          'totalQuests': totalQuests,
          'totalNotifications': totalNotifications,
        },
      },
    );
  }

  @override
  Future<Map<String, dynamic>> getAnalytics(DateTime startDate, DateTime endDate) async {
    // Get users created in date range
    final allUsers = await _userRepository.getAll();
    final usersInRange = allUsers.where((user) =>
        user.createdAt.isAfter(startDate) && user.createdAt.isBefore(endDate)).toList();
    
    // Get quests created in date range
    final allQuests = await _questRepository.getAll();
    final questsInRange = allQuests.where((quest) =>
        quest.createdAt.isAfter(startDate) && quest.createdAt.isBefore(endDate)).toList();
    
    // Get notifications in date range
    final allNotifications = await _notificationRepository.getAll();
    final notificationsInRange = allNotifications.where((notification) =>
        notification.createdAt.isAfter(startDate) && notification.createdAt.isBefore(endDate)).toList();
    
    // Calculate daily metrics
    final daysDiff = endDate.difference(startDate).inDays;
    final dailyUserRegistrations = usersInRange.length / (daysDiff > 0 ? daysDiff : 1);
    final dailyQuestCreations = questsInRange.length / (daysDiff > 0 ? daysDiff : 1);
    
    // Quest completion analytics
    final completedQuestsInRange = questsInRange.where((q) => q.status == QuestStatus.completed).length;
    final questCompletionRate = questsInRange.isNotEmpty ? (completedQuestsInRange / questsInRange.length) * 100 : 0.0;
    
    // User engagement metrics
    final activeUsersInRange = usersInRange.where((user) => 
        user.lastLoginAt != null && 
        user.lastLoginAt!.isAfter(startDate) && 
        user.lastLoginAt!.isBefore(endDate)).length;
    
    return {
      'period': {
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'days': daysDiff,
      },
      'users': {
        'newRegistrations': usersInRange.length,
        'dailyAverage': dailyUserRegistrations,
        'activeUsers': activeUsersInRange,
        'engagementRate': usersInRange.isNotEmpty ? (activeUsersInRange / usersInRange.length) * 100 : 0.0,
      },
      'quests': {
        'created': questsInRange.length,
        'completed': completedQuestsInRange,
        'completionRate': questCompletionRate,
        'dailyAverage': dailyQuestCreations,
      },
      'notifications': {
        'sent': notificationsInRange.length,
        'byType': _groupNotificationsByType(notificationsInRange),
      },
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    final now = DateTime.now();
    
    // Check if cache is still valid
    if (_lastMetricsUpdate != null && 
        now.difference(_lastMetricsUpdate!).compareTo(_cacheExpiry) < 0) {
      return _metricsCache;
    }
    
    // Calculate fresh metrics
    final totalUsers = await _userRepository.count();
    final totalQuests = await _questRepository.count();
    final totalNotifications = await _notificationRepository.count();
    
    // Get top performing users
    final allUsers = await _userRepository.getAll();
    allUsers.sort((a, b) => b.xp.compareTo(a.xp));
    final topUsers = allUsers.take(10).map((user) => {
      'id': user.id,
      'username': user.username,
      'xp': user.xp,
      'level': user.level,
    }).toList();
    
    // Get most popular quests
    final popularQuests = await _questRepository.getPopularQuests(pageSize: 10);
    
    // Calculate response time metrics (simplified)
    final metrics = {
      'overview': {
        'totalUsers': totalUsers,
        'totalQuests': totalQuests,
        'totalNotifications': totalNotifications,
        'uptime': '${DateTime.now().difference(DateTime.now().subtract(const Duration(hours: 1))).inMinutes} minutes',
      },
      'performance': {
        'averageResponseTime': 150.0, // ms - would track in middleware
        'requestsPerSecond': 25.0, // would track in middleware
        'errorRate': 0.1, // % - would track in middleware
        'memoryUsage': ProcessInfo.currentRss / (1024 * 1024), // MB
      },
      'topUsers': topUsers,
      'popularQuests': popularQuests.map((quest) => {
        'id': quest.id,
        'title': quest.title,
        'difficulty': quest.difficulty.name,
        'xpReward': quest.xpReward,
      }).toList(),
      'lastUpdated': now.toIso8601String(),
    };
    
    // Update cache
    _metricsCache.clear();
    _metricsCache.addAll(metrics);
    _lastMetricsUpdate = now;
    
    return metrics;
  }

  /// Helper method to group notifications by type
  Map<String, int> _groupNotificationsByType(List<Notification> notifications) {
    final grouped = <String, int>{};
    for (final notification in notifications) {
      final type = notification.type.name;
      grouped[type] = (grouped[type] ?? 0) + 1;
    }
    return grouped;
  }
}
