# Quester Docker Configuration

This document describes the Docker configuration for the Quester project, which follows a client/server/shared architecture pattern.

## Project Structure

```
d:\quester\
├── client/          # Flutter client application
├── server/          # Dart server application  
├── shared/          # Shared Dart package for common code
├── settings/        # Configuration files
│   ├── docker/      # Dockerfiles
│   ├── nginx/       # Nginx configurations
│   └── scripts/     # Setup scripts
└── docker-compose.* # Docker Compose configurations
```

## Architecture Overview

The project uses a multi-package Dart/Flutter architecture:

- **Shared Package** (`d:\quester\shared\`): Contains common models, utilities, and constants
- **Server** (`d:\quester\server\`): Dart Shelf server with API and WebSocket support
- **Client** (`d:\quester\client\`): Flutter web application

## Docker Configuration

### Dockerfiles

#### Server Dockerfile (`settings/docker/Dockerfile.server`)
- Multi-stage build with development and production targets
- Optimized for Dart server applications
- <PERSON><PERSON>ly handles shared package dependencies
- Includes health checks and security best practices

#### Client Dockerfile (`settings/docker/Dockerfile.client`)
- Multi-stage build for Flutter web applications
- Handles Flutter-specific build requirements
- Manages ephemeral files and symlinks (Windows compatibility)
- Production stage uses Nginx for serving static files

### Docker Compose Files

#### Main Configuration (`docker-compose.yml`)
- Base configuration with all services
- MongoDB with admin interface (Mongo Express)
- Redis with admin interface (Redis Commander)
- Server and client services
- Nginx reverse proxy

#### Development (`docker-compose.dev.yml`)
- Extends base configuration for development
- Exposes database ports for development tools
- Volume mounts for hot reload
- Development-specific environment variables

#### Production (`docker-compose.prod.yml`)
- Production-optimized configuration
- Resource limits and security restrictions
- SSL/HTTPS support
- Restricted admin interface access

## Usage

### Development Environment

```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Build and start with forced rebuild
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Start specific services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up mongodb redis server
```

### Production Environment

```bash
# Start production environment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# View logs
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f
```

### Service Access

#### Development
- **Client**: http://localhost:8000
- **Server API**: http://localhost:8080/api
- **Nginx Proxy**: http://localhost:3000
- **MongoDB Admin**: http://localhost:8081
- **Redis Admin**: http://localhost:8082
- **MongoDB Direct**: localhost:27017
- **Redis Direct**: localhost:6379

#### Production
- **Application**: http://localhost (via Nginx)
- **HTTPS**: https://localhost (if SSL configured)

## Environment Variables

Create a `.env` file in the project root:

```env
# Database Configuration
MONGO_USERNAME=admin
MONGO_PASSWORD=your_secure_password
MONGO_DATABASE=quester
MONGO_ADMIN_USERNAME=admin
MONGO_ADMIN_PASSWORD=admin_secure_password

# Redis Configuration
REDIS_PASSWORD=redis_secure_password
REDIS_ADMIN_USERNAME=admin
REDIS_ADMIN_PASSWORD=redis_admin_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_jwt_refresh_secret

# Production Configuration
DOMAIN=your-domain.com
BUILD_TARGET=production
```

## Build Optimization

### .dockerignore Files
- Root `.dockerignore`: General exclusions for all builds
- `client/.dockerignore`: Flutter-specific exclusions
- `server/.dockerignore`: Server-specific exclusions  
- `shared/.dockerignore`: Shared package exclusions

### Build Context Optimization
- Shared package dependencies are resolved first for better caching
- Multi-stage builds minimize final image sizes
- Platform-specific files are excluded appropriately

## Troubleshooting

### Common Issues

1. **Symlink Issues on Windows**: The Dockerfiles include commands to clean up Flutter symlinks
2. **Permission Issues**: Services run as non-root users in production
3. **Hot Reload**: Development volumes are mounted for live code changes

### Debugging

```bash
# View service logs
docker-compose logs [service_name]

# Execute commands in running containers
docker-compose exec server bash
docker-compose exec client bash

# Check service health
docker-compose ps
```

## Security Considerations

- Non-root users in production containers
- Environment variables for sensitive data
- Restricted admin interface access in production
- SSL/HTTPS support for production deployments
- Resource limits to prevent resource exhaustion

## Maintenance

### Updates
```bash
# Pull latest images
docker-compose pull

# Rebuild services
docker-compose build --no-cache

# Clean up unused resources
docker system prune -a
```

### Backups
- MongoDB data is persisted in named volumes
- Backup directory mounted in production configuration
- Redis data persistence enabled
