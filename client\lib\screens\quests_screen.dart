import 'package:flutter/material.dart';

/// Quests screen showing available and active quests
class QuestsScreen extends StatelessWidget {
  const QuestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Quests',
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Implement quest creation
                },
                icon: const Icon(Icons.add),
                label: const Text('New Quest'),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Filter tabs
          DefaultTabController(
            length: 3,
            child: Column(
              children: [
                TabBar(
                  tabs: const [
                    Tab(text: 'Active'),
                    Tab(text: 'Available'),
                    Tab(text: 'Completed'),
                  ],
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                
                const SizedBox(height: 16),
                
                SizedBox(
                  height: 600,
                  child: TabBarView(
                    children: [
                      // Active quests
                      _QuestList(
                        quests: [
                          _QuestData(
                            title: 'Daily Challenge',
                            description: 'Complete 5 tasks today',
                            difficulty: 'Easy',
                            xpReward: 100,
                            progress: 0.6,
                            dueDate: DateTime.now().add(const Duration(hours: 8)),
                            status: 'In Progress',
                          ),
                          _QuestData(
                            title: 'Weekly Adventure',
                            description: 'Explore new features and complete challenges',
                            difficulty: 'Medium',
                            xpReward: 500,
                            progress: 0.3,
                            dueDate: DateTime.now().add(const Duration(days: 3)),
                            status: 'In Progress',
                          ),
                          _QuestData(
                            title: 'Team Collaboration',
                            description: 'Work with team members on shared goals',
                            difficulty: 'Hard',
                            xpReward: 1000,
                            progress: 0.1,
                            dueDate: DateTime.now().add(const Duration(days: 7)),
                            status: 'In Progress',
                          ),
                        ],
                      ),
                      
                      // Available quests
                      _QuestList(
                        quests: [
                          _QuestData(
                            title: 'Learning Path',
                            description: 'Complete the beginner tutorial series',
                            difficulty: 'Easy',
                            xpReward: 200,
                            progress: 0.0,
                            status: 'Available',
                          ),
                          _QuestData(
                            title: 'Social Connect',
                            description: 'Connect with 5 new team members',
                            difficulty: 'Medium',
                            xpReward: 300,
                            progress: 0.0,
                            status: 'Available',
                          ),
                        ],
                      ),
                      
                      // Completed quests
                      _QuestList(
                        quests: [
                          _QuestData(
                            title: 'First Steps',
                            description: 'Complete your profile setup',
                            difficulty: 'Easy',
                            xpReward: 50,
                            progress: 1.0,
                            status: 'Completed',
                            completedDate: DateTime.now().subtract(const Duration(days: 1)),
                          ),
                          _QuestData(
                            title: 'Welcome Quest',
                            description: 'Get familiar with the platform',
                            difficulty: 'Easy',
                            xpReward: 100,
                            progress: 1.0,
                            status: 'Completed',
                            completedDate: DateTime.now().subtract(const Duration(days: 2)),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _QuestList extends StatelessWidget {
  final List<_QuestData> quests;

  const _QuestList({required this.quests});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: quests.length,
      itemBuilder: (context, index) {
        return _QuestCard(quest: quests[index]);
      },
    );
  }
}

class _QuestCard extends StatelessWidget {
  final _QuestData quest;

  const _QuestCard({required this.quest});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        quest.title,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        quest.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                _DifficultyChip(difficulty: quest.difficulty),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress bar (if in progress)
            if (quest.progress > 0 && quest.progress < 1) ...[
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: quest.progress,
                      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${(quest.progress * 100).toInt()}%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            Row(
              children: [
                Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.orange,
                ),
                const SizedBox(width: 4),
                Text(
                  '${quest.xpReward} XP',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                if (quest.dueDate != null) ...[
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Due ${_formatDate(quest.dueDate!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
                
                if (quest.completedDate != null) ...[
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Completed ${_formatDate(quest.completedDate!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                    ),
                  ),
                ],
                
                const Spacer(),
                
                if (quest.status == 'Available')
                  ElevatedButton(
                    onPressed: () {
                      // TODO: Start quest
                    },
                    child: const Text('Start'),
                  ),
                
                if (quest.status == 'In Progress')
                  OutlinedButton(
                    onPressed: () {
                      // TODO: View quest details
                    },
                    child: const Text('Continue'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);
    
    if (difference.inDays > 0) {
      return 'in ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return 'in ${difference.inHours} hours';
    } else if (difference.inDays < 0) {
      return '${-difference.inDays} days ago';
    } else {
      return 'today';
    }
  }
}

class _DifficultyChip extends StatelessWidget {
  final String difficulty;

  const _DifficultyChip({required this.difficulty});

  @override
  Widget build(BuildContext context) {
    Color color;
    switch (difficulty.toLowerCase()) {
      case 'easy':
        color = Colors.green;
        break;
      case 'medium':
        color = Colors.orange;
        break;
      case 'hard':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        difficulty,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color.withValues(alpha: 0.3)),
    );
  }
}

class _QuestData {
  final String title;
  final String description;
  final String difficulty;
  final int xpReward;
  final double progress;
  final String status;
  final DateTime? dueDate;
  final DateTime? completedDate;

  _QuestData({
    required this.title,
    required this.description,
    required this.difficulty,
    required this.xpReward,
    required this.progress,
    required this.status,
    this.dueDate,
    this.completedDate,
  });
}
