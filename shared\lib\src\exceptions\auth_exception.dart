/// Authentication and authorization exception classes
class AuthException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const AuthException({
    required this.message,
    this.code,
    this.details,
  });

  @override
  String toString() {
    final buffer = StringBuffer('AuthException: $message');
    if (code != null) buffer.write(' (Code: $code)');
    return buffer.toString();
  }

  /// Invalid credentials
  factory AuthException.invalidCredentials({
    String message = 'Invalid email or password',
  }) {
    return AuthException(
      message: message,
      code: 'INVALID_CREDENTIALS',
    );
  }

  /// User not found
  factory AuthException.userNotFound({
    String message = 'User not found',
    String? email,
  }) {
    return AuthException(
      message: message,
      code: 'USER_NOT_FOUND',
      details: email != null ? {'email': email} : null,
    );
  }

  /// User already exists
  factory AuthException.userAlreadyExists({
    String message = 'User already exists',
    String? email,
    String? username,
  }) {
    final details = <String, dynamic>{};
    if (email != null) details['email'] = email;
    if (username != null) details['username'] = username;

    return AuthException(
      message: message,
      code: 'USER_ALREADY_EXISTS',
      details: details.isNotEmpty ? details : null,
    );
  }

  /// Account locked
  factory AuthException.accountLocked({
    String message = 'Account is locked due to too many failed login attempts',
    DateTime? unlockTime,
  }) {
    return AuthException(
      message: message,
      code: 'ACCOUNT_LOCKED',
      details: unlockTime != null ? {'unlockTime': unlockTime.toIso8601String()} : null,
    );
  }

  /// Account disabled
  factory AuthException.accountDisabled({
    String message = 'Account has been disabled',
  }) {
    return AuthException(
      message: message,
      code: 'ACCOUNT_DISABLED',
    );
  }

  /// Account not verified
  factory AuthException.accountNotVerified({
    String message = 'Account email has not been verified',
  }) {
    return AuthException(
      message: message,
      code: 'ACCOUNT_NOT_VERIFIED',
    );
  }

  /// Token expired
  factory AuthException.tokenExpired({
    String message = 'Authentication token has expired',
    String? tokenType,
  }) {
    return AuthException(
      message: message,
      code: 'TOKEN_EXPIRED',
      details: tokenType != null ? {'tokenType': tokenType} : null,
    );
  }

  /// Token invalid
  factory AuthException.tokenInvalid({
    String message = 'Authentication token is invalid',
    String? tokenType,
  }) {
    return AuthException(
      message: message,
      code: 'TOKEN_INVALID',
      details: tokenType != null ? {'tokenType': tokenType} : null,
    );
  }

  /// Token missing
  factory AuthException.tokenMissing({
    String message = 'Authentication token is required',
  }) {
    return AuthException(
      message: message,
      code: 'TOKEN_MISSING',
    );
  }

  /// Session expired
  factory AuthException.sessionExpired({
    String message = 'Session has expired',
  }) {
    return AuthException(
      message: message,
      code: 'SESSION_EXPIRED',
    );
  }

  /// Permission denied
  factory AuthException.permissionDenied({
    String message = 'You do not have permission to perform this action',
    String? requiredPermission,
    String? userRole,
  }) {
    final details = <String, dynamic>{};
    if (requiredPermission != null) details['requiredPermission'] = requiredPermission;
    if (userRole != null) details['userRole'] = userRole;

    return AuthException(
      message: message,
      code: 'PERMISSION_DENIED',
      details: details.isNotEmpty ? details : null,
    );
  }

  /// Insufficient privileges
  factory AuthException.insufficientPrivileges({
    String message = 'Insufficient privileges to access this resource',
    String? requiredRole,
    String? userRole,
  }) {
    final details = <String, dynamic>{};
    if (requiredRole != null) details['requiredRole'] = requiredRole;
    if (userRole != null) details['userRole'] = userRole;

    return AuthException(
      message: message,
      code: 'INSUFFICIENT_PRIVILEGES',
      details: details.isNotEmpty ? details : null,
    );
  }

  /// Password reset required
  factory AuthException.passwordResetRequired({
    String message = 'Password reset is required',
  }) {
    return AuthException(
      message: message,
      code: 'PASSWORD_RESET_REQUIRED',
    );
  }

  /// Password expired
  factory AuthException.passwordExpired({
    String message = 'Password has expired and must be changed',
    DateTime? expiredAt,
  }) {
    return AuthException(
      message: message,
      code: 'PASSWORD_EXPIRED',
      details: expiredAt != null ? {'expiredAt': expiredAt.toIso8601String()} : null,
    );
  }

  /// Two-factor authentication required
  factory AuthException.twoFactorRequired({
    String message = 'Two-factor authentication is required',
    String? challengeId,
  }) {
    return AuthException(
      message: message,
      code: 'TWO_FACTOR_REQUIRED',
      details: challengeId != null ? {'challengeId': challengeId} : null,
    );
  }

  /// Invalid two-factor code
  factory AuthException.invalidTwoFactorCode({
    String message = 'Invalid two-factor authentication code',
  }) {
    return AuthException(
      message: message,
      code: 'INVALID_TWO_FACTOR_CODE',
    );
  }

  /// Rate limit exceeded
  factory AuthException.rateLimitExceeded({
    String message = 'Too many authentication attempts. Please try again later.',
    Duration? retryAfter,
  }) {
    return AuthException(
      message: message,
      code: 'RATE_LIMIT_EXCEEDED',
      details: retryAfter != null ? {'retryAfter': retryAfter.inSeconds} : null,
    );
  }

  /// Registration disabled
  factory AuthException.registrationDisabled({
    String message = 'User registration is currently disabled',
  }) {
    return AuthException(
      message: message,
      code: 'REGISTRATION_DISABLED',
    );
  }

  /// Email verification failed
  factory AuthException.emailVerificationFailed({
    String message = 'Email verification failed',
    String? token,
  }) {
    return AuthException(
      message: message,
      code: 'EMAIL_VERIFICATION_FAILED',
      details: token != null ? {'token': token} : null,
    );
  }

  /// Password reset failed
  factory AuthException.passwordResetFailed({
    String message = 'Password reset failed',
    String? token,
  }) {
    return AuthException(
      message: message,
      code: 'PASSWORD_RESET_FAILED',
      details: token != null ? {'token': token} : null,
    );
  }

  /// Social login failed
  factory AuthException.socialLoginFailed({
    String message = 'Social login failed',
    String? provider,
    String? reason,
  }) {
    final details = <String, dynamic>{};
    if (provider != null) details['provider'] = provider;
    if (reason != null) details['reason'] = reason;

    return AuthException(
      message: message,
      code: 'SOCIAL_LOGIN_FAILED',
      details: details.isNotEmpty ? details : null,
    );
  }

  /// Biometric authentication failed
  factory AuthException.biometricFailed({
    String message = 'Biometric authentication failed',
    String? reason,
  }) {
    return AuthException(
      message: message,
      code: 'BIOMETRIC_FAILED',
      details: reason != null ? {'reason': reason} : null,
    );
  }

  /// Device not trusted
  factory AuthException.deviceNotTrusted({
    String message = 'Device is not trusted for this operation',
    String? deviceId,
  }) {
    return AuthException(
      message: message,
      code: 'DEVICE_NOT_TRUSTED',
      details: deviceId != null ? {'deviceId': deviceId} : null,
    );
  }

  /// Check if this is a credentials error
  bool get isCredentialsError => code == 'INVALID_CREDENTIALS';

  /// Check if this is a token error
  bool get isTokenError => code?.contains('TOKEN') == true;

  /// Check if this is a permission error
  bool get isPermissionError => code == 'PERMISSION_DENIED' || code == 'INSUFFICIENT_PRIVILEGES';

  /// Check if this is a rate limit error
  bool get isRateLimitError => code == 'RATE_LIMIT_EXCEEDED';

  /// Check if this is an account issue
  bool get isAccountIssue => [
        'ACCOUNT_LOCKED',
        'ACCOUNT_DISABLED',
        'ACCOUNT_NOT_VERIFIED',
        'PASSWORD_EXPIRED',
        'PASSWORD_RESET_REQUIRED'
      ].contains(code);

  /// Check if this is a two-factor authentication issue
  bool get isTwoFactorIssue => code?.contains('TWO_FACTOR') == true;

  /// Get retry after duration for rate limiting
  Duration? get retryAfter {
    final seconds = details?['retryAfter'] as int?;
    return seconds != null ? Duration(seconds: seconds) : null;
  }

  /// Get unlock time for locked accounts
  DateTime? get unlockTime {
    final timeString = details?['unlockTime'] as String?;
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'code': code,
      'details': details,
    };
  }

  /// Create from map
  factory AuthException.fromMap(Map<String, dynamic> map) {
    return AuthException(
      message: map['message'] as String,
      code: map['code'] as String?,
      details: map['details'] as Map<String, dynamic>?,
    );
  }
}
