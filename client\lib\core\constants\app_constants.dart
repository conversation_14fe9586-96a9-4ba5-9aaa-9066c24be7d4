import 'package:shared/shared.dart';

/// Flutter client-specific constants that extend shared constants
class ClientConstants {
  // Re-export shared constants for convenience
  static const String appName = AppConstants.appName;
  static const String appVersion = AppConstants.appVersion;
  static const String appDescription = AppConstants.appDescription;

  // API Configuration - Use shared constants
  static const String baseUrl = ApiConstants.baseUrl;
  static const String apiVersion = ApiConstants.apiVersion;
  static const String apiBaseUrl = ApiConstants.apiBaseUrl;

  // WebSocket Configuration - Use shared constants
  static const String wsBaseUrl = ApiConstants.wsBaseUrl;
  static const String wsNotificationsUrl = ApiConstants.wsNotificationsUrl;
  static const String wsUpdatesUrl = ApiConstants.wsUpdatesUrl;

  // Storage Keys - Use shared constants
  static const String authTokenKey = AppConstants.authTokenKey;
  static const String refreshTokenKey = AppConstants.refreshTokenKey;
  static const String userDataKey = AppConstants.userDataKey;
  static const String themeKey = AppConstants.themeKey;
  static const String languageKey = AppConstants.languageKey;
  static const String notificationSettingsKey = AppConstants.notificationSettingsKey;

  // Flutter-specific UI Constants (not in shared package)
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  static const double defaultMargin = 16.0;
  static const double smallMargin = 8.0;
  static const double largeMargin = 24.0;

  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  static const double defaultElevation = 4.0;
  static const double smallElevation = 2.0;
  static const double largeElevation = 8.0;

  // App Bar Constants - Use shared where possible
  static const double appBarHeight = AppConstants.mobileAppBarHeight;
  static const double appBarElevation = AppConstants.appBarElevation;
  static const String appBarTitle = AppConstants.appBarTitle;
  static const String appBarLogoPath = AppConstants.appBarLogoPath;
  
  // Navigation Constants
  static const int maxNavigationItems = 5;
  static const double navigationIconSize = 24.0;
  static const double navigationLabelFontSize = 12.0;
  static const double bottomNavigationHeight = 60.0;
  static const double sideNavigationWidth = 280.0;
  
  // Sidebar Constants
  static const double sidebarWidth = 320.0;
  static const double sidebarMinWidth = 280.0;
  static const double sidebarMaxWidth = 400.0;
  static const Duration sidebarAnimationDuration = Duration(milliseconds: 250);
  
  // Notification Constants
  static const int maxNotificationDisplayCount = 5;
  static const Duration notificationAutoHideDuration = Duration(seconds: 5);
  static const double notificationDropdownWidth = 360.0;
  static const double notificationDropdownMaxHeight = 400.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Breakpoints - Use shared constants
  static const double mobileBreakpoint = AppConstants.mobileBreakpoint;
  static const double tabletBreakpoint = AppConstants.tabletBreakpoint;
  static const double desktopBreakpoint = AppConstants.desktopBreakpoint;
  static const double largeDesktopBreakpoint = AppConstants.largeDesktopBreakpoint;

  // Grid Constants - Use shared constants
  static const int mobileGridColumns = AppConstants.mobileGridColumns;
  static const int tabletGridColumns = AppConstants.tabletGridColumns;
  static const int desktopGridColumns = AppConstants.desktopGridColumns;
  static const int largeDesktopGridColumns = AppConstants.desktopGridColumns; // Use desktop for large desktop
  
  // Image Constants
  static const double defaultAvatarSize = 40.0;
  static const double largeAvatarSize = 80.0;
  static const double smallAvatarSize = 24.0;
  
  // Loading Constants
  static const Duration loadingTimeout = Duration(seconds: 30);
  static const Duration refreshTimeout = Duration(seconds: 10);
  
  // Pagination Constants
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation Constants - Use shared constants
  static const int minPasswordLength = ValidationConstants.minPasswordLength;
  static const int maxPasswordLength = ValidationConstants.maxPasswordLength;
  static const int minUsernameLength = ValidationConstants.minUsernameLength;
  static const int maxUsernameLength = ValidationConstants.maxUsernameLength;
  static const int maxBioLength = ValidationConstants.maxBioLength;

  // File Upload Constants - Use shared constants
  static const int maxImageSizeBytes = ApiConstants.maxAvatarSize;
  static const List<String> allowedImageExtensions = ApiConstants.supportedImageFormats;
  
  // Cache Constants
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100;
  
  // Error Messages - Use shared constants
  static const String genericErrorMessage = AppConstants.genericErrorMessage;
  static const String networkErrorMessage = AppConstants.networkErrorMessage;
  static const String timeoutErrorMessage = AppConstants.timeoutErrorMessage;
  static const String unauthorizedErrorMessage = AppConstants.unauthorizedErrorMessage;
  static const String notFoundErrorMessage = AppConstants.notFoundErrorMessage;

  // Success Messages - Use shared constants
  static const String genericSuccessMessage = AppConstants.genericSuccessMessage;
  static const String saveSuccessMessage = AppConstants.saveSuccessMessage;
  static const String deleteSuccessMessage = AppConstants.deleteSuccessMessage;
  static const String updateSuccessMessage = AppConstants.updateSuccessMessage;
  static const String createSuccessMessage = AppConstants.createSuccessMessage;
  
  // Feature Flags - Use shared constants
  static const bool enableDarkMode = AppConstants.enableDarkMode;
  static const bool enableNotifications = AppConstants.enableNotifications;
  static const bool enableWebSocket = AppConstants.enableWebSocket;
  static const bool enableOfflineMode = AppConstants.enableOfflineMode;
  static const bool enableAnalytics = AppConstants.enableAnalytics;

  // Development Constants - Use shared constants
  static bool get isDebugMode => AppConstants.isDebug;
  static const bool enableLogging = AppConstants.enableLogging;
  static const bool enablePerformanceMonitoring = AppConstants.enablePerformanceMonitoring;
  
  // Social Features
  static const int maxFriendsCount = 1000;
  static const int maxGroupSize = 50;
  static const Duration friendRequestExpiration = Duration(days: 30);
  
  // Quest Constants
  static const int maxActiveQuests = 10;
  static const int maxQuestTitleLength = 100;
  static const int maxQuestDescriptionLength = 1000;
  static const Duration questTimeout = Duration(days: 30);
  
  // Achievement Constants
  static const int maxAchievementsPerCategory = 50;
  static const int maxAchievementRequirements = 10;
  static const Duration achievementAnimationDuration = Duration(milliseconds: 800);
  
  // Leaderboard Constants
  static const int defaultLeaderboardSize = 10;
  static const int maxLeaderboardSize = 100;
  static const Duration leaderboardUpdateInterval = Duration(minutes: 5);
  
  // Real-time Update Intervals
  static const Duration dashboardUpdateInterval = Duration(seconds: 30);
  static const Duration notificationCheckInterval = Duration(seconds: 15);
  static const Duration userStatusUpdateInterval = Duration(minutes: 1);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  
  // Color Constants (Hex values)
  static const String primaryColorHex = '#2563EB';
  static const String secondaryColorHex = '#64748B';
  static const String successColorHex = '#059669';
  static const String warningColorHex = '#D97706';
  static const String errorColorHex = '#DC2626';
  static const String greyAppBarColorHex = '#F5F5F5';
  static const String backgroundColorHex = '#FFFFFF';
  static const String surfaceColorHex = '#F8FAFC';
  
  // Font Constants
  static const String primaryFontFamily = 'Inter';
  static const String secondaryFontFamily = 'Roboto';
  static const double baseFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double largeFontSize = 16.0;
  static const double headingFontSize = 24.0;
  
  // Icon Constants
  static const double defaultIconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // Button Constants
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;
  static const double buttonBorderRadius = 8.0;
  
  // Input Field Constants
  static const double inputFieldHeight = 48.0;
  static const double inputFieldBorderRadius = 8.0;
  static const double inputFieldBorderWidth = 1.0;
  
  // Card Constants
  static const double cardBorderRadius = 12.0;
  static const double cardElevation = 2.0;
  static const double cardPadding = 16.0;
  
  // List Constants
  static const double listItemHeight = 72.0;
  static const double listItemPadding = 16.0;
  static const double listItemSpacing = 8.0;
  
  // Dialog Constants
  static const double dialogBorderRadius = 16.0;
  static const double dialogPadding = 24.0;
  static const double dialogMaxWidth = 400.0;
  
  // Snackbar Constants
  static const Duration snackbarDuration = Duration(seconds: 4);
  static const double snackbarBorderRadius = 8.0;
  
  // Progress Indicator Constants
  static const double progressIndicatorSize = 24.0;
  static const double largeProgressIndicatorSize = 48.0;
  static const double progressIndicatorStrokeWidth = 3.0;
  
  // Shimmer Constants
  static const Duration shimmerDuration = Duration(milliseconds: 1500);
  static const double shimmerBorderRadius = 8.0;
  
  // Search Constants
  static const int searchDebounceMs = 300;
  static const int minSearchLength = 2;
  static const int maxSearchResults = 50;
  
  // Refresh Constants
  static const double refreshTriggerDistance = 100.0;
  static const Duration refreshIndicatorDuration = Duration(seconds: 2);
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Theme mode enumeration
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Navigation type enumeration
enum NavigationType {
  bottom,
  side,
  rail,
}

/// Screen size helper class
class ScreenSize {
  static DeviceType getDeviceType(double width) {
    if (width < ClientConstants.mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < ClientConstants.tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < ClientConstants.largeDesktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  static NavigationType getNavigationType(double width) {
    if (width < ClientConstants.tabletBreakpoint) {
      return NavigationType.bottom;
    } else if (width < ClientConstants.desktopBreakpoint) {
      return NavigationType.rail;
    } else {
      return NavigationType.side;
    }
  }

  static int getGridColumns(double width) {
    final deviceType = getDeviceType(width);
    switch (deviceType) {
      case DeviceType.mobile:
        return ClientConstants.mobileGridColumns;
      case DeviceType.tablet:
        return ClientConstants.tabletGridColumns;
      case DeviceType.desktop:
        return ClientConstants.desktopGridColumns;
      case DeviceType.largeDesktop:
        return ClientConstants.largeDesktopGridColumns;
    }
  }
}
