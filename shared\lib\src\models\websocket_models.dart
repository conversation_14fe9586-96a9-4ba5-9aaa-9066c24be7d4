import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'notification.dart';
import 'user.dart';
import 'quest.dart';
import 'dashboard.dart';

part 'websocket_models.g.dart';

/// WebSocket message wrapper for all real-time communications
@JsonSerializable()
class WebSocketMessage extends Equatable {
  final String id;
  final WebSocketMessageType type;
  final String? channel;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? userId;
  final String? sessionId;

  const WebSocketMessage({
    required this.id,
    required this.type,
    this.channel,
    required this.data,
    required this.timestamp,
    this.userId,
    this.sessionId,
  });

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) =>
      _$WebSocketMessageFromJson(json);

  Map<String, dynamic> toJson() => _$WebSocketMessageToJson(this);

  /// Create a notification message
  factory WebSocketMessage.notification({
    required String id,
    required Notification notification,
    String? userId,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.notification,
      channel: 'notifications',
      data: notification.toJson(),
      timestamp: DateTime.now(),
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Create a quest update message
  factory WebSocketMessage.questUpdate({
    required String id,
    required Quest quest,
    String? userId,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.questUpdate,
      channel: 'quests',
      data: quest.toJson(),
      timestamp: DateTime.now(),
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Create a user update message
  factory WebSocketMessage.userUpdate({
    required String id,
    required User user,
    String? userId,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.userUpdate,
      channel: 'users',
      data: user.toJson(),
      timestamp: DateTime.now(),
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Create a dashboard update message
  factory WebSocketMessage.dashboardUpdate({
    required String id,
    required DashboardStats stats,
    String? userId,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.dashboardUpdate,
      channel: 'dashboard',
      data: stats.toJson(),
      timestamp: DateTime.now(),
      userId: userId,
      sessionId: sessionId,
    );
  }

  /// Create a ping message
  factory WebSocketMessage.ping({
    required String id,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.ping,
      data: {'timestamp': DateTime.now().toIso8601String()},
      timestamp: DateTime.now(),
      sessionId: sessionId,
    );
  }

  /// Create a pong message
  factory WebSocketMessage.pong({
    required String id,
    String? sessionId,
  }) {
    return WebSocketMessage(
      id: id,
      type: WebSocketMessageType.pong,
      data: {'timestamp': DateTime.now().toIso8601String()},
      timestamp: DateTime.now(),
      sessionId: sessionId,
    );
  }

  @override
  List<Object?> get props => [id, type, channel, data, timestamp, userId, sessionId];
}

/// WebSocket message types enumeration
enum WebSocketMessageType {
  @JsonValue('ping')
  ping,
  @JsonValue('pong')
  pong,
  @JsonValue('notification')
  notification,
  @JsonValue('quest_update')
  questUpdate,
  @JsonValue('user_update')
  userUpdate,
  @JsonValue('dashboard_update')
  dashboardUpdate,
  @JsonValue('system_message')
  systemMessage,
  @JsonValue('error')
  error,
  @JsonValue('subscribe')
  subscribe,
  @JsonValue('unsubscribe')
  unsubscribe,
  @JsonValue('authentication')
  authentication,
  @JsonValue('heartbeat')
  heartbeat,
}

/// WebSocket connection state model
@JsonSerializable()
class WebSocketConnectionState extends Equatable {
  final ConnectionStatus status;
  final String? error;
  final DateTime? lastConnected;
  final DateTime? lastDisconnected;
  final int reconnectAttempts;
  final Duration? nextReconnectDelay;

  const WebSocketConnectionState({
    required this.status,
    this.error,
    this.lastConnected,
    this.lastDisconnected,
    this.reconnectAttempts = 0,
    this.nextReconnectDelay,
  });

  factory WebSocketConnectionState.fromJson(Map<String, dynamic> json) =>
      _$WebSocketConnectionStateFromJson(json);

  Map<String, dynamic> toJson() => _$WebSocketConnectionStateToJson(this);

  /// Create connected state
  factory WebSocketConnectionState.connected() {
    return WebSocketConnectionState(
      status: ConnectionStatus.connected,
      lastConnected: DateTime.now(),
      reconnectAttempts: 0,
    );
  }

  /// Create disconnected state
  factory WebSocketConnectionState.disconnected({String? error}) {
    return WebSocketConnectionState(
      status: ConnectionStatus.disconnected,
      error: error,
      lastDisconnected: DateTime.now(),
    );
  }

  /// Create connecting state
  factory WebSocketConnectionState.connecting() {
    return const WebSocketConnectionState(
      status: ConnectionStatus.connecting,
    );
  }

  /// Create reconnecting state
  factory WebSocketConnectionState.reconnecting({
    required int attempts,
    Duration? delay,
  }) {
    return WebSocketConnectionState(
      status: ConnectionStatus.reconnecting,
      reconnectAttempts: attempts,
      nextReconnectDelay: delay,
    );
  }

  @override
  List<Object?> get props => [
        status,
        error,
        lastConnected,
        lastDisconnected,
        reconnectAttempts,
        nextReconnectDelay,
      ];
}

/// Connection status enumeration
enum ConnectionStatus {
  @JsonValue('disconnected')
  disconnected,
  @JsonValue('connecting')
  connecting,
  @JsonValue('connected')
  connected,
  @JsonValue('reconnecting')
  reconnecting,
  @JsonValue('error')
  error,
}

/// WebSocket error model
@JsonSerializable()
class WebSocketError extends Equatable {
  final String id;
  final WebSocketErrorType type;
  final String message;
  final String? code;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  const WebSocketError({
    required this.id,
    required this.type,
    required this.message,
    this.code,
    required this.timestamp,
    this.details,
  });

  factory WebSocketError.fromJson(Map<String, dynamic> json) =>
      _$WebSocketErrorFromJson(json);

  Map<String, dynamic> toJson() => _$WebSocketErrorToJson(this);

  @override
  List<Object?> get props => [id, type, message, code, timestamp, details];
}

/// WebSocket error types enumeration
enum WebSocketErrorType {
  @JsonValue('connection_failed')
  connectionFailed,
  @JsonValue('authentication_failed')
  authenticationFailed,
  @JsonValue('message_parse_error')
  messageParseError,
  @JsonValue('channel_error')
  channelError,
  @JsonValue('rate_limit_exceeded')
  rateLimitExceeded,
  @JsonValue('server_error')
  serverError,
  @JsonValue('unknown_error')
  unknownError,
}

/// WebSocket subscription model
@JsonSerializable()
class WebSocketSubscription extends Equatable {
  final String id;
  final String channel;
  final String? userId;
  final Map<String, dynamic>? filters;
  final DateTime subscribedAt;
  final bool isActive;

  const WebSocketSubscription({
    required this.id,
    required this.channel,
    this.userId,
    this.filters,
    required this.subscribedAt,
    this.isActive = true,
  });

  factory WebSocketSubscription.fromJson(Map<String, dynamic> json) =>
      _$WebSocketSubscriptionFromJson(json);

  Map<String, dynamic> toJson() => _$WebSocketSubscriptionToJson(this);

  @override
  List<Object?> get props => [id, channel, userId, filters, subscribedAt, isActive];
}
