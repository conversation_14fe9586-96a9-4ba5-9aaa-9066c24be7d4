# Server-specific .dockerignore
# Optimizes server Docker build context

# Docker files
.dockerignore
Dockerfile*

# Version control
.git/
.github/
.gitignore

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Build artifacts
build/
.dart_tool/
.packages
.pub-cache/

# Documentation
*.md
!README.md

# Test files (can be excluded in production)
test/
*_test.dart

# Coverage reports
coverage/

# Logs
*.log
logs/

# Environment files
.env*

# OS files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
