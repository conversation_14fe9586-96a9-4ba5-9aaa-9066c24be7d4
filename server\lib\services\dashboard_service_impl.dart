import 'dart:async';
import 'package:shared/shared.dart';
import 'dashboard_repository_impl.dart';
import 'user_repository_impl.dart';
import 'quest_repository_impl.dart';
import 'notification_service_impl.dart';

/// Server-side dashboard service implementation
class ServerDashboardService {
  final InMemoryDashboardRepository _dashboardRepository;
  final InMemoryUserRepository _userRepository;
  final InMemoryQuestRepository _questRepository;
  final ServerNotificationService _notificationService;
  
  // Real-time update streams
  final StreamController<DashboardStats> _statsController = StreamController<DashboardStats>.broadcast();
  final StreamController<Map<String, dynamic>> _userDashboardController = StreamController<Map<String, dynamic>>.broadcast();
  
  // Cache for frequently accessed data
  DashboardStats? _cachedStats;
  DateTime? _lastStatsUpdate;
  static const Duration _statsCacheExpiry = Duration(minutes: 2);

  ServerDashboardService({
    required InMemoryDashboardRepository dashboardRepository,
    required InMemoryUserRepository userRepository,
    required InMemoryQuestRepository questRepository,
    required ServerNotificationService notificationService,
  })  : _dashboardRepository = dashboardRepository,
        _userRepository = userRepository,
        _questRepository = questRepository,
        _notificationService = notificationService;

  /// Stream of dashboard statistics updates
  Stream<DashboardStats> get statsStream => _statsController.stream;
  
  /// Stream of user dashboard updates
  Stream<Map<String, dynamic>> get userDashboardStream => _userDashboardController.stream;

  /// Get current dashboard statistics
  Future<DashboardStats> getStats({bool forceRefresh = false}) async {
    final now = DateTime.now();
    
    // Return cached stats if still valid and not forcing refresh
    if (!forceRefresh && 
        _cachedStats != null && 
        _lastStatsUpdate != null &&
        now.difference(_lastStatsUpdate!).compareTo(_statsCacheExpiry) < 0) {
      return _cachedStats!;
    }
    
    // Get fresh stats
    final stats = await _dashboardRepository.getStats();
    
    // Update cache
    _cachedStats = stats;
    _lastStatsUpdate = now;
    
    // Broadcast update
    _statsController.add(stats);
    
    return stats;
  }

  /// Get user-specific dashboard data
  Future<Map<String, dynamic>> getUserDashboard(String userId) async {
    final dashboard = await _dashboardRepository.getUserDashboard(userId);
    
    // Broadcast user dashboard update
    _userDashboardController.add({
      'userId': userId,
      'dashboard': dashboard,
    });
    
    return dashboard;
  }

  /// Get recent activity across the system
  Future<List<UserActivity>> getRecentActivity({int page = 1, int pageSize = 20}) async {
    return await _dashboardRepository.getRecentActivity(page: page, pageSize: pageSize);
  }

  /// Get system health metrics
  Future<SystemHealth> getSystemHealth() async {
    return await _dashboardRepository.getSystemHealth();
  }

  /// Get analytics data for a date range
  Future<Map<String, dynamic>> getAnalytics(DateTime startDate, DateTime endDate) async {
    return await _dashboardRepository.getAnalytics(startDate, endDate);
  }

  /// Get performance metrics
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    return await _dashboardRepository.getPerformanceMetrics();
  }

  /// Get user performance rankings
  Future<List<UserPerformance>> getUserPerformanceRankings({int page = 1, int pageSize = 20}) async {
    final allUsers = await _userRepository.getAll();
    final userPerformances = <UserPerformance>[];
    
    // Calculate performance metrics for each user
    for (int i = 0; i < allUsers.length; i++) {
      final user = allUsers[i];
      final userQuests = await _questRepository.getAssignedToUser(user.id);
      
      final totalQuests = userQuests.length;
      final completedQuests = userQuests.where((q) => q.status == QuestStatus.completed).length;
      final failedQuests = userQuests.where((q) => q.status == QuestStatus.failed).length;
      final completionRate = totalQuests > 0 ? (completedQuests / totalQuests) * 100 : 0.0;
      
      userPerformances.add(UserPerformance(
        userId: user.id,
        username: user.username,
        totalQuests: totalQuests,
        completedQuests: completedQuests,
        failedQuests: failedQuests,
        completionRate: completionRate,
        totalXp: user.xp,
        currentLevel: user.level,
        rank: i + 1, // Will be recalculated after sorting
        lastActive: user.lastLoginAt ?? user.createdAt,
      ));
    }
    
    // Sort by XP (descending) and update ranks
    userPerformances.sort((a, b) => b.totalXp.compareTo(a.totalXp));
    for (int i = 0; i < userPerformances.length; i++) {
      final performance = userPerformances[i];
      userPerformances[i] = UserPerformance(
        userId: performance.userId,
        username: performance.username,
        totalQuests: performance.totalQuests,
        completedQuests: performance.completedQuests,
        failedQuests: performance.failedQuests,
        completionRate: performance.completionRate,
        totalXp: performance.totalXp,
        currentLevel: performance.currentLevel,
        rank: i + 1,
        lastActive: performance.lastActive,
      );
    }
    
    // Apply pagination
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= userPerformances.length) return [];
    
    return userPerformances.sublist(
      startIndex,
      endIndex > userPerformances.length ? userPerformances.length : endIndex,
    );
  }

  /// Get quest statistics
  Future<List<QuestStats>> getQuestStats({int page = 1, int pageSize = 20}) async {
    final allQuests = await _questRepository.getAll();
    final questStats = <QuestStats>[];
    
    for (final quest in allQuests) {
      final stats = await _questRepository.getQuestStats(quest.id);
      
      questStats.add(QuestStats(
        questId: quest.id,
        title: quest.title,
        totalAssignments: stats['totalAssignments'] ?? 0,
        completions: stats['completions'] ?? 0,
        failures: 0, // Would need to track failed attempts
        completionRate: stats['completionRate'] ?? 0.0,
        averageCompletionTime: 0.0, // Would need to track completion times
        lastCompleted: quest.completedAt ?? quest.createdAt,
      ));
    }
    
    // Sort by completion rate (descending)
    questStats.sort((a, b) => b.completionRate.compareTo(a.completionRate));
    
    // Apply pagination
    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    
    if (startIndex >= questStats.length) return [];
    
    return questStats.sublist(
      startIndex,
      endIndex > questStats.length ? questStats.length : endIndex,
    );
  }

  /// Trigger dashboard refresh and notify connected clients
  Future<void> refreshDashboard() async {
    // Force refresh stats
    await getStats(forceRefresh: true);

    // Send broadcast notification about dashboard update
    await _notificationService.sendBroadcast(
      title: 'Dashboard Updated',
      message: 'Dashboard statistics have been refreshed',
      type: NotificationType.system,
      metadata: {
        'type': 'dashboard_refresh',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Get dashboard summary for quick overview
  Future<Map<String, dynamic>> getDashboardSummary() async {
    final stats = await getStats();
    final systemHealth = await getSystemHealth();
    final recentActivity = await getRecentActivity(pageSize: 5);
    
    return {
      'stats': stats.toJson(),
      'systemHealth': {
        'status': systemHealth.status,
        'activeConnections': systemHealth.activeConnections,
        'memoryUsage': systemHealth.memoryUsage,
      },
      'recentActivity': recentActivity.map((activity) => activity.toJson()).toList(),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Record user activity for dashboard tracking
  Future<void> recordUserActivity({
    required String userId,
    required String username,
    required String action,
    required String description,
    String? questId,
    int? xpGained,
    Map<String, dynamic>? details,
  }) async {
    final activity = UserActivity(
      id: IdGenerator.generateActivityId(),
      userId: userId,
      username: username,
      action: action,
      description: description,
      timestamp: DateTime.now(),
      questId: questId,
      xpGained: xpGained,
      details: details,
    );
    
    // Add activity to user repository
    await _userRepository.addUserActivity(userId, activity);
    
    // Invalidate stats cache to trigger refresh
    _cachedStats = null;
    _lastStatsUpdate = null;
  }

  /// Clean up old dashboard data
  Future<void> cleanupOldData({Duration olderThan = const Duration(days: 90)}) async {
    // This would clean up old activities, metrics, etc.
    // For now, just clean up old notifications
    await _notificationService.cleanupOldNotifications(olderThan: olderThan);
  }

  /// Dispose of resources
  void dispose() {
    _statsController.close();
    _userDashboardController.close();
  }
}
