/// Validation-related exception classes
class ValidationException implements Exception {
  final String message;
  final Map<String, String> fieldErrors;
  final String? code;

  const ValidationException({
    required this.message,
    this.fieldErrors = const {},
    this.code,
  });

  @override
  String toString() {
    final buffer = StringBuffer('ValidationException: $message');
    if (fieldErrors.isNotEmpty) {
      buffer.write(' (Fields: ${fieldErrors.keys.join(', ')})');
    }
    return buffer.toString();
  }

  /// Create from field errors
  factory ValidationException.fromFields(Map<String, String> fields) {
    return ValidationException(
      message: 'Validation failed',
      fieldErrors: fields,
      code: 'VALIDATION_FAILED',
    );
  }

  /// Single field error
  factory ValidationException.singleField({
    required String field,
    required String error,
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Validation failed for $field',
      fieldErrors: {field: error},
      code: 'FIELD_VALIDATION_FAILED',
    );
  }

  /// Required field error
  factory ValidationException.requiredField({
    required String field,
    String? message,
  }) {
    return ValidationException(
      message: message ?? '$field is required',
      fieldErrors: {field: 'This field is required'},
      code: 'REQUIRED_FIELD_MISSING',
    );
  }

  /// Invalid format error
  factory ValidationException.invalidFormat({
    required String field,
    required String expectedFormat,
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid format for $field',
      fieldErrors: {field: 'Expected format: $expectedFormat'},
      code: 'INVALID_FORMAT',
    );
  }

  /// Length validation error
  factory ValidationException.invalidLength({
    required String field,
    int? minLength,
    int? maxLength,
    int? actualLength,
    String? message,
  }) {
    String errorMessage;
    if (minLength != null && maxLength != null) {
      errorMessage = 'Must be between $minLength and $maxLength characters';
    } else if (minLength != null) {
      errorMessage = 'Must be at least $minLength characters';
    } else if (maxLength != null) {
      errorMessage = 'Must not exceed $maxLength characters';
    } else {
      errorMessage = 'Invalid length';
    }

    return ValidationException(
      message: message ?? 'Invalid length for $field',
      fieldErrors: {field: errorMessage},
      code: 'INVALID_LENGTH',
    );
  }

  /// Range validation error
  factory ValidationException.outOfRange({
    required String field,
    num? minValue,
    num? maxValue,
    num? actualValue,
    String? message,
  }) {
    String errorMessage;
    if (minValue != null && maxValue != null) {
      errorMessage = 'Must be between $minValue and $maxValue';
    } else if (minValue != null) {
      errorMessage = 'Must be at least $minValue';
    } else if (maxValue != null) {
      errorMessage = 'Must not exceed $maxValue';
    } else {
      errorMessage = 'Value out of range';
    }

    return ValidationException(
      message: message ?? 'Value out of range for $field',
      fieldErrors: {field: errorMessage},
      code: 'OUT_OF_RANGE',
    );
  }

  /// Email validation error
  factory ValidationException.invalidEmail({
    String field = 'email',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid email address',
      fieldErrors: {field: 'Please enter a valid email address'},
      code: 'INVALID_EMAIL',
    );
  }

  /// Password validation error
  factory ValidationException.weakPassword({
    String field = 'password',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Password does not meet requirements',
      fieldErrors: {
        field: 'Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters'
      },
      code: 'WEAK_PASSWORD',
    );
  }

  /// Username validation error
  factory ValidationException.invalidUsername({
    String field = 'username',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid username',
      fieldErrors: {
        field: 'Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens'
      },
      code: 'INVALID_USERNAME',
    );
  }

  /// Phone validation error
  factory ValidationException.invalidPhone({
    String field = 'phone',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid phone number',
      fieldErrors: {field: 'Please enter a valid phone number'},
      code: 'INVALID_PHONE',
    );
  }

  /// URL validation error
  factory ValidationException.invalidUrl({
    String field = 'url',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid URL',
      fieldErrors: {field: 'Please enter a valid URL'},
      code: 'INVALID_URL',
    );
  }

  /// Date validation error
  factory ValidationException.invalidDate({
    String field = 'date',
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid date',
      fieldErrors: {field: 'Please enter a valid date'},
      code: 'INVALID_DATE',
    );
  }

  /// File validation error
  factory ValidationException.invalidFile({
    String field = 'file',
    String? reason,
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Invalid file',
      fieldErrors: {field: reason ?? 'Please select a valid file'},
      code: 'INVALID_FILE',
    );
  }

  /// File size validation error
  factory ValidationException.fileTooLarge({
    String field = 'file',
    int? maxSizeBytes,
    int? actualSizeBytes,
    String? message,
  }) {
    String errorMessage = 'File size exceeds the maximum limit';
    if (maxSizeBytes != null) {
      final maxSizeMB = (maxSizeBytes / (1024 * 1024)).toStringAsFixed(1);
      errorMessage = 'File size must not exceed ${maxSizeMB}MB';
    }

    return ValidationException(
      message: message ?? 'File too large',
      fieldErrors: {field: errorMessage},
      code: 'FILE_TOO_LARGE',
    );
  }

  /// File type validation error
  factory ValidationException.unsupportedFileType({
    String field = 'file',
    List<String>? allowedTypes,
    String? actualType,
    String? message,
  }) {
    String errorMessage = 'File type is not supported';
    if (allowedTypes != null && allowedTypes.isNotEmpty) {
      errorMessage = 'Allowed file types: ${allowedTypes.join(', ')}';
    }

    return ValidationException(
      message: message ?? 'Unsupported file type',
      fieldErrors: {field: errorMessage},
      code: 'UNSUPPORTED_FILE_TYPE',
    );
  }

  /// Duplicate value error
  factory ValidationException.duplicateValue({
    required String field,
    String? value,
    String? message,
  }) {
    String errorMessage = 'This value already exists';
    if (value != null) {
      errorMessage = '"$value" already exists';
    }

    return ValidationException(
      message: message ?? 'Duplicate value for $field',
      fieldErrors: {field: errorMessage},
      code: 'DUPLICATE_VALUE',
    );
  }

  /// Custom validation error
  factory ValidationException.custom({
    required String field,
    required String error,
    String? message,
    String? code,
  }) {
    return ValidationException(
      message: message ?? 'Validation failed for $field',
      fieldErrors: {field: error},
      code: code ?? 'CUSTOM_VALIDATION_ERROR',
    );
  }

  /// Multiple validation errors
  factory ValidationException.multiple({
    required Map<String, String> errors,
    String? message,
  }) {
    return ValidationException(
      message: message ?? 'Multiple validation errors occurred',
      fieldErrors: errors,
      code: 'MULTIPLE_VALIDATION_ERRORS',
    );
  }

  /// Check if has errors for specific field
  bool hasErrorForField(String field) {
    return fieldErrors.containsKey(field);
  }

  /// Get error for specific field
  String? getErrorForField(String field) {
    return fieldErrors[field];
  }

  /// Get all field names with errors
  List<String> get errorFields => fieldErrors.keys.toList();

  /// Check if has any field errors
  bool get hasFieldErrors => fieldErrors.isNotEmpty;

  /// Get formatted error message including field errors
  String get detailedMessage {
    if (fieldErrors.isEmpty) return message;
    
    final buffer = StringBuffer(message);
    buffer.writeln();
    
    for (final entry in fieldErrors.entries) {
      buffer.writeln('${entry.key}: ${entry.value}');
    }
    
    return buffer.toString().trim();
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'fieldErrors': fieldErrors,
      'code': code,
    };
  }

  /// Create from map
  factory ValidationException.fromMap(Map<String, dynamic> map) {
    return ValidationException(
      message: map['message'] as String,
      fieldErrors: Map<String, String>.from(map['fieldErrors'] ?? {}),
      code: map['code'] as String?,
    );
  }
}
