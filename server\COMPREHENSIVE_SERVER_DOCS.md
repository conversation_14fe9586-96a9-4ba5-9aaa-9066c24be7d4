# Quester Server - Comprehensive Documentation

## 📋 Overview

The Quester server is a comprehensive Dart-based backend application that provides RESTful APIs and WebSocket connections for the Quester quest management platform. It features a complete service-oriented architecture with robust error handling, comprehensive testing, and production-ready components.

## 🏗️ Architecture

### **Core Services Architecture**

The server follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP/WebSocket Layer                     │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │    Auth     │ │    Quest    │ │ Notification│           │
│  │   Service   │ │   Service   │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Achievement │ │ Leaderboard │ │  Dashboard  │           │
│  │   Service   │ │   Service   │ │   Service   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                  Repository Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │    User     │ │    Quest    │ │Notification │           │
│  │ Repository  │ │ Repository  │ │ Repository  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                               │
│         In-Memory Storage / MongoDB Integration             │
└─────────────────────────────────────────────────────────────┘
```

### **Technology Stack**
- **Runtime**: Dart VM (3.0+)
- **Web Framework**: <PERSON><PERSON> (HTTP server and routing)
- **WebSocket**: shelf_web_socket for real-time communication
- **Authentication**: JWT tokens with bcrypt password hashing
- **Data Storage**: In-memory repositories (development) / MongoDB (production)
- **Configuration**: Environment variables with JSON config support
- **Testing**: Dart test framework with comprehensive unit and integration tests
- **Error Handling**: Structured error handling with logging and metrics

## 🚀 Getting Started

### **Prerequisites**
- Dart SDK 3.0 or higher
- MongoDB (for production) or use in-memory storage for development

### **Installation**

1. **Clone the repository**
   ```bash
   cd d:\quester\server
   ```

2. **Install dependencies**
   ```bash
   dart pub get
   ```

3. **Configure environment**
   ```bash
   # Copy sample configuration
   cp config/server.env .env
   
   # Edit configuration as needed
   nano .env
   ```

4. **Run the server**
   ```bash
   # Development mode
   dart run bin/server.dart
   
   # Production mode
   NODE_ENV=production dart run bin/server.dart
   ```

### **Configuration**

The server supports multiple configuration sources:

1. **Environment Variables** (highest priority)
2. **JSON Configuration File** (`config/server.json`)
3. **Default Values** (lowest priority)

#### **Key Configuration Options**

```bash
# Server Configuration
HOST=0.0.0.0
PORT=8080
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=27017
DB_NAME=quester_dev

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_ACCESS_EXPIRY_HOURS=1
JWT_REFRESH_EXPIRY_DAYS=7

# WebSocket Configuration
WS_PING_INTERVAL_SECONDS=30
WS_TIMEOUT_SECONDS=60
WS_MAX_CONNECTIONS=1000

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_EMAIL_VERIFICATION=false
```

## 📚 API Documentation

### **Authentication Endpoints**

#### **POST /api/auth/register**
Register a new user account.

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "isActive": true,
      "createdAt": "2025-01-02T10:00:00.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
  },
  "message": "User registered successfully",
  "statusCode": 201,
  "timestamp": "2025-01-02T10:00:00.000Z"
}
```

#### **POST /api/auth/login**
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "username": "johndoe",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "lastLoginAt": "2025-01-02T10:00:00.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
  },
  "message": "Login successful",
  "statusCode": 200,
  "timestamp": "2025-01-02T10:00:00.000Z"
}
```

#### **POST /api/auth/refresh**
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### **POST /api/auth/logout**
Logout user and invalidate tokens.

**Request Body:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

#### **GET /api/auth/validate**
Validate access token and get user info.

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

### **User Management Endpoints**

#### **GET /api/users/profile**
Get current user profile.

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_123",
    "username": "johndoe",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "level": 5,
    "xp": 2500,
    "isActive": true,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "lastLoginAt": "2025-01-02T10:00:00.000Z"
  },
  "statusCode": 200
}
```

#### **PUT /api/users/profile**
Update user profile.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "bio": "Updated bio"
}
```

#### **GET /api/users/{userId}/stats**
Get user statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "user_123",
    "level": 5,
    "xp": 2500,
    "joinDate": "2025-01-01T00:00:00.000Z",
    "lastLogin": "2025-01-02T10:00:00.000Z",
    "isActive": true,
    "role": "user",
    "fullName": "John Doe"
  },
  "statusCode": 200
}
```

### **Quest Management Endpoints**

#### **GET /api/quests**
Get list of quests with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 20)
- `status` (optional): Filter by quest status
- `difficulty` (optional): Filter by difficulty level

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "quest_123",
      "title": "Complete Daily Exercise",
      "description": "Exercise for at least 30 minutes",
      "difficulty": "easy",
      "category": "health",
      "status": "active",
      "xpReward": 100,
      "createdBy": "user_456",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "deadline": "2025-01-03T23:59:59.000Z"
    }
  ],
  "statusCode": 200
}
```

#### **POST /api/quests**
Create a new quest.

**Request Body:**
```json
{
  "title": "Learn New Programming Language",
  "description": "Complete a tutorial in a new programming language",
  "difficulty": "medium",
  "category": "education",
  "xpReward": 250,
  "deadline": "2025-01-10T23:59:59.000Z"
}
```

#### **GET /api/quests/{questId}**
Get specific quest details.

#### **PUT /api/quests/{questId}**
Update quest information.

#### **DELETE /api/quests/{questId}**
Delete a quest.

#### **POST /api/quests/{questId}/assign/{userId}**
Assign quest to a user.

#### **POST /api/quests/{questId}/complete**
Mark quest as completed.

**Request Body:**
```json
{
  "completionData": {
    "notes": "Completed successfully",
    "evidence": "screenshot_url"
  }
}
```

### **Notification Endpoints**

#### **GET /api/notifications**
Get user notifications.

**Query Parameters:**
- `page` (optional): Page number
- `pageSize` (optional): Items per page
- `unreadOnly` (optional): Show only unread notifications

#### **POST /api/notifications**
Send notification to user.

**Request Body:**
```json
{
  "userId": "user_123",
  "title": "Quest Completed!",
  "message": "You have successfully completed your daily exercise quest.",
  "type": "achievement"
}
```

#### **PUT /api/notifications/{notificationId}/read**
Mark notification as read.

#### **GET /api/notifications/unread-count**
Get count of unread notifications.

### **Achievement Endpoints**

#### **GET /api/achievements**
Get list of available achievements.

#### **GET /api/achievements/user/{userId}**
Get user's earned achievements.

#### **POST /api/achievements/{achievementId}/award/{userId}**
Award achievement to user.

### **Leaderboard Endpoints**

#### **GET /api/leaderboard/{boardType}**
Get leaderboard rankings.

**Parameters:**
- `boardType`: Type of leaderboard (global, weekly, monthly)

#### **GET /api/leaderboard/{boardType}/user/{userId}/rank**
Get user's rank in leaderboard.

### **Dashboard Endpoints**

#### **GET /api/dashboard/stats**
Get system dashboard statistics.

#### **GET /api/dashboard/user/{userId}**
Get user-specific dashboard data.

#### **GET /api/dashboard/activity/recent**
Get recent system activity.

### **System Endpoints**

#### **GET /api/health**
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-02T10:00:00.000Z",
  "uptime": "24:00:00",
  "version": "1.0.0"
}
```

#### **GET /api/metrics**
System metrics (if enabled).

## 🔌 WebSocket API

### **Connection**
Connect to WebSocket endpoint: `ws://localhost:8080/ws`

### **Message Format**
All WebSocket messages follow this format:
```json
{
  "id": "msg_123",
  "type": "notification",
  "data": {
    "title": "New Achievement!",
    "message": "You've earned a new badge!"
  },
  "timestamp": "2025-01-02T10:00:00.000Z",
  "userId": "user_123",
  "sessionId": "session_456"
}
```

### **Message Types**
- `authentication`: Authenticate WebSocket connection
- `subscribe`: Subscribe to channels
- `unsubscribe`: Unsubscribe from channels
- `notification`: Receive notifications
- `ping`/`pong`: Connection heartbeat
- `heartbeat`: Keep connection alive
- `systemMessage`: System announcements
- `error`: Error messages

### **Authentication**
Send authentication message after connection:
```json
{
  "id": "auth_123",
  "type": "authentication",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs..."
  },
  "timestamp": "2025-01-02T10:00:00.000Z"
}
```

### **Channel Subscription**
Subscribe to notification channels:
```json
{
  "id": "sub_123",
  "type": "subscribe",
  "data": {
    "channel": "user_notifications"
  },
  "timestamp": "2025-01-02T10:00:00.000Z"
}
```

## 🧪 Testing

### **Running Tests**

```bash
# Run all tests
dart test

# Run specific test file
dart test test/services/auth_service_test.dart

# Run with coverage
dart test --coverage=coverage

# Run integration tests
dart test test/integration/
```

### **Test Structure**

The server includes comprehensive testing:

- **Unit Tests**: Test individual services and repositories
- **Integration Tests**: Test complete workflows and service interactions
- **Test Utilities**: Helper functions for creating test data
- **Test Runner**: Centralized test execution with reporting

### **Test Coverage**

- Authentication Service: 95%+ coverage
- User Repository: 90%+ coverage
- Error Handler: 85%+ coverage
- Integration Tests: Complete workflow coverage

## 🚨 Error Handling

### **Error Response Format**
All API errors follow this consistent format:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "errorId": "err_123",
    "code": "VALIDATION_FAILED",
    "fieldErrors": {
      "email": "Invalid email format"
    }
  },
  "statusCode": 400,
  "timestamp": "2025-01-02T10:00:00.000Z"
}
```

### **HTTP Status Codes**
- `200`: Success
- `201`: Created
- `400`: Bad Request / Validation Error
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `429`: Rate Limited
- `500`: Internal Server Error

### **Error Categories**
- **Validation Errors**: Input validation failures
- **Authentication Errors**: Login/token issues
- **Authorization Errors**: Permission denied
- **Not Found Errors**: Resource not found
- **Rate Limit Errors**: Too many requests
- **Server Errors**: Internal system errors

## 📊 Monitoring and Logging

### **Error Logging**
The server includes comprehensive error logging with:
- Error categorization by severity
- Context information (user, endpoint, etc.)
- Stack traces for debugging
- Error statistics and reporting

### **Health Monitoring**
- Health check endpoint for monitoring
- System metrics collection
- Performance monitoring
- Connection statistics

## 🔧 Configuration Management

### **Environment-Based Configuration**
The server supports multiple environments:
- Development
- Staging
- Production
- Testing

### **Configuration Validation**
- Required settings validation
- Security checks for production
- Default value fallbacks
- Configuration file templates

## 🚀 Deployment

### **Development Deployment**
```bash
dart run bin/server.dart
```

### **Production Deployment**
```bash
NODE_ENV=production dart run bin/server.dart
```

### **Docker Deployment**
```bash
docker build -t quester-server .
docker run -p 8080:8080 quester-server
```

### **Environment Variables for Production**
```bash
NODE_ENV=production
JWT_SECRET=your-production-secret
DB_HOST=your-mongodb-host
DB_USERNAME=your-db-user
DB_PASSWORD=your-db-password
ENABLE_HTTPS=true
```

## 📈 Performance Considerations

### **Scalability Features**
- Connection pooling for database
- WebSocket connection management
- Rate limiting to prevent abuse
- Efficient pagination for large datasets
- Caching strategies for frequently accessed data

### **Security Features**
- JWT token authentication
- Password hashing with bcrypt
- CORS configuration
- Input validation and sanitization
- Rate limiting
- Error message sanitization

## 🔄 Development Workflow

### **Adding New Features**
1. Create service interface in shared package
2. Implement service in server
3. Add repository if needed
4. Create comprehensive tests
5. Update API documentation
6. Add configuration options if needed

### **Code Quality**
- Dart analysis with strict rules
- Comprehensive test coverage
- Error handling for all operations
- Consistent code formatting
- Documentation for all public APIs

This comprehensive documentation covers all aspects of the Quester server implementation, from basic setup to advanced deployment scenarios.
