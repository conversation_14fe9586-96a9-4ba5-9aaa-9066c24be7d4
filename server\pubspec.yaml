name: server
description: <PERSON><PERSON> server with comprehensive API and WebSocket support
version: 2.0.0
# repository: https://github.com/my_org/my_repo
publish_to: none

environment:
  sdk: ^3.8.1

dependencies:
  # Shared package integration
  shared:
    path: ../shared

  # Core server framework
  shelf: ^1.4.2
  shelf_router: ^1.1.4
  shelf_web_socket: ^2.0.0
  shelf_cors_headers: ^0.1.5
  shelf_static: ^1.1.2

  # HTTP and networking
  http: ^1.2.1
  web_socket_channel: ^2.4.5

  # JSON and serialization
  json_annotation: ^4.9.0

  # Database and storage
  mongo_dart: ^0.10.5

  # Authentication and security
  crypto: ^3.0.6
  bcrypt: ^1.1.3
  dart_jsonwebtoken: ^2.14.0

  # Utilities
  uuid: ^4.4.0
  meta: ^1.15.0
  equatable: ^2.0.5

  # Logging and monitoring
  logging: ^1.2.0

dev_dependencies:
  # Testing
  test: ^1.25.8
  mockito: ^5.4.4
  build_runner: ^2.4.7

  # Code quality
  lints: ^5.0.0

  # Code generation
  json_serializable: ^6.8.0
