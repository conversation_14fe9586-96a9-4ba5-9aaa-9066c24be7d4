// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'websocket_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WebSocketMessage _$WebSocketMessageFromJson(Map<String, dynamic> json) =>
    WebSocketMessage(
      id: json['id'] as String,
      type: $enumDecode(_$WebSocketMessageTypeEnumMap, json['type']),
      channel: json['channel'] as String?,
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
    );

Map<String, dynamic> _$WebSocketMessageToJson(WebSocketMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$WebSocketMessageTypeEnumMap[instance.type]!,
      'channel': instance.channel,
      'data': instance.data,
      'timestamp': instance.timestamp.toIso8601String(),
      'userId': instance.userId,
      'sessionId': instance.sessionId,
    };

const _$WebSocketMessageTypeEnumMap = {
  WebSocketMessageType.ping: 'ping',
  WebSocketMessageType.pong: 'pong',
  WebSocketMessageType.notification: 'notification',
  WebSocketMessageType.questUpdate: 'quest_update',
  WebSocketMessageType.userUpdate: 'user_update',
  WebSocketMessageType.dashboardUpdate: 'dashboard_update',
  WebSocketMessageType.systemMessage: 'system_message',
  WebSocketMessageType.error: 'error',
  WebSocketMessageType.subscribe: 'subscribe',
  WebSocketMessageType.unsubscribe: 'unsubscribe',
  WebSocketMessageType.authentication: 'authentication',
  WebSocketMessageType.heartbeat: 'heartbeat',
};

WebSocketConnectionState _$WebSocketConnectionStateFromJson(
  Map<String, dynamic> json,
) => WebSocketConnectionState(
  status: $enumDecode(_$ConnectionStatusEnumMap, json['status']),
  error: json['error'] as String?,
  lastConnected: json['lastConnected'] == null
      ? null
      : DateTime.parse(json['lastConnected'] as String),
  lastDisconnected: json['lastDisconnected'] == null
      ? null
      : DateTime.parse(json['lastDisconnected'] as String),
  reconnectAttempts: (json['reconnectAttempts'] as num?)?.toInt() ?? 0,
  nextReconnectDelay: json['nextReconnectDelay'] == null
      ? null
      : Duration(microseconds: (json['nextReconnectDelay'] as num).toInt()),
);

Map<String, dynamic> _$WebSocketConnectionStateToJson(
  WebSocketConnectionState instance,
) => <String, dynamic>{
  'status': _$ConnectionStatusEnumMap[instance.status]!,
  'error': instance.error,
  'lastConnected': instance.lastConnected?.toIso8601String(),
  'lastDisconnected': instance.lastDisconnected?.toIso8601String(),
  'reconnectAttempts': instance.reconnectAttempts,
  'nextReconnectDelay': instance.nextReconnectDelay?.inMicroseconds,
};

const _$ConnectionStatusEnumMap = {
  ConnectionStatus.disconnected: 'disconnected',
  ConnectionStatus.connecting: 'connecting',
  ConnectionStatus.connected: 'connected',
  ConnectionStatus.reconnecting: 'reconnecting',
  ConnectionStatus.error: 'error',
};

WebSocketError _$WebSocketErrorFromJson(Map<String, dynamic> json) =>
    WebSocketError(
      id: json['id'] as String,
      type: $enumDecode(_$WebSocketErrorTypeEnumMap, json['type']),
      message: json['message'] as String,
      code: json['code'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      details: json['details'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$WebSocketErrorToJson(WebSocketError instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$WebSocketErrorTypeEnumMap[instance.type]!,
      'message': instance.message,
      'code': instance.code,
      'timestamp': instance.timestamp.toIso8601String(),
      'details': instance.details,
    };

const _$WebSocketErrorTypeEnumMap = {
  WebSocketErrorType.connectionFailed: 'connection_failed',
  WebSocketErrorType.authenticationFailed: 'authentication_failed',
  WebSocketErrorType.messageParseError: 'message_parse_error',
  WebSocketErrorType.channelError: 'channel_error',
  WebSocketErrorType.rateLimitExceeded: 'rate_limit_exceeded',
  WebSocketErrorType.serverError: 'server_error',
  WebSocketErrorType.unknownError: 'unknown_error',
};

WebSocketSubscription _$WebSocketSubscriptionFromJson(
  Map<String, dynamic> json,
) => WebSocketSubscription(
  id: json['id'] as String,
  channel: json['channel'] as String,
  userId: json['userId'] as String?,
  filters: json['filters'] as Map<String, dynamic>?,
  subscribedAt: DateTime.parse(json['subscribedAt'] as String),
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$WebSocketSubscriptionToJson(
  WebSocketSubscription instance,
) => <String, dynamic>{
  'id': instance.id,
  'channel': instance.channel,
  'userId': instance.userId,
  'filters': instance.filters,
  'subscribedAt': instance.subscribedAt.toIso8601String(),
  'isActive': instance.isActive,
};
