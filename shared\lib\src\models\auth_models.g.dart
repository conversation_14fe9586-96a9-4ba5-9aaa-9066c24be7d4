// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
  email: json['email'] as String,
  password: json['password'] as String,
);

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{'email': instance.email, 'password': instance.password};

RegisterRequest _$RegisterRequestFromJson(Map<String, dynamic> json) =>
    RegisterRequest(
      username: json['username'] as String,
      email: json['email'] as String,
      password: json['password'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
    );

Map<String, dynamic> _$RegisterRequestToJson(RegisterRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'email': instance.email,
      'password': instance.password,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
    };

UpdateUserRequest _$UpdateUserRequestFromJson(Map<String, dynamic> json) =>
    UpdateUserRequest(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      username: json['username'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$UpdateUserRequestToJson(UpdateUserRequest instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'username': instance.username,
      'email': instance.email,
    };

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
  user: User.fromJson(json['user'] as Map<String, dynamic>),
  accessToken: json['accessToken'] as String,
  refreshToken: json['refreshToken'] as String,
);

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'user': instance.user,
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
    };

TokenResponse _$TokenResponseFromJson(Map<String, dynamic> json) =>
    TokenResponse(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
    );

Map<String, dynamic> _$TokenResponseToJson(TokenResponse instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
  level: (json['level'] as num).toInt(),
  xp: (json['xp'] as num).toInt(),
  totalQuests: (json['totalQuests'] as num).toInt(),
  completedQuests: (json['completedQuests'] as num).toInt(),
  failedQuests: (json['failedQuests'] as num).toInt(),
  activeQuests: (json['activeQuests'] as num).toInt(),
  completionRate: (json['completionRate'] as num).toDouble(),
  streak: (json['streak'] as num).toInt(),
  achievements: (json['achievements'] as num).toInt(),
  rank: (json['rank'] as num).toInt(),
  joinDate: DateTime.parse(json['joinDate'] as String),
  lastActive: DateTime.parse(json['lastActive'] as String),
);

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
  'level': instance.level,
  'xp': instance.xp,
  'totalQuests': instance.totalQuests,
  'completedQuests': instance.completedQuests,
  'failedQuests': instance.failedQuests,
  'activeQuests': instance.activeQuests,
  'completionRate': instance.completionRate,
  'streak': instance.streak,
  'achievements': instance.achievements,
  'rank': instance.rank,
  'joinDate': instance.joinDate.toIso8601String(),
  'lastActive': instance.lastActive.toIso8601String(),
};

QuestAssignment _$QuestAssignmentFromJson(Map<String, dynamic> json) =>
    QuestAssignment(
      questId: json['questId'] as String,
      userId: json['userId'] as String,
      status: json['status'] as String,
      progress: (json['progress'] as num).toDouble(),
      assignedAt: DateTime.parse(json['assignedAt'] as String),
      assignedBy: json['assignedBy'] as String,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      quest: json['quest'] == null
          ? null
          : Quest.fromJson(json['quest'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$QuestAssignmentToJson(QuestAssignment instance) =>
    <String, dynamic>{
      'questId': instance.questId,
      'userId': instance.userId,
      'status': instance.status,
      'progress': instance.progress,
      'assignedAt': instance.assignedAt.toIso8601String(),
      'assignedBy': instance.assignedBy,
      'completedAt': instance.completedAt?.toIso8601String(),
      'quest': instance.quest,
    };

QuestCompletion _$QuestCompletionFromJson(Map<String, dynamic> json) =>
    QuestCompletion(
      assignment: QuestAssignment.fromJson(
        json['assignment'] as Map<String, dynamic>,
      ),
      rewards: QuestRewards.fromJson(json['rewards'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$QuestCompletionToJson(QuestCompletion instance) =>
    <String, dynamic>{
      'assignment': instance.assignment,
      'rewards': instance.rewards,
    };

QuestRewards _$QuestRewardsFromJson(Map<String, dynamic> json) => QuestRewards(
  xp: (json['xp'] as num).toInt(),
  coins: (json['coins'] as num).toInt(),
);

Map<String, dynamic> _$QuestRewardsToJson(QuestRewards instance) =>
    <String, dynamic>{'xp': instance.xp, 'coins': instance.coins};

CreateQuestRequest _$CreateQuestRequestFromJson(Map<String, dynamic> json) =>
    CreateQuestRequest(
      title: json['title'] as String,
      description: json['description'] as String,
      difficulty: json['difficulty'] as String,
      category: json['category'] as String?,
      xpReward: (json['xpReward'] as num).toInt(),
      coinReward: (json['coinReward'] as num?)?.toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
    );

Map<String, dynamic> _$CreateQuestRequestToJson(CreateQuestRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'category': instance.category,
      'xpReward': instance.xpReward,
      'coinReward': instance.coinReward,
      'dueDate': instance.dueDate?.toIso8601String(),
    };

UpdateQuestRequest _$UpdateQuestRequestFromJson(Map<String, dynamic> json) =>
    UpdateQuestRequest(
      title: json['title'] as String,
      description: json['description'] as String,
      difficulty: json['difficulty'] as String,
      category: json['category'] as String?,
      xpReward: (json['xpReward'] as num).toInt(),
      coinReward: (json['coinReward'] as num?)?.toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      status: json['status'] as String?,
    );

Map<String, dynamic> _$UpdateQuestRequestToJson(UpdateQuestRequest instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'category': instance.category,
      'xpReward': instance.xpReward,
      'coinReward': instance.coinReward,
      'dueDate': instance.dueDate?.toIso8601String(),
      'status': instance.status,
    };
