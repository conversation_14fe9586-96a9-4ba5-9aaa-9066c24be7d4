import 'dart:async';
import 'dart:io';
import 'package:shared/shared.dart';

/// Database configuration
class DatabaseConfig {
  final String host;
  final int port;
  final String databaseName;
  final String? username;
  final String? password;
  final bool useSSL;
  final Duration connectionTimeout;
  final int maxConnections;

  const DatabaseConfig({
    this.host = 'localhost',
    this.port = 27017,
    this.databaseName = 'quester_db',
    this.username,
    this.password,
    this.useSSL = false,
    this.connectionTimeout = const Duration(seconds: 30),
    this.maxConnections = 10,
  });

  /// Create config from environment variables
  factory DatabaseConfig.fromEnvironment() {
    return DatabaseConfig(
      host: Platform.environment['DB_HOST'] ?? 'localhost',
      port: int.parse(Platform.environment['DB_PORT'] ?? '27017'),
      databaseName: Platform.environment['DB_NAME'] ?? 'quester_db',
      username: Platform.environment['DB_USERNAME'],
      password: Platform.environment['DB_PASSWORD'],
      useSSL: Platform.environment['DB_USE_SSL']?.toLowerCase() == 'true',
      connectionTimeout: Duration(
        seconds: int.parse(Platform.environment['DB_TIMEOUT'] ?? '30'),
      ),
      maxConnections: int.parse(Platform.environment['DB_MAX_CONNECTIONS'] ?? '10'),
    );
  }

  /// Get connection string
  String get connectionString {
    final auth = username != null && password != null ? '$username:$password@' : '';
    final ssl = useSSL ? '?ssl=true' : '';
    return 'mongodb://$auth$host:$port/$databaseName$ssl';
  }
}

/// Database connection status
enum DatabaseStatus {
  disconnected,
  connecting,
  connected,
  error,
}

/// Database service interface
abstract class DatabaseService {
  /// Connection management
  Future<void> connect();
  Future<void> disconnect();
  DatabaseStatus get status;
  Stream<DatabaseStatus> get statusStream;

  /// Collection operations
  Future<T?> findById<T>(String collection, String id, T Function(Map<String, dynamic>) fromJson);
  Future<List<T>> findAll<T>(String collection, T Function(Map<String, dynamic>) fromJson, {int? limit, int? skip});
  Future<List<T>> findWhere<T>(String collection, Map<String, dynamic> query, T Function(Map<String, dynamic>) fromJson, {int? limit, int? skip});
  Future<String> insertOne<T>(String collection, T document, Map<String, dynamic> Function(T) toJson);
  Future<List<String>> insertMany<T>(String collection, List<T> documents, Map<String, dynamic> Function(T) toJson);
  Future<bool> updateOne(String collection, String id, Map<String, dynamic> update);
  Future<int> updateMany(String collection, Map<String, dynamic> query, Map<String, dynamic> update);
  Future<bool> deleteOne(String collection, String id);
  Future<int> deleteMany(String collection, Map<String, dynamic> query);
  Future<int> count(String collection, {Map<String, dynamic>? query});

  /// Index management
  Future<void> createIndex(String collection, Map<String, dynamic> keys, {bool unique = false});
  Future<void> dropIndex(String collection, String indexName);

  /// Transaction support
  Future<T> transaction<T>(Future<T> Function() operation);

  /// Health check
  Future<Map<String, dynamic>> healthCheck();
}

/// In-memory database service implementation for development
class InMemoryDatabaseService implements DatabaseService {
  final DatabaseConfig _config;
  final Map<String, Map<String, Map<String, dynamic>>> _collections = {}; // collection -> id -> document
  final StreamController<DatabaseStatus> _statusController = StreamController<DatabaseStatus>.broadcast();
  
  DatabaseStatus _status = DatabaseStatus.disconnected;
  Timer? _healthCheckTimer;

  InMemoryDatabaseService(this._config);

  @override
  DatabaseStatus get status => _status;

  @override
  Stream<DatabaseStatus> get statusStream => _statusController.stream;

  @override
  Future<void> connect() async {
    if (_status == DatabaseStatus.connected) return;

    _status = DatabaseStatus.connecting;
    _statusController.add(_status);

    try {
      // Simulate connection delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Initialize collections if they don't exist
      _initializeCollections();
      
      _status = DatabaseStatus.connected;
      _statusController.add(_status);
      
      // Start health check timer
      _startHealthCheck();
      
      print('📊 Database connected successfully (In-Memory)');
    } catch (e) {
      _status = DatabaseStatus.error;
      _statusController.add(_status);
      throw Exception('Failed to connect to database: $e');
    }
  }

  @override
  Future<void> disconnect() async {
    _status = DatabaseStatus.disconnected;
    _statusController.add(_status);
    
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
    
    print('📊 Database disconnected');
  }

  @override
  Future<T?> findById<T>(String collection, String id, T Function(Map<String, dynamic>) fromJson) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return null;
    
    final document = collectionData[id];
    if (document == null) return null;
    
    return fromJson(document);
  }

  @override
  Future<List<T>> findAll<T>(String collection, T Function(Map<String, dynamic>) fromJson, {int? limit, int? skip}) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return [];
    
    var documents = collectionData.values.toList();
    
    if (skip != null && skip > 0) {
      documents = documents.skip(skip).toList();
    }
    
    if (limit != null && limit > 0) {
      documents = documents.take(limit).toList();
    }
    
    return documents.map((doc) => fromJson(doc)).toList();
  }

  @override
  Future<List<T>> findWhere<T>(String collection, Map<String, dynamic> query, T Function(Map<String, dynamic>) fromJson, {int? limit, int? skip}) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return [];
    
    var documents = collectionData.values.where((doc) => _matchesQuery(doc, query)).toList();
    
    if (skip != null && skip > 0) {
      documents = documents.skip(skip).toList();
    }
    
    if (limit != null && limit > 0) {
      documents = documents.take(limit).toList();
    }
    
    return documents.map((doc) => fromJson(doc)).toList();
  }

  @override
  Future<String> insertOne<T>(String collection, T document, Map<String, dynamic> Function(T) toJson) async {
    _ensureConnected();
    
    final documentMap = toJson(document);
    final id = documentMap['id'] as String? ?? IdGenerator.generateShortId();
    documentMap['id'] = id;
    documentMap['createdAt'] = DateTime.now().toIso8601String();
    documentMap['updatedAt'] = DateTime.now().toIso8601String();
    
    if (!_collections.containsKey(collection)) {
      _collections[collection] = {};
    }
    
    _collections[collection]![id] = documentMap;
    return id;
  }

  @override
  Future<List<String>> insertMany<T>(String collection, List<T> documents, Map<String, dynamic> Function(T) toJson) async {
    final ids = <String>[];
    
    for (final document in documents) {
      final id = await insertOne(collection, document, toJson);
      ids.add(id);
    }
    
    return ids;
  }

  @override
  Future<bool> updateOne(String collection, String id, Map<String, dynamic> update) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null || !collectionData.containsKey(id)) {
      return false;
    }
    
    final document = collectionData[id]!;
    document.addAll(update);
    document['updatedAt'] = DateTime.now().toIso8601String();
    
    return true;
  }

  @override
  Future<int> updateMany(String collection, Map<String, dynamic> query, Map<String, dynamic> update) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return 0;
    
    int updatedCount = 0;
    
    for (final entry in collectionData.entries) {
      if (_matchesQuery(entry.value, query)) {
        entry.value.addAll(update);
        entry.value['updatedAt'] = DateTime.now().toIso8601String();
        updatedCount++;
      }
    }
    
    return updatedCount;
  }

  @override
  Future<bool> deleteOne(String collection, String id) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return false;
    
    return collectionData.remove(id) != null;
  }

  @override
  Future<int> deleteMany(String collection, Map<String, dynamic> query) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return 0;
    
    final idsToDelete = <String>[];
    
    for (final entry in collectionData.entries) {
      if (_matchesQuery(entry.value, query)) {
        idsToDelete.add(entry.key);
      }
    }
    
    for (final id in idsToDelete) {
      collectionData.remove(id);
    }
    
    return idsToDelete.length;
  }

  @override
  Future<int> count(String collection, {Map<String, dynamic>? query}) async {
    _ensureConnected();
    
    final collectionData = _collections[collection];
    if (collectionData == null) return 0;
    
    if (query == null) {
      return collectionData.length;
    }
    
    return collectionData.values.where((doc) => _matchesQuery(doc, query)).length;
  }

  @override
  Future<void> createIndex(String collection, Map<String, dynamic> keys, {bool unique = false}) async {
    // In-memory implementation doesn't need actual indexes
    print('📊 Created index on $collection: $keys (unique: $unique)');
  }

  @override
  Future<void> dropIndex(String collection, String indexName) async {
    // In-memory implementation doesn't need actual indexes
    print('📊 Dropped index $indexName on $collection');
  }

  @override
  Future<T> transaction<T>(Future<T> Function() operation) async {
    // In-memory implementation doesn't support real transactions
    // Just execute the operation
    return await operation();
  }

  @override
  Future<Map<String, dynamic>> healthCheck() async {
    return {
      'status': _status.name,
      'connected': _status == DatabaseStatus.connected,
      'collections': _collections.keys.toList(),
      'totalDocuments': _collections.values.fold<int>(0, (sum, collection) => sum + collection.length),
      'config': {
        'host': _config.host,
        'port': _config.port,
        'database': _config.databaseName,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Initialize default collections
  void _initializeCollections() {
    final defaultCollections = ['users', 'quests', 'notifications', 'achievements', 'leaderboards'];
    
    for (final collection in defaultCollections) {
      if (!_collections.containsKey(collection)) {
        _collections[collection] = {};
      }
    }
  }

  /// Check if document matches query (simplified implementation)
  bool _matchesQuery(Map<String, dynamic> document, Map<String, dynamic> query) {
    for (final entry in query.entries) {
      final key = entry.key;
      final value = entry.value;
      
      if (!document.containsKey(key)) return false;
      
      if (document[key] != value) return false;
    }
    
    return true;
  }

  /// Ensure database is connected
  void _ensureConnected() {
    if (_status != DatabaseStatus.connected) {
      throw Exception('Database is not connected');
    }
  }

  /// Start health check timer
  void _startHealthCheck() {
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        await healthCheck();
      } catch (e) {
        print('📊 Database health check failed: $e');
        _status = DatabaseStatus.error;
        _statusController.add(_status);
      }
    });
  }

  /// Dispose of resources
  void dispose() {
    _healthCheckTimer?.cancel();
    _statusController.close();
  }
}

/// Database-backed user repository implementation
class DatabaseUserRepository implements UserRepository {
  final DatabaseService _db;
  static const String _collection = 'users';

  DatabaseUserRepository(this._db);

  @override
  Future<User> create(User entity) async {
    final id = await _db.insertOne(_collection, entity, (user) => user.toJson());
    return entity.copyWith(id: id);
  }

  @override
  Future<User?> getById(String id) async {
    return await _db.findById(_collection, id, (json) => User.fromJson(json));
  }

  @override
  Future<List<User>> getAll({int page = 1, int pageSize = 20}) async {
    final skip = (page - 1) * pageSize;
    return await _db.findAll(_collection, (json) => User.fromJson(json), limit: pageSize, skip: skip);
  }

  @override
  Future<User> update(String id, User entity) async {
    final success = await _db.updateOne(_collection, id, entity.toJson());
    if (!success) {
      throw Exception('User not found');
    }
    return entity;
  }

  @override
  Future<void> delete(String id) async {
    final success = await _db.deleteOne(_collection, id);
    if (!success) {
      throw Exception('User not found');
    }
  }

  @override
  Future<bool> exists(String id) async {
    final user = await getById(id);
    return user != null;
  }

  @override
  Future<int> count() async {
    return await _db.count(_collection);
  }

  @override
  Future<List<User>> search(String query, {int page = 1, int pageSize = 20}) async {
    // Simplified search - would use text search in real MongoDB
    final skip = (page - 1) * pageSize;
    return await _db.findWhere(_collection, {
      'username': query, // Simplified - would use regex in real implementation
    }, (json) => User.fromJson(json), limit: pageSize, skip: skip);
  }

  @override
  Future<List<User>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20}) async {
    final skip = (page - 1) * pageSize;
    return await _db.findWhere(_collection, filters, (json) => User.fromJson(json), limit: pageSize, skip: skip);
  }

  @override
  Future<List<User>> createBatch(List<User> entities) async {
    await _db.insertMany(_collection, entities, (user) => user.toJson());
    return entities;
  }

  @override
  Future<List<User>> updateBatch(List<User> entities) async {
    for (final user in entities) {
      await update(user.id, user);
    }
    return entities;
  }

  @override
  Future<void> deleteBatch(List<String> ids) async {
    for (final id in ids) {
      await delete(id);
    }
  }

  @override
  Future<User?> getByEmail(String email) async {
    final users = await _db.findWhere(_collection, {'email': email}, (json) => User.fromJson(json), limit: 1);
    return users.isNotEmpty ? users.first : null;
  }

  @override
  Future<User?> getByUsername(String username) async {
    final users = await _db.findWhere(_collection, {'username': username}, (json) => User.fromJson(json), limit: 1);
    return users.isNotEmpty ? users.first : null;
  }

  @override
  Future<List<User>> getByRole(UserRole role, {int page = 1, int pageSize = 20}) async {
    final skip = (page - 1) * pageSize;
    return await _db.findWhere(_collection, {'role': role.name}, (json) => User.fromJson(json), limit: pageSize, skip: skip);
  }

  @override
  Future<List<User>> getActiveUsers({int page = 1, int pageSize = 20}) async {
    final skip = (page - 1) * pageSize;
    return await _db.findWhere(_collection, {'isActive': true}, (json) => User.fromJson(json), limit: pageSize, skip: skip);
  }

  @override
  Future<User> updateProfile(String userId, Map<String, dynamic> updates) async {
    await _db.updateOne(_collection, userId, updates);
    final user = await getById(userId);
    if (user == null) {
      throw Exception('User not found');
    }
    return user;
  }

  @override
  Future<User> updateAvatar(String userId, String avatarUrl) async {
    await _db.updateOne(_collection, userId, {'avatarUrl': avatarUrl});
    final user = await getById(userId);
    if (user == null) {
      throw Exception('User not found');
    }
    return user;
  }

  @override
  Future<void> deactivateUser(String userId) async {
    await _db.updateOne(_collection, userId, {'isActive': false});
  }

  @override
  Future<void> activateUser(String userId) async {
    await _db.updateOne(_collection, userId, {'isActive': true});
  }

  @override
  Future<Map<String, dynamic>> getUserStats(String userId) async {
    final user = await getById(userId);
    if (user == null) {
      throw Exception('User not found');
    }

    // Return basic stats - would be more comprehensive in real implementation
    return {
      'userId': userId,
      'level': user.level,
      'xp': user.xp,
      'joinDate': user.createdAt.toIso8601String(),
      'lastLogin': user.lastLoginAt?.toIso8601String(),
      'isActive': user.isActive,
      'role': user.role.name,
      'fullName': user.fullName,
    };
  }

  @override
  Future<List<UserActivity>> getUserActivity(String userId, {int page = 1, int pageSize = 20}) async {
    // This would be stored in a separate activities collection in a real implementation
    // For now, return empty list
    return [];
  }

  /// Add user activity (not in interface but used by other services)
  Future<void> addUserActivity(String userId, UserActivity activity) async {
    // This would insert into activities collection in a real implementation
    // For now, do nothing
  }

  /// Update last login (not in interface but used by auth service)
  Future<void> updateLastLogin(String userId) async {
    await _db.updateOne(_collection, userId, {
      'lastLoginAt': DateTime.now().toIso8601String(),
    });
  }

  /// Update user preferences (not in interface but used by other services)
  Future<void> updateUserPreferences(String userId, Map<String, dynamic> preferences) async {
    await _db.updateOne(_collection, userId, {
      'preferences': preferences,
    });
  }

  /// Update user metadata (not in interface but used by other services)
  Future<void> updateUserMetadata(String userId, Map<String, dynamic> metadata) async {
    await _db.updateOne(_collection, userId, {
      'metadata': metadata,
    });
  }
}
