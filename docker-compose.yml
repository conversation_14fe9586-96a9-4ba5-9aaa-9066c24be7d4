services:
  mongodb:
    image: mongo:4.4
    container_name: quester_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    volumes:
      - mongodb_data:/data/db
      - ./settings/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - quester_network    
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongo localhost:27017/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 30s

  # MongoDB Admin Interface
  mongo-express:
    image: mongo-express:1.0-20
    container_name: quester_mongo_admin
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_USERNAME}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD}
      ME_CONFIG_MONGODB_URL: mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/?authSource=admin
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_ADMIN_USERNAME:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_ADMIN_PASSWORD:-admin123}
      ME_CONFIG_SITE_BASEURL: '/mongo-admin/'
    ports:
      - "8081:8081"
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - quester_network

  # Redis Database
  redis:
    image: redis:7-alpine
    container_name: quester_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - quester_network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Admin Interface
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: quester_redis_admin
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD}
      HTTP_USER: ${REDIS_ADMIN_USERNAME:-admin}
      HTTP_PASSWORD: ${REDIS_ADMIN_PASSWORD:-admin123}
    ports:
      - "8082:8081"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - quester_network

  server:
    build:
      context: .
      dockerfile: ./settings/docker/Dockerfile.server
      target: development
    container_name: quester_server
    restart: unless-stopped
    environment:
      MONGO_URL: mongodb://${MONGO_USERNAME}:${MONGO_PASSWORD}@mongodb:27017/${MONGO_DATABASE}?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      PORT: 8080
      NODE_ENV: development
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - quester_network
    ports:
      - "8080:8080"
    expose:
      - "8080"
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:8080/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  client:
    build:
      context: .
      dockerfile: ./settings/docker/Dockerfile.client
      target: development
    container_name: quester_client
    restart: unless-stopped
    environment:
      FLUTTER_WEB_PORT: 8000
      FLUTTER_WEB_HOSTNAME: 0.0.0.0
      API_BASE_URL: http://server:8080/api
      WS_URL: ws://server:8080/ws
    ports:
      - "8000:8000"
    networks:
      - quester_network
    expose:
      - "8000"
    depends_on:
      server:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:8000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    container_name: quester_nginx
    restart: unless-stopped
    volumes:
      - ./settings/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "3000:80"
    depends_on:
      server:
        condition: service_healthy
      client:
        condition: service_healthy
    networks:
      - quester_network
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  quester_network:
    driver: bridge
