/// WebSocket communication exception classes
class WebSocketCommunicationException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const WebSocketCommunicationException({
    required this.message,
    this.code,
    this.details,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer('WebSocketCommunicationException: $message');
    if (code != null) buffer.write(' (Code: $code)');
    return buffer.toString();
  }

  /// Connection failed
  factory WebSocketCommunicationException.connectionFailed({
    String message = 'Failed to establish WebSocket connection',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'CONNECTION_FAILED',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Connection lost
  factory WebSocketCommunicationException.connectionLost({
    String message = 'WebSocket connection lost',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'CONNECTION_LOST',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Authentication failed
  factory WebSocketCommunicationException.authenticationFailed({
    String message = 'WebSocket authentication failed',
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'AUTHENTICATION_FAILED',
    );
  }

  /// Message parsing error
  factory WebSocketCommunicationException.messageParseError({
    String message = 'Failed to parse WebSocket message',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'MESSAGE_PARSE_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Channel subscription error
  factory WebSocketCommunicationException.channelError({
    String message = 'WebSocket channel error',
    String? channel,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'CHANNEL_ERROR',
      details: channel != null ? {'channel': channel} : null,
    );
  }

  /// Rate limit exceeded
  factory WebSocketCommunicationException.rateLimitExceeded({
    String message = 'WebSocket rate limit exceeded',
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'RATE_LIMIT_EXCEEDED',
    );
  }

  /// Server error
  factory WebSocketCommunicationException.serverError({
    String message = 'WebSocket server error',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'SERVER_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Timeout error
  factory WebSocketCommunicationException.timeout({
    String message = 'WebSocket operation timed out',
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'TIMEOUT',
    );
  }

  /// Invalid message format
  factory WebSocketCommunicationException.invalidMessage({
    String message = 'Invalid WebSocket message format',
    Map<String, dynamic>? messageData,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'INVALID_MESSAGE',
      details: messageData,
    );
  }

  /// Subscription limit exceeded
  factory WebSocketCommunicationException.subscriptionLimitExceeded({
    String message = 'Maximum WebSocket subscriptions exceeded',
    int? currentCount,
    int? maxAllowed,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
      details: {
        if (currentCount != null) 'currentCount': currentCount,
        if (maxAllowed != null) 'maxAllowed': maxAllowed,
      },
    );
  }

  /// Reconnection failed
  factory WebSocketCommunicationException.reconnectionFailed({
    String message = 'Failed to reconnect WebSocket',
    int? attempts,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'RECONNECTION_FAILED',
      details: attempts != null ? {'attempts': attempts} : null,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Protocol error
  factory WebSocketCommunicationException.protocolError({
    String message = 'WebSocket protocol error',
    String? protocol,
    dynamic originalError,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'PROTOCOL_ERROR',
      details: protocol != null ? {'protocol': protocol} : null,
      originalError: originalError,
    );
  }

  /// Unknown error
  factory WebSocketCommunicationException.unknown({
    String message = 'Unknown WebSocket error',
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return WebSocketCommunicationException(
      message: message,
      code: 'UNKNOWN_ERROR',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}

/// Achievement-related exception classes
class AchievementException implements Exception {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;

  const AchievementException({
    required this.message,
    this.code,
    this.details,
  });

  @override
  String toString() {
    final buffer = StringBuffer('AchievementException: $message');
    if (code != null) buffer.write(' (Code: $code)');
    return buffer.toString();
  }

  /// Achievement not found
  factory AchievementException.notFound({
    String message = 'Achievement not found',
    String? achievementId,
  }) {
    return AchievementException(
      message: message,
      code: 'ACHIEVEMENT_NOT_FOUND',
      details: achievementId != null ? {'achievementId': achievementId} : null,
    );
  }

  /// Achievement already unlocked
  factory AchievementException.alreadyUnlocked({
    String message = 'Achievement already unlocked',
    String? achievementId,
  }) {
    return AchievementException(
      message: message,
      code: 'ACHIEVEMENT_ALREADY_UNLOCKED',
      details: achievementId != null ? {'achievementId': achievementId} : null,
    );
  }

  /// Requirements not met
  factory AchievementException.requirementsNotMet({
    String message = 'Achievement requirements not met',
    String? achievementId,
    List<String>? missingRequirements,
  }) {
    return AchievementException(
      message: message,
      code: 'REQUIREMENTS_NOT_MET',
      details: {
        if (achievementId != null) 'achievementId': achievementId,
        if (missingRequirements != null) 'missingRequirements': missingRequirements,
      },
    );
  }

  /// Achievement expired
  factory AchievementException.expired({
    String message = 'Achievement has expired',
    String? achievementId,
    DateTime? expiredAt,
  }) {
    return AchievementException(
      message: message,
      code: 'ACHIEVEMENT_EXPIRED',
      details: {
        if (achievementId != null) 'achievementId': achievementId,
        if (expiredAt != null) 'expiredAt': expiredAt.toIso8601String(),
      },
    );
  }

  /// Invalid achievement data
  factory AchievementException.invalidData({
    String message = 'Invalid achievement data',
    Map<String, dynamic>? invalidFields,
  }) {
    return AchievementException(
      message: message,
      code: 'INVALID_ACHIEVEMENT_DATA',
      details: invalidFields,
    );
  }
}
