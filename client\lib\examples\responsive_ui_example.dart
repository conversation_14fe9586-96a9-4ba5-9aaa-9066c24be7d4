import 'package:flutter/material.dart';

import '../core/layout/adaptive_layout.dart';
import '../core/utils/responsive_utils.dart';
import '../core/theme/design_tokens.dart';
import '../components/quester_app_bar.dart';

/// Example implementation of Universal Responsive UI
/// 
/// Demonstrates how to use the optimized responsive system with:
/// - Adaptive layouts for different screen sizes
/// - Responsive grids and containers
/// - Material 3 design tokens
/// - Cross-platform optimization
class ResponsiveUIExample extends StatefulWidget {
  const ResponsiveUIExample({super.key});

  @override
  State<ResponsiveUIExample> createState() => _ResponsiveUIExampleState();
}

class _ResponsiveUIExampleState extends State<ResponsiveUIExample> {
  int selectedIndex = 0;

  final List<NavigationDestination> destinations = [
    const NavigationDestination(
      icon: Icon(Icons.home_outlined),
      selectedIcon: Icon(Icons.home),
      label: 'Home',
    ),
    const NavigationDestination(
      icon: Icon(Icons.explore_outlined),
      selectedIcon: Icon(Icons.explore),
      label: 'Explore',
    ),
    const NavigationDestination(
      icon: Icon(Icons.notifications_outlined),
      selectedIcon: Icon(Icons.notifications),
      label: 'Notifications',
    ),
    const NavigationDestination(
      icon: Icon(Icons.account_circle_outlined),
      selectedIcon: Icon(Icons.account_circle),
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return AdaptiveLayout(
      appBar: const QuesterAppBar(),
      destinations: destinations,
      selectedIndex: selectedIndex,
      onDestinationSelected: (index) {
        setState(() {
          selectedIndex = index;
        });
      },
      child: _buildContent(context),
      floatingActionButton: ResponsiveUtils.isMobile(context)
          ? FloatingActionButton(
              onPressed: () {},
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildContent(BuildContext context) {
    return ResponsiveContainer(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero section with responsive text
            _buildHeroSection(context),
            
            const ResponsiveSpacing(
              xs: DesignTokens.spaceLarge,
              md: DesignTokens.spaceXLarge,
              xl: DesignTokens.spaceXXLarge,
            ),
            
            // Responsive grid of cards
            _buildCardGrid(context),
            
            const ResponsiveSpacing(
              xs: DesignTokens.spaceLarge,
              md: DesignTokens.spaceXLarge,
              xl: DesignTokens.spaceXXLarge,
            ),
            
            // Feature showcase
            _buildFeatureShowcase(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return ResponsiveCard(
      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Universal Responsive UI',
            style: Theme.of(context).textTheme.displaySmall?.copyWith(
              fontSize: ResponsiveUtils.getResponsiveValue(
                context,
                xs: DesignTokens.headlineMedium,
                md: DesignTokens.headlineLarge,
                xl: DesignTokens.displaySmall,
                fallback: DesignTokens.headlineMedium,
              ),
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          
          const ResponsiveSpacing(
            xs: DesignTokens.spaceSmall,
            md: DesignTokens.spaceMedium,
          ),
          
          Text(
            'Experience seamless design across all devices with Material 3 and responsive breakpoints.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontSize: ResponsiveUtils.getResponsiveValue(
                context,
                xs: DesignTokens.bodyMedium,
                md: DesignTokens.bodyLarge,
                xl: 18.0,
                fallback: DesignTokens.bodyMedium,
              ),
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardGrid(BuildContext context) {
    final features = [
      _FeatureData(
        icon: Icons.devices,
        title: 'Cross-Platform',
        description: 'Works seamlessly on mobile, tablet, and desktop',
      ),
      _FeatureData(
        icon: Icons.palette,
        title: 'Material 3',
        description: 'Latest Material Design with dynamic theming',
      ),
      _FeatureData(
        icon: Icons.speed,
        title: 'Optimized',
        description: 'High performance with efficient rendering',
      ),
      _FeatureData(
        icon: Icons.accessibility,
        title: 'Accessible',
        description: 'Built with accessibility best practices',
      ),
      _FeatureData(
        icon: Icons.code,
        title: 'Developer Friendly',
        description: 'Clean APIs and comprehensive documentation',
      ),
      _FeatureData(
        icon: Icons.update,
        title: 'Future Ready',
        description: 'Designed for scalability and maintainability',
      ),
    ];

    return ResponsiveGrid(
      children: features.map((feature) => _buildFeatureCard(context, feature)).toList(),
    );
  }

  Widget _buildFeatureCard(BuildContext context, _FeatureData feature) {
    return ResponsiveCard(
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Tapped ${feature.title}')),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            feature.icon,
            size: ResponsiveUtils.getResponsiveValue(
              context,
              xs: DesignTokens.iconLarge,
              md: DesignTokens.iconXLarge,
              xl: DesignTokens.iconXXLarge,
              fallback: DesignTokens.iconLarge,
            ),
            color: Theme.of(context).colorScheme.primary,
          ),
          
          const ResponsiveSpacing(
            xs: DesignTokens.spaceSmall,
            md: DesignTokens.spaceMedium,
          ),
          
          Text(
            feature.title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const ResponsiveSpacing(
            xs: DesignTokens.spaceXSmall,
            md: DesignTokens.spaceSmall,
          ),
          
          Text(
            feature.description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureShowcase(BuildContext context) {
    return ResponsiveUtils.responsive(
      context: context,
      xs: _buildVerticalShowcase(context),
      sm: _buildVerticalShowcase(context),
      md: _buildHorizontalShowcase(context),
      lg: _buildHorizontalShowcase(context),
      xl: _buildHorizontalShowcase(context),
      xxl: _buildHorizontalShowcase(context),
      fallback: _buildVerticalShowcase(context),
    );
  }

  Widget _buildVerticalShowcase(BuildContext context) {
    return Column(
      children: [
        _buildShowcaseImage(context),
        const ResponsiveSpacing(xs: DesignTokens.spaceMedium),
        _buildShowcaseContent(context),
      ],
    );
  }

  Widget _buildHorizontalShowcase(BuildContext context) {
    return Row(
      children: [
        Expanded(flex: 2, child: _buildShowcaseContent(context)),
        const ResponsiveSpacing.horizontal(md: DesignTokens.spaceLarge),
        Expanded(flex: 3, child: _buildShowcaseImage(context)),
      ],
    );
  }

  Widget _buildShowcaseImage(BuildContext context) {
    return ResponsiveCard(
      backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: Center(
          child: Icon(
            Icons.image,
            size: ResponsiveUtils.getResponsiveValue(
              context,
              xs: 48.0,
              md: 64.0,
              xl: 80.0,
              fallback: 48.0,
            ),
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  Widget _buildShowcaseContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Responsive Design System',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const ResponsiveSpacing(xs: DesignTokens.spaceSmall),
        
        Text(
          'Our universal responsive UI system adapts to any screen size, providing optimal user experience across all devices and platforms.',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        
        const ResponsiveSpacing(xs: DesignTokens.spaceMedium),
        
        ElevatedButton(
          onPressed: () {},
          child: const Text('Learn More'),
        ),
      ],
    );
  }
}

class _FeatureData {
  final IconData icon;
  final String title;
  final String description;

  _FeatureData({
    required this.icon,
    required this.title,
    required this.description,
  });
}
