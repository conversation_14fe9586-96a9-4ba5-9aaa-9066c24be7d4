import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../components/quester_app_bar.dart';

/// Example usage of QuesterAppLayout with navigation destinations
/// 
/// This demonstrates the responsive app layout with:
/// - Horizontal app bar at the top
/// - Bottom navigation for mobile/tablet
/// - Side navigation rail for desktop
/// - Responsive behavior based on screen size
class QuesterAppLayoutExample extends StatefulWidget {
  const QuesterAppLayoutExample({super.key});

  @override
  State<QuesterAppLayoutExample> createState() => _QuesterAppLayoutExampleState();
}

class _QuesterAppLayoutExampleState extends State<QuesterAppLayoutExample> {
  int selectedIndex = 0;

  final List<NavigationDestination> destinations = [
    const NavigationDestination(
      icon: Icon(Icons.home_outlined),
      selectedIcon: Icon(Icons.home),
      label: 'Home',
    ),
    const NavigationDestination(
      icon: Icon(Icons.explore_outlined),
      selectedIcon: Icon(Icons.explore),
      label: 'Explore',
    ),
    const NavigationDestination(
      icon: Icon(Icons.assignment_outlined),
      selectedIcon: Icon(Icons.assignment),
      label: 'Quests',
    ),
    const NavigationDestination(
      icon: Icon(Icons.notifications_outlined),
      selectedIcon: Icon(Icons.notifications),
      label: 'Notifications',
    ),
    const NavigationDestination(
      icon: Icon(Icons.account_circle_outlined),
      selectedIcon: Icon(Icons.account_circle),
      label: 'Profile',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return QuesterAppLayout(
      destinations: destinations,
      selectedIndex: selectedIndex,
      onDestinationSelected: (index) {
        setState(() {
          selectedIndex = index;
        });
        _navigateToPage(index);
      },
      onAccountTap: () {
        // Handle account tap - navigate to profile
        setState(() {
          selectedIndex = 4; // Profile tab
        });
        context.go('/profile');
      },
      floatingActionButton: selectedIndex == 0 // Only show on home
          ? FloatingActionButton(
              onPressed: () {
                // Handle create new quest
                _showCreateQuestDialog();
              },
              child: const Icon(Icons.add),
              tooltip: 'Create Quest',
            )
          : null,
      child: _buildPageContent(),
    );
  }

  /// Build page content based on selected index
  Widget _buildPageContent() {
    switch (selectedIndex) {
      case 0:
        return _buildHomePage();
      case 1:
        return _buildExplorePage();
      case 2:
        return _buildQuestsPage();
      case 3:
        return _buildNotificationsPage();
      case 4:
        return _buildProfilePage();
      default:
        return const Center(child: Text('Unknown Page'));
    }
  }

  Widget _buildHomePage() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome to Quester',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'Your quest management dashboard',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 32),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.explore, size: 64, color: Colors.blue),
                  SizedBox(height: 16),
                  Text(
                    'Start your quest journey!',
                    style: TextStyle(fontSize: 18),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExplorePage() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.explore, size: 64, color: Colors.green),
          SizedBox(height: 16),
          Text(
            'Explore Page',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Discover new quests and adventures'),
        ],
      ),
    );
  }

  Widget _buildQuestsPage() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.assignment, size: 64, color: Colors.orange),
          SizedBox(height: 16),
          Text(
            'Quests Page',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Manage your active and completed quests'),
        ],
      ),
    );
  }

  Widget _buildNotificationsPage() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications, size: 64, color: Colors.red),
          SizedBox(height: 16),
          Text(
            'Notifications Page',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Stay updated with your quest progress'),
        ],
      ),
    );
  }

  Widget _buildProfilePage() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_circle, size: 64, color: Colors.purple),
          SizedBox(height: 16),
          Text(
            'Profile Page',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Manage your account and preferences'),
        ],
      ),
    );
  }

  /// Handle navigation based on selected index
  void _navigateToPage(int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/explore');
        break;
      case 2:
        context.go('/quests');
        break;
      case 3:
        context.go('/notifications');
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }

  /// Show create quest dialog
  void _showCreateQuestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Quest'),
        content: const Text('Quest creation functionality coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
