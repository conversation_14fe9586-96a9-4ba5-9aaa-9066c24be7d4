import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'leaderboard.g.dart';

/// Leaderboard entry model
@JsonSerializable()
class LeaderboardEntry extends Equatable {
  final String userId;
  final String username;
  final String? displayName;
  final String? avatarUrl;
  final int rank;
  final int score;
  final int level;
  final int xp;
  final int questsCompleted;
  final int achievements;
  final DateTime lastActive;
  final Map<String, dynamic>? metadata;

  const LeaderboardEntry({
    required this.userId,
    required this.username,
    this.displayName,
    this.avatarUrl,
    required this.rank,
    required this.score,
    required this.level,
    required this.xp,
    required this.questsCompleted,
    required this.achievements,
    required this.lastActive,
    this.metadata,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardEntryFromJson(json);

  Map<String, dynamic> toJson() => _$LeaderboardEntryToJson(this);

  /// Get display name or fallback to username
  String get effectiveDisplayName => displayName ?? username;

  @override
  List<Object?> get props => [
        userId,
        username,
        displayName,
        avatarUrl,
        rank,
        score,
        level,
        xp,
        questsCompleted,
        achievements,
        lastActive,
        metadata,
      ];
}

/// Leaderboard model with entries and metadata
@JsonSerializable()
class Leaderboard extends Equatable {
  final String id;
  final String title;
  final String description;
  final LeaderboardType type;
  final LeaderboardTimeframe timeframe;
  final List<LeaderboardEntry> entries;
  final DateTime startDate;
  final DateTime endDate;
  final DateTime lastUpdated;
  final int totalParticipants;
  final LeaderboardEntry? currentUserEntry;
  final Map<String, dynamic>? metadata;

  const Leaderboard({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.timeframe,
    required this.entries,
    required this.startDate,
    required this.endDate,
    required this.lastUpdated,
    required this.totalParticipants,
    this.currentUserEntry,
    this.metadata,
  });

  factory Leaderboard.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardFromJson(json);

  Map<String, dynamic> toJson() => _$LeaderboardToJson(this);

  /// Get top N entries
  List<LeaderboardEntry> getTopEntries(int count) {
    return entries.take(count).toList();
  }

  /// Check if user is in top N
  bool isUserInTop(String userId, int topCount) {
    final topEntries = getTopEntries(topCount);
    return topEntries.any((entry) => entry.userId == userId);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        timeframe,
        entries,
        startDate,
        endDate,
        lastUpdated,
        totalParticipants,
        currentUserEntry,
        metadata,
      ];
}

/// Leaderboard type enumeration
enum LeaderboardType {
  @JsonValue('global')
  global,
  @JsonValue('category')
  category,
  @JsonValue('regional')
  regional,
  @JsonValue('team')
  team,
  @JsonValue('custom')
  custom,
}

/// Leaderboard timeframe enumeration
enum LeaderboardTimeframe {
  @JsonValue('daily')
  daily,
  @JsonValue('weekly')
  weekly,
  @JsonValue('monthly')
  monthly,
  @JsonValue('quarterly')
  quarterly,
  @JsonValue('yearly')
  yearly,
  @JsonValue('all_time')
  allTime,
}

/// Leaderboard statistics model
@JsonSerializable()
class LeaderboardStats extends Equatable {
  final String leaderboardId;
  final int totalParticipants;
  final int activeParticipants;
  final double averageScore;
  final int highestScore;
  final int lowestScore;
  final DateTime lastUpdated;
  final Map<String, int> scoreDistribution;

  const LeaderboardStats({
    required this.leaderboardId,
    required this.totalParticipants,
    required this.activeParticipants,
    required this.averageScore,
    required this.highestScore,
    required this.lowestScore,
    required this.lastUpdated,
    required this.scoreDistribution,
  });

  factory LeaderboardStats.fromJson(Map<String, dynamic> json) =>
      _$LeaderboardStatsFromJson(json);

  Map<String, dynamic> toJson() => _$LeaderboardStatsToJson(this);

  @override
  List<Object?> get props => [
        leaderboardId,
        totalParticipants,
        activeParticipants,
        averageScore,
        highestScore,
        lowestScore,
        lastUpdated,
        scoreDistribution,
      ];
}
