import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'achievement.g.dart';

/// Achievement model for gamification system
@JsonSerializable()
class Achievement extends Equatable {
  final String id;
  final String title;
  final String description;
  final String? iconUrl;
  final AchievementCategory category;
  final AchievementRarity rarity;
  final int xpReward;
  final int? coinReward;
  final List<AchievementRequirement> requirements;
  final bool isHidden;
  final bool isRepeatable;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final Map<String, dynamic>? metadata;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    this.iconUrl,
    required this.category,
    required this.rarity,
    required this.xpReward,
    this.coinReward,
    required this.requirements,
    this.isHidden = false,
    this.isRepeatable = false,
    required this.createdAt,
    this.expiresAt,
    this.metadata,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) =>
      _$AchievementFromJson(json);

  Map<String, dynamic> toJson() => _$AchievementToJson(this);

  /// Get total reward value (XP + coins)
  int get totalRewardValue => xpReward + (coinReward ?? 0);

  /// Check if achievement is currently available
  bool get isAvailable {
    if (expiresAt == null) return true;
    return DateTime.now().isBefore(expiresAt!);
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        iconUrl,
        category,
        rarity,
        xpReward,
        coinReward,
        requirements,
        isHidden,
        isRepeatable,
        createdAt,
        expiresAt,
        metadata,
      ];
}

/// User achievement progress model
@JsonSerializable()
class UserAchievement extends Equatable {
  final String id;
  final String userId;
  final String achievementId;
  final Achievement? achievement;
  final AchievementStatus status;
  final double progress;
  final DateTime? unlockedAt;
  final DateTime? expiresAt;
  final Map<String, dynamic>? progressData;

  const UserAchievement({
    required this.id,
    required this.userId,
    required this.achievementId,
    this.achievement,
    required this.status,
    required this.progress,
    this.unlockedAt,
    this.expiresAt,
    this.progressData,
  });

  factory UserAchievement.fromJson(Map<String, dynamic> json) =>
      _$UserAchievementFromJson(json);

  Map<String, dynamic> toJson() => _$UserAchievementToJson(this);

  /// Check if achievement is completed
  bool get isCompleted => status == AchievementStatus.unlocked;

  /// Check if achievement is in progress
  bool get isInProgress => status == AchievementStatus.inProgress;

  /// Get progress percentage (0-100)
  double get progressPercentage => (progress * 100).clamp(0, 100);

  @override
  List<Object?> get props => [
        id,
        userId,
        achievementId,
        achievement,
        status,
        progress,
        unlockedAt,
        expiresAt,
        progressData,
      ];
}

/// Achievement requirement model
@JsonSerializable()
class AchievementRequirement extends Equatable {
  final String id;
  final AchievementRequirementType type;
  final String description;
  final Map<String, dynamic> criteria;
  final int targetValue;
  final String? unit;

  const AchievementRequirement({
    required this.id,
    required this.type,
    required this.description,
    required this.criteria,
    required this.targetValue,
    this.unit,
  });

  factory AchievementRequirement.fromJson(Map<String, dynamic> json) =>
      _$AchievementRequirementFromJson(json);

  Map<String, dynamic> toJson() => _$AchievementRequirementToJson(this);

  @override
  List<Object?> get props => [id, type, description, criteria, targetValue, unit];
}

/// Achievement category enumeration
enum AchievementCategory {
  @JsonValue('milestone')
  milestone,
  @JsonValue('quest')
  quest,
  @JsonValue('social')
  social,
  @JsonValue('skill')
  skill,
  @JsonValue('exploration')
  exploration,
  @JsonValue('collection')
  collection,
  @JsonValue('time')
  time,
  @JsonValue('special')
  special,
}

/// Achievement rarity enumeration
enum AchievementRarity {
  @JsonValue('common')
  common,
  @JsonValue('uncommon')
  uncommon,
  @JsonValue('rare')
  rare,
  @JsonValue('epic')
  epic,
  @JsonValue('legendary')
  legendary,
}

/// Achievement status enumeration
enum AchievementStatus {
  @JsonValue('locked')
  locked,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('unlocked')
  unlocked,
  @JsonValue('expired')
  expired,
}

/// Achievement requirement type enumeration
enum AchievementRequirementType {
  @JsonValue('quest_completion')
  questCompletion,
  @JsonValue('xp_earned')
  xpEarned,
  @JsonValue('level_reached')
  levelReached,
  @JsonValue('streak_maintained')
  streakMaintained,
  @JsonValue('time_spent')
  timeSpent,
  @JsonValue('social_interaction')
  socialInteraction,
  @JsonValue('custom')
  custom,
}

/// Achievement summary for user profile
@JsonSerializable()
class AchievementSummary extends Equatable {
  final int totalAchievements;
  final int unlockedAchievements;
  final int inProgressAchievements;
  final int lockedAchievements;
  final double completionPercentage;
  final int totalXpEarned;
  final int totalCoinsEarned;
  final List<UserAchievement> recentUnlocked;
  final Map<AchievementCategory, int> categoryBreakdown;

  const AchievementSummary({
    required this.totalAchievements,
    required this.unlockedAchievements,
    required this.inProgressAchievements,
    required this.lockedAchievements,
    required this.completionPercentage,
    required this.totalXpEarned,
    required this.totalCoinsEarned,
    required this.recentUnlocked,
    required this.categoryBreakdown,
  });

  factory AchievementSummary.fromJson(Map<String, dynamic> json) =>
      _$AchievementSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$AchievementSummaryToJson(this);

  @override
  List<Object?> get props => [
        totalAchievements,
        unlockedAchievements,
        inProgressAchievements,
        lockedAchievements,
        completionPercentage,
        totalXpEarned,
        totalCoinsEarned,
        recentUnlocked,
        categoryBreakdown,
      ];
}
