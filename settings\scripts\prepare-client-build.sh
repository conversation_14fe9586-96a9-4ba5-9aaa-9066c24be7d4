#!/bin/bash

# <PERSON><PERSON>t to prepare client directory for Docker build by removing problematic symlinks
set -e

echo "Preparing client directory for Docker build..."

# Navigate to the client directory
cd "$(dirname "$0")/../client"

# Remove all symlinks
echo "Removing all symlinks..."
find . -type l -delete 2>/dev/null || true

# Remove ephemeral directories that might contain symlinks
echo "Removing ephemeral directories..."
rm -rf linux/flutter/ephemeral/ 2>/dev/null || true
rm -rf macos/Flutter/ephemeral/ 2>/dev/null || true
rm -rf windows/flutter/ephemeral/ 2>/dev/null || true

# Remove plugin symlink directories
echo "Removing plugin symlink directories..."
find . -name ".plugin_symlinks" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "ephemeral" -type d -exec rm -rf {} + 2>/dev/null || true

# Remove build artifacts that might contain symlinks
echo "Removing build artifacts..."
rm -rf .dart_tool/ 2>/dev/null || true
rm -rf build/ 2>/dev/null || true

echo "Client directory prepared for Docker build."
