# Redis Configuration for Quester Application
# Based on Redis 7.x configuration

# Network Configuration
bind 0.0.0.0
port 6379
protected-mode no

# General Configuration
daemonize no
pidfile /var/run/redis.pid
loglevel notice
logfile ""

# Persistence Configuration
save 900 1
save 300 10
save 60 10000

# Enable AOF persistence for better durability
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# Memory Configuration
maxmemory 256mb
maxmemory-policy allkeys-lru

# Security Configuration
# Password will be set via command line: --requirepass <password>
# requirepass your_password_here

# Client Configuration
timeout 300
tcp-keepalive 300

# Slow Log Configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Performance Configuration
tcp-backlog 511
databases 16

# Disable dangerous commands in production
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""
# rename-command SHUTDOWN SHUTDOWN_d9f5a8f3
# rename-command DEBUG ""

# Enable notifications for keyspace events
notify-keyspace-events "Ex"

# Memory usage optimization
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog sparse representation optimization
hll-sparse-max-bytes 3000

# Streams configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Background saving configuration
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication configuration (for future clustering)
# replica-serve-stale-data yes
# replica-read-only yes
# replica-diskless-sync no
# replica-diskless-sync-delay 5
