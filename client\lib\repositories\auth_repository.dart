import 'package:shared/shared.dart' hide ApiService, StorageService;

import '../services/api_service.dart';
import '../services/storage_service.dart';

/// Repository for authentication-related operations
class AuthRepository {
  final ApiService _apiService;
  final StorageService _storageService;

  AuthRepository({
    required ApiService apiService,
    required StorageService storageService,
  })  : _apiService = apiService,
        _storageService = storageService;

  /// Register a new user
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String email,
    required String password,
    required String username,
    String? displayName,
  }) async {
    return await _apiService.post('/auth/register', data: {
      'email': email,
      'password': password,
      'username': username,
      'displayName': displayName,
    });
  }

  /// Login with email and password
  Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    return await _apiService.post('/auth/login', data: {
      'email': email,
      'password': password,
      'rememberMe': rememberMe,
    });
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    return await _apiService.post('/auth/logout');
  }

  /// Refresh authentication token
  Future<ApiResponse<Map<String, dynamic>>> refreshToken() async {
    final refreshToken = await _storageService.getRefreshToken();
    if (refreshToken == null) {
      throw Exception('No refresh token available');
    }

    return await _apiService.post('/auth/refresh', data: {
      'refreshToken': refreshToken,
    });
  }

  /// Forgot password
  Future<ApiResponse<void>> forgotPassword({
    required String email,
  }) async {
    return await _apiService.post('/auth/forgot-password', data: {
      'email': email,
    });
  }

  /// Reset password
  Future<ApiResponse<void>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    return await _apiService.post('/auth/reset-password', data: {
      'token': token,
      'newPassword': newPassword,
    });
  }

  /// Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    return await _apiService.post('/auth/change-password', data: {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
    });
  }

  /// Verify email
  Future<ApiResponse<void>> verifyEmail({
    required String token,
  }) async {
    return await _apiService.post('/auth/verify-email', data: {
      'token': token,
    });
  }

  /// Resend email verification
  Future<ApiResponse<void>> resendEmailVerification() async {
    return await _apiService.post('/auth/resend-verification');
  }

  /// Check if email is available
  Future<ApiResponse<Map<String, dynamic>>> checkEmailAvailability({
    required String email,
  }) async {
    return await _apiService.get('/auth/check-email', queryParameters: {
      'email': email,
    });
  }

  /// Check if username is available
  Future<ApiResponse<Map<String, dynamic>>> checkUsernameAvailability({
    required String username,
  }) async {
    return await _apiService.get('/auth/check-username', queryParameters: {
      'username': username,
    });
  }

  /// Get current user profile
  Future<ApiResponse<Map<String, dynamic>>> getCurrentUser() async {
    return await _apiService.get('/users/me');
  }

  /// Update user profile
  Future<ApiResponse<Map<String, dynamic>>> updateProfile({
    String? displayName,
    String? bio,
    String? avatarUrl,
  }) async {
    final data = <String, dynamic>{};
    if (displayName != null) data['displayName'] = displayName;
    if (bio != null) data['bio'] = bio;
    if (avatarUrl != null) data['avatarUrl'] = avatarUrl;

    return await _apiService.put('/users/me', data: data);
  }

  /// Delete user account
  Future<ApiResponse<void>> deleteAccount({
    required String password,
  }) async {
    return await _apiService.delete('/users/me', data: {
      'password': password,
    });
  }

  /// Enable two-factor authentication
  Future<ApiResponse<Map<String, dynamic>>> enableTwoFactor() async {
    return await _apiService.post('/auth/2fa/enable');
  }

  /// Disable two-factor authentication
  Future<ApiResponse<void>> disableTwoFactor({
    required String code,
  }) async {
    return await _apiService.post('/auth/2fa/disable', data: {
      'code': code,
    });
  }

  /// Verify two-factor authentication code
  Future<ApiResponse<Map<String, dynamic>>> verifyTwoFactor({
    required String code,
  }) async {
    return await _apiService.post('/auth/2fa/verify', data: {
      'code': code,
    });
  }

  /// Get backup codes for two-factor authentication
  Future<ApiResponse<Map<String, dynamic>>> getTwoFactorBackupCodes() async {
    return await _apiService.get('/auth/2fa/backup-codes');
  }

  /// Regenerate backup codes for two-factor authentication
  Future<ApiResponse<Map<String, dynamic>>> regenerateTwoFactorBackupCodes() async {
    return await _apiService.post('/auth/2fa/backup-codes/regenerate');
  }

  /// Get user sessions
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserSessions() async {
    return await _apiService.get('/auth/sessions');
  }

  /// Revoke a specific session
  Future<ApiResponse<void>> revokeSession({
    required String sessionId,
  }) async {
    return await _apiService.delete('/auth/sessions/$sessionId');
  }

  /// Revoke all other sessions
  Future<ApiResponse<void>> revokeAllOtherSessions() async {
    return await _apiService.post('/auth/sessions/revoke-others');
  }

  /// Get login history
  Future<ApiResponse<List<Map<String, dynamic>>>> getLoginHistory({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/auth/login-history', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Report suspicious activity
  Future<ApiResponse<void>> reportSuspiciousActivity({
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    return await _apiService.post('/auth/report-suspicious', data: {
      'description': description,
      'metadata': metadata,
    });
  }

  /// Get account security settings
  Future<ApiResponse<Map<String, dynamic>>> getSecuritySettings() async {
    return await _apiService.get('/auth/security-settings');
  }

  /// Update account security settings
  Future<ApiResponse<void>> updateSecuritySettings({
    bool? emailNotifications,
    bool? smsNotifications,
    bool? loginAlerts,
    bool? deviceTracking,
  }) async {
    final data = <String, dynamic>{};
    if (emailNotifications != null) data['emailNotifications'] = emailNotifications;
    if (smsNotifications != null) data['smsNotifications'] = smsNotifications;
    if (loginAlerts != null) data['loginAlerts'] = loginAlerts;
    if (deviceTracking != null) data['deviceTracking'] = deviceTracking;

    return await _apiService.put('/auth/security-settings', data: data);
  }

  /// Get privacy settings
  Future<ApiResponse<Map<String, dynamic>>> getPrivacySettings() async {
    return await _apiService.get('/auth/privacy-settings');
  }

  /// Update privacy settings
  Future<ApiResponse<void>> updatePrivacySettings({
    bool? profileVisibility,
    bool? activityVisibility,
    bool? friendsVisibility,
    bool? achievementsVisibility,
  }) async {
    final data = <String, dynamic>{};
    if (profileVisibility != null) data['profileVisibility'] = profileVisibility;
    if (activityVisibility != null) data['activityVisibility'] = activityVisibility;
    if (friendsVisibility != null) data['friendsVisibility'] = friendsVisibility;
    if (achievementsVisibility != null) data['achievementsVisibility'] = achievementsVisibility;

    return await _apiService.put('/auth/privacy-settings', data: data);
  }

  /// Export user data (GDPR compliance)
  Future<ApiResponse<Map<String, dynamic>>> exportUserData() async {
    return await _apiService.post('/auth/export-data');
  }

  /// Request data deletion (GDPR compliance)
  Future<ApiResponse<void>> requestDataDeletion({
    required String reason,
  }) async {
    return await _apiService.post('/auth/request-deletion', data: {
      'reason': reason,
    });
  }
}
