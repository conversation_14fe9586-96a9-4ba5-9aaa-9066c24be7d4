import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'notification.g.dart';

/// Notification type enumeration
enum NotificationType {
  @JsonValue('info')
  info,
  @JsonValue('success')
  success,
  @JsonValue('warning')
  warning,
  @JsonValue('error')
  error,
  @JsonValue('quest_complete')
  questComplete,
  @JsonValue('quest_assigned')
  questAssigned,
  @JsonValue('achievement_unlocked')
  achievementUnlocked,
  @JsonValue('level_up')
  levelUp,
  @JsonValue('friend_request')
  friendRequest,
  @JsonValue('system')
  system,
}

/// Notification priority levels
enum NotificationPriority {
  @JsonValue('low')
  low,
  @JsonValue('normal')
  normal,
  @JsonValue('high')
  high,
  @JsonValue('urgent')
  urgent,
}

/// Main notification model for real-time updates
@JsonSerializable()
class Notification extends Equatable {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? expiresAt;
  final String? userId;
  final String? actionUrl;
  final Map<String, dynamic>? metadata;
  final bool isPersistent;

  const Notification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.createdAt,
    this.readAt,
    this.expiresAt,
    this.userId,
    this.actionUrl,
    this.metadata,
    this.isPersistent = false,
  });

  /// Create notification from JSON
  factory Notification.fromJson(Map<String, dynamic> json) =>
      _$NotificationFromJson(json);

  /// Convert notification to JSON
  Map<String, dynamic> toJson() => _$NotificationToJson(this);

  /// Factory for quest completion notifications
  factory Notification.questComplete({
    required String id,
    required String questTitle,
    required int xpReward,
    required String userId,
    String? actionUrl,
  }) {
    return Notification(
      id: id,
      title: 'Quest Complete! 🎉',
      message: 'You completed "$questTitle" and earned $xpReward XP!',
      type: NotificationType.questComplete,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      userId: userId,
      actionUrl: actionUrl,
      metadata: {
        'questTitle': questTitle,
        'xpReward': xpReward,
        'type': 'quest_completion',
      },
      isPersistent: true,
    );
  }

  /// Factory for quest assignment notifications
  factory Notification.questAssigned({
    required String id,
    required String questTitle,
    required String userId,
    String? actionUrl,
  }) {
    return Notification(
      id: id,
      title: 'New Quest Available! ⚔️',
      message: 'A new quest "$questTitle" has been assigned to you.',
      type: NotificationType.questAssigned,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
      userId: userId,
      actionUrl: actionUrl,
      metadata: {
        'questTitle': questTitle,
        'type': 'quest_assignment',
      },
    );
  }

  /// Factory for level up notifications
  factory Notification.levelUp({
    required String id,
    required int newLevel,
    required String userId,
    String? actionUrl,
  }) {
    return Notification(
      id: id,
      title: 'Level Up! 🚀',
      message: 'Congratulations! You reached level $newLevel!',
      type: NotificationType.levelUp,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      userId: userId,
      actionUrl: actionUrl,
      metadata: {
        'newLevel': newLevel,
        'type': 'level_up',
      },
      isPersistent: true,
    );
  }

  /// Factory for achievement notifications
  factory Notification.achievementUnlocked({
    required String id,
    required String achievementName,
    required String userId,
    String? actionUrl,
  }) {
    return Notification(
      id: id,
      title: 'Achievement Unlocked! 🏆',
      message: 'You unlocked the "$achievementName" achievement!',
      type: NotificationType.achievementUnlocked,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      userId: userId,
      actionUrl: actionUrl,
      metadata: {
        'achievementName': achievementName,
        'type': 'achievement',
      },
      isPersistent: true,
    );
  }

  /// Factory for system notifications
  factory Notification.system({
    required String id,
    required String title,
    required String message,
    NotificationPriority priority = NotificationPriority.normal,
    String? actionUrl,
    Map<String, dynamic>? metadata,
  }) {
    return Notification(
      id: id,
      title: title,
      message: message,
      type: NotificationType.system,
      priority: priority,
      createdAt: DateTime.now(),
      actionUrl: actionUrl,
      metadata: metadata,
    );
  }

  /// Create a copy with updated fields
  Notification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? expiresAt,
    String? userId,
    String? actionUrl,
    Map<String, dynamic>? metadata,
    bool? isPersistent,
  }) {
    return Notification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
      userId: userId ?? this.userId,
      actionUrl: actionUrl ?? this.actionUrl,
      metadata: metadata ?? this.metadata,
      isPersistent: isPersistent ?? this.isPersistent,
    );
  }

  /// Mark notification as read
  Notification markAsRead() {
    return copyWith(readAt: DateTime.now());
  }

  /// Check if notification is read
  bool get isRead => readAt != null;

  /// Check if notification is expired
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  /// Check if notification is still valid
  bool get isValid => !isExpired;

  @override
  List<Object?> get props => [
        id,
        title,
        message,
        type,
        priority,
        createdAt,
        readAt,
        expiresAt,
        userId,
        actionUrl,
        metadata,
        isPersistent,
      ];
}
