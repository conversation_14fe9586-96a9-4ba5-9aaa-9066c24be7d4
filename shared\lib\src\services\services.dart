/// Shared service interfaces and implementations
/// 
/// This file exports all service interfaces and implementations used across
/// the client and server applications for consistent business logic.
library;

// Service interfaces
export 'validation_service.dart';
export 'websocket_service.dart';
export 'api_service.dart';
export 'auth_service.dart';
export 'storage_service.dart';
export 'repository_service.dart';
