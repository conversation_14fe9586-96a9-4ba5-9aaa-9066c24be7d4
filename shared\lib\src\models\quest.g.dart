// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Quest _$QuestFromJson(Map<String, dynamic> json) => Quest(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  difficulty: $enumDecode(_$QuestDifficultyEnumMap, json['difficulty']),
  status: $enumDecode(_$QuestStatusEnumMap, json['status']),
  category: $enumDecode(_$QuestCategoryEnumMap, json['category']),
  xpReward: (json['xpReward'] as num).toInt(),
  coinReward: (json['coinReward'] as num?)?.toInt(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  startedAt: json['startedAt'] == null
      ? null
      : DateTime.parse(json['startedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
  assignedUserId: json['assignedUserId'] as String?,
  requirements:
      (json['requirements'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  metadata: json['metadata'] as Map<String, dynamic>?,
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$QuestToJson(Quest instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'description': instance.description,
  'difficulty': _$QuestDifficultyEnumMap[instance.difficulty]!,
  'status': _$QuestStatusEnumMap[instance.status]!,
  'category': _$QuestCategoryEnumMap[instance.category]!,
  'xpReward': instance.xpReward,
  'coinReward': instance.coinReward,
  'createdAt': instance.createdAt.toIso8601String(),
  'startedAt': instance.startedAt?.toIso8601String(),
  'completedAt': instance.completedAt?.toIso8601String(),
  'expiresAt': instance.expiresAt?.toIso8601String(),
  'assignedUserId': instance.assignedUserId,
  'requirements': instance.requirements,
  'metadata': instance.metadata,
  'isActive': instance.isActive,
};

const _$QuestDifficultyEnumMap = {
  QuestDifficulty.easy: 'easy',
  QuestDifficulty.medium: 'medium',
  QuestDifficulty.hard: 'hard',
  QuestDifficulty.extreme: 'extreme',
};

const _$QuestStatusEnumMap = {
  QuestStatus.pending: 'pending',
  QuestStatus.inProgress: 'in_progress',
  QuestStatus.completed: 'completed',
  QuestStatus.failed: 'failed',
  QuestStatus.cancelled: 'cancelled',
};

const _$QuestCategoryEnumMap = {
  QuestCategory.tutorial: 'tutorial',
  QuestCategory.adventure: 'adventure',
  QuestCategory.challenge: 'challenge',
  QuestCategory.daily: 'daily',
  QuestCategory.weekly: 'weekly',
  QuestCategory.special: 'special',
};

QuestCompletionRequest _$QuestCompletionRequestFromJson(
  Map<String, dynamic> json,
) => QuestCompletionRequest(
  questId: json['questId'] as String,
  userId: json['userId'] as String,
  completionData: json['completionData'] as Map<String, dynamic>?,
  completedAt: DateTime.parse(json['completedAt'] as String),
);

Map<String, dynamic> _$QuestCompletionRequestToJson(
  QuestCompletionRequest instance,
) => <String, dynamic>{
  'questId': instance.questId,
  'userId': instance.userId,
  'completionData': instance.completionData,
  'completedAt': instance.completedAt.toIso8601String(),
};
