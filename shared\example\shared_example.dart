import 'package:shared/shared.dart';

void main() {
  // Example usage of shared package
  print('Shared package loaded successfully!');

  // Example: Create a user model
  final user = User(
    id: 'example-id',
    email: '<EMAIL>',
    username: 'example_user',
    firstName: 'Example',
    lastName: 'User',
    displayName: 'Example User',
    createdAt: DateTime.now(),
  );

  print('Created user: ${user.displayName} (${user.email})');

  // Example: Create an API response
  final response = ApiResponse<User>.success(
    data: user,
    message: 'User created successfully',
  );

  print('API Response: ${response.message}');
}
