import 'package:json_annotation/json_annotation.dart';
import 'user.dart';
import 'quest.dart';

part 'auth_models.g.dart';

/// Login request model
@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

/// Register request model
@JsonSerializable()
class RegisterRequest {
  final String username;
  final String email;
  final String password;
  final String firstName;
  final String lastName;

  const RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

/// Update user request model
@JsonSerializable()
class UpdateUserRequest {
  final String? firstName;
  final String? lastName;
  final String? username;
  final String? email;

  const UpdateUserRequest({
    this.firstName,
    this.lastName,
    this.username,
    this.email,
  });

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateUserRequestToJson(this);
}

/// Authentication response model
@JsonSerializable()
class AuthResponse {
  final User user;
  final String accessToken;
  final String refreshToken;

  const AuthResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

/// Token response model
@JsonSerializable()
class TokenResponse {
  final String accessToken;
  final String refreshToken;

  const TokenResponse({
    required this.accessToken,
    required this.refreshToken,
  });

  factory TokenResponse.fromJson(Map<String, dynamic> json) =>
      _$TokenResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TokenResponseToJson(this);
}

/// User statistics model
@JsonSerializable()
class UserStats {
  final int level;
  final int xp;
  final int totalQuests;
  final int completedQuests;
  final int failedQuests;
  final int activeQuests;
  final double completionRate;
  final int streak;
  final int achievements;
  final int rank;
  final DateTime joinDate;
  final DateTime lastActive;

  const UserStats({
    required this.level,
    required this.xp,
    required this.totalQuests,
    required this.completedQuests,
    required this.failedQuests,
    required this.activeQuests,
    required this.completionRate,
    required this.streak,
    required this.achievements,
    required this.rank,
    required this.joinDate,
    required this.lastActive,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsToJson(this);
}

/// Quest assignment model
@JsonSerializable()
class QuestAssignment {
  final String questId;
  final String userId;
  final String status;
  final double progress;
  final DateTime assignedAt;
  final String assignedBy;
  final DateTime? completedAt;
  final Quest? quest;

  const QuestAssignment({
    required this.questId,
    required this.userId,
    required this.status,
    required this.progress,
    required this.assignedAt,
    required this.assignedBy,
    this.completedAt,
    this.quest,
  });

  factory QuestAssignment.fromJson(Map<String, dynamic> json) =>
      _$QuestAssignmentFromJson(json);

  Map<String, dynamic> toJson() => _$QuestAssignmentToJson(this);
}

/// Quest completion model
@JsonSerializable()
class QuestCompletion {
  final QuestAssignment assignment;
  final QuestRewards rewards;

  const QuestCompletion({
    required this.assignment,
    required this.rewards,
  });

  factory QuestCompletion.fromJson(Map<String, dynamic> json) =>
      _$QuestCompletionFromJson(json);

  Map<String, dynamic> toJson() => _$QuestCompletionToJson(this);
}

/// Quest rewards model
@JsonSerializable()
class QuestRewards {
  final int xp;
  final int coins;

  const QuestRewards({
    required this.xp,
    required this.coins,
  });

  factory QuestRewards.fromJson(Map<String, dynamic> json) =>
      _$QuestRewardsFromJson(json);

  Map<String, dynamic> toJson() => _$QuestRewardsToJson(this);
}

/// Create quest request model
@JsonSerializable()
class CreateQuestRequest {
  final String title;
  final String description;
  final String difficulty;
  final String? category;
  final int xpReward;
  final int? coinReward;
  final DateTime? dueDate;

  const CreateQuestRequest({
    required this.title,
    required this.description,
    required this.difficulty,
    this.category,
    required this.xpReward,
    this.coinReward,
    this.dueDate,
  });

  factory CreateQuestRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateQuestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateQuestRequestToJson(this);
}

/// Update quest request model
@JsonSerializable()
class UpdateQuestRequest {
  final String title;
  final String description;
  final String difficulty;
  final String? category;
  final int xpReward;
  final int? coinReward;
  final DateTime? dueDate;
  final String? status;

  const UpdateQuestRequest({
    required this.title,
    required this.description,
    required this.difficulty,
    this.category,
    required this.xpReward,
    this.coinReward,
    this.dueDate,
    this.status,
  });

  factory UpdateQuestRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateQuestRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateQuestRequestToJson(this);
}


