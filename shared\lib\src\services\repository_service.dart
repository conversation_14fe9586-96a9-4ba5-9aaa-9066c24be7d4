import 'dart:async';
import '../models/models.dart';

/// Generic repository interface for CRUD operations
abstract class Repository<T, ID> {
  /// Create a new entity
  Future<T> create(T entity);
  
  /// Get entity by ID
  Future<T?> getById(ID id);
  
  /// Get all entities with optional pagination
  Future<List<T>> getAll({int page = 1, int pageSize = 20});
  
  /// Update entity
  Future<T> update(ID id, T entity);
  
  /// Delete entity by ID
  Future<void> delete(ID id);
  
  /// Check if entity exists
  Future<bool> exists(ID id);
  
  /// Count total entities
  Future<int> count();
  
  /// Search entities
  Future<List<T>> search(String query, {int page = 1, int pageSize = 20});
  
  /// Get entities with filter
  Future<List<T>> getFiltered(Map<String, dynamic> filters, {int page = 1, int pageSize = 20});
  
  /// Batch create entities
  Future<List<T>> createBatch(List<T> entities);
  
  /// Batch update entities
  Future<List<T>> updateBatch(List<T> entities);
  
  /// Batch delete entities
  Future<void> deleteBatch(List<ID> ids);
}

/// User repository interface
abstract class UserRepository extends Repository<User, String> {
  /// Get user by email
  Future<User?> getByEmail(String email);
  
  /// Get user by username
  Future<User?> getByUsername(String username);
  
  /// Get users by role
  Future<List<User>> getByRole(UserRole role, {int page = 1, int pageSize = 20});
  
  /// Get active users
  Future<List<User>> getActiveUsers({int page = 1, int pageSize = 20});
  
  /// Update user profile
  Future<User> updateProfile(String userId, Map<String, dynamic> updates);
  
  /// Update user avatar
  Future<User> updateAvatar(String userId, String avatarUrl);
  
  /// Deactivate user
  Future<void> deactivateUser(String userId);
  
  /// Activate user
  Future<void> activateUser(String userId);
  
  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats(String userId);
  
  /// Get user activity
  Future<List<UserActivity>> getUserActivity(String userId, {int page = 1, int pageSize = 20});
}

/// Quest repository interface
abstract class QuestRepository extends Repository<Quest, String> {
  /// Get quests by status
  Future<List<Quest>> getByStatus(QuestStatus status, {int page = 1, int pageSize = 20});
  
  /// Get quests by difficulty
  Future<List<Quest>> getByDifficulty(QuestDifficulty difficulty, {int page = 1, int pageSize = 20});
  
  /// Get quests by category
  Future<List<Quest>> getByCategory(QuestCategory category, {int page = 1, int pageSize = 20});
  
  /// Get quests assigned to user
  Future<List<Quest>> getAssignedToUser(String userId, {int page = 1, int pageSize = 20});
  
  /// Get quests created by user
  Future<List<Quest>> getCreatedByUser(String userId, {int page = 1, int pageSize = 20});
  
  /// Assign quest to user
  Future<void> assignToUser(String questId, String userId);
  
  /// Unassign quest from user
  Future<void> unassignFromUser(String questId, String userId);
  
  /// Complete quest
  Future<Quest> completeQuest(String questId, String userId, Map<String, dynamic>? completionData);
  
  /// Get quest statistics
  Future<Map<String, dynamic>> getQuestStats(String questId);
  
  /// Get popular quests
  Future<List<Quest>> getPopularQuests({int page = 1, int pageSize = 20});
  
  /// Get recent quests
  Future<List<Quest>> getRecentQuests({int page = 1, int pageSize = 20});
}

/// Notification repository interface
abstract class NotificationRepository extends Repository<Notification, String> {
  /// Get notifications for user
  Future<List<Notification>> getForUser(String userId, {int page = 1, int pageSize = 20});
  
  /// Get unread notifications for user
  Future<List<Notification>> getUnreadForUser(String userId, {int page = 1, int pageSize = 20});
  
  /// Get notifications by type
  Future<List<Notification>> getByType(NotificationType type, {int page = 1, int pageSize = 20});
  
  /// Mark notification as read
  Future<void> markAsRead(String notificationId);
  
  /// Mark all notifications as read for user
  Future<void> markAllAsReadForUser(String userId);
  
  /// Get unread count for user
  Future<int> getUnreadCountForUser(String userId);
  
  /// Delete old notifications
  Future<void> deleteOldNotifications(Duration olderThan);
  
  /// Send notification to user
  Future<Notification> sendToUser(String userId, String title, String message, {NotificationType? type, Map<String, dynamic>? data});
  
  /// Send notification to multiple users
  Future<List<Notification>> sendToUsers(List<String> userIds, String title, String message, {NotificationType? type, Map<String, dynamic>? data});
  
  /// Send broadcast notification
  Future<List<Notification>> sendBroadcast(String title, String message, {NotificationType? type, Map<String, dynamic>? data});
}

/// Achievement repository interface
abstract class AchievementRepository extends Repository<Achievement, String> {
  /// Get achievements by category
  Future<List<Achievement>> getByCategory(String category, {int page = 1, int pageSize = 20});
  
  /// Get user achievements
  Future<List<Achievement>> getUserAchievements(String userId, {int page = 1, int pageSize = 20});
  
  /// Get available achievements for user
  Future<List<Achievement>> getAvailableForUser(String userId, {int page = 1, int pageSize = 20});
  
  /// Award achievement to user
  Future<void> awardToUser(String achievementId, String userId);
  
  /// Check if user has achievement
  Future<bool> userHasAchievement(String userId, String achievementId);
  
  /// Get achievement progress for user
  Future<Map<String, dynamic>> getProgressForUser(String userId, String achievementId);
  
  /// Update achievement progress for user
  Future<void> updateProgressForUser(String userId, String achievementId, Map<String, dynamic> progress);
}

/// Leaderboard repository interface
abstract class LeaderboardRepository extends Repository<Leaderboard, String> {
  /// Get leaderboard by type
  Future<Leaderboard?> getByType(String type);
  
  /// Get user position in leaderboard
  Future<int?> getUserPosition(String leaderboardId, String userId);
  
  /// Get top users in leaderboard
  Future<List<Map<String, dynamic>>> getTopUsers(String leaderboardId, {int limit = 10});
  
  /// Get users around position
  Future<List<Map<String, dynamic>>> getUsersAroundPosition(String leaderboardId, int position, {int range = 5});
  
  /// Update user score
  Future<void> updateUserScore(String leaderboardId, String userId, int score);
  
  /// Reset leaderboard
  Future<void> reset(String leaderboardId);
  
  /// Get leaderboard statistics
  Future<Map<String, dynamic>> getStats(String leaderboardId);
}

/// Dashboard repository interface
abstract class DashboardRepository {
  /// Get dashboard statistics
  Future<DashboardStats> getStats();
  
  /// Get user dashboard data
  Future<Map<String, dynamic>> getUserDashboard(String userId);
  
  /// Get recent activity
  Future<List<UserActivity>> getRecentActivity({int page = 1, int pageSize = 20});
  
  /// Get system health
  Future<SystemHealth> getSystemHealth();
  
  /// Get analytics data
  Future<Map<String, dynamic>> getAnalytics(DateTime startDate, DateTime endDate);
  
  /// Get performance metrics
  Future<Map<String, dynamic>> getPerformanceMetrics();
}

/// Generic data access object interface
abstract class DataAccessObject<T, ID> {
  /// Initialize DAO
  Future<void> initialize();
  
  /// Close DAO
  Future<void> close();
  
  /// Begin transaction
  Future<void> beginTransaction();
  
  /// Commit transaction
  Future<void> commitTransaction();
  
  /// Rollback transaction
  Future<void> rollbackTransaction();
  
  /// Execute in transaction
  Future<R> executeInTransaction<R>(Future<R> Function() operation);
  
  /// Get connection status
  bool get isConnected;
  
  /// Test connection
  Future<bool> testConnection();
}
