/// Validation constants and regex patterns
class ValidationConstants {
  // Email validation
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static final RegExp emailRegex = RegExp(emailPattern);
  
  // Username validation - Updated to match client expectations
  static const String usernamePattern = r'^[a-zA-Z0-9_-]+$';
  static final RegExp usernameRegex = RegExp(usernamePattern);
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  
  // Password validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const String passwordUppercasePattern = r'[A-Z]';
  static const String passwordLowercasePattern = r'[a-z]';
  static const String passwordNumberPattern = r'[0-9]';
  static const String passwordSpecialCharPattern = r'[!@#$%^&*(),.?":{}|<>]';
  static final RegExp passwordUppercaseRegex = RegExp(passwordUppercasePattern);
  static final RegExp passwordLowercaseRegex = RegExp(passwordLowercasePattern);
  static final RegExp passwordNumberRegex = RegExp(passwordNumberPattern);
  static final RegExp passwordSpecialCharRegex = RegExp(passwordSpecialCharPattern);
  
  // Name validation (first name, last name)
  static const String namePattern = r"^[a-zA-Z\s\-']+$";
  static final RegExp nameRegex = RegExp(namePattern);
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Bio validation
  static const int maxBioLength = 500;
  
  // Phone number validation
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';
  static final RegExp phoneRegex = RegExp(phonePattern);
  static const int minPhoneLength = 10;
  static const int maxPhoneLength = 15;
  
  // URL validation
  static const String urlPattern = r'^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&=]*)$';
  static final RegExp urlRegex = RegExp(urlPattern);
  
  // Quest validation
  static const int minQuestTitleLength = 5;
  static const int maxQuestTitleLength = 100;
  static const int minQuestDescriptionLength = 10;
  static const int maxQuestDescriptionLength = 1000;
  static const int minXpReward = 1;
  static const int maxXpReward = 10000;
  static const int minCoinReward = 0;
  static const int maxCoinReward = 100000;
  
  // Alphanumeric validation
  static const String alphanumericPattern = r'^[a-zA-Z0-9]+$';
  static final RegExp alphanumericRegex = RegExp(alphanumericPattern);
  
  // Alphabetic validation
  static const String alphabeticPattern = r'^[a-zA-Z]+$';
  static final RegExp alphabeticRegex = RegExp(alphabeticPattern);
  
  // Numeric validation
  static const String numericPattern = r'^[0-9]+$';
  static final RegExp numericRegex = RegExp(numericPattern);
  
  // Hex color validation
  static const String hexColorPattern = r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$';
  static final RegExp hexColorRegex = RegExp(hexColorPattern);
  
  // UUID validation
  static const String uuidPattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$';
  static final RegExp uuidRegex = RegExp(uuidPattern, caseSensitive: false);
  
  // Credit card validation
  static const String creditCardPattern = r'^[0-9]{13,19}$';
  static final RegExp creditCardRegex = RegExp(creditCardPattern);
  
  // IP address validation
  static const String ipv4Pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$';
  static final RegExp ipv4Regex = RegExp(ipv4Pattern);
  
  // MAC address validation
  static const String macAddressPattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$';
  static final RegExp macAddressRegex = RegExp(macAddressPattern);
  
  // Social security number validation (US format)
  static const String ssnPattern = r'^\d{3}-?\d{2}-?\d{4}$';
  static final RegExp ssnRegex = RegExp(ssnPattern);
  
  // Postal code validation (US ZIP code)
  static const String zipCodePattern = r'^\d{5}(-\d{4})?$';
  static final RegExp zipCodeRegex = RegExp(zipCodePattern);
  
  // File extension validation
  static const List<String> imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
  static const List<String> documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
  static const List<String> videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
  static const List<String> audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'];
  
  // MIME type validation
  static const List<String> imageMimeTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/svg+xml'
  ];
  
  static const List<String> documentMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/rtf',
    'application/vnd.oasis.opendocument.text'
  ];
  
  // Date validation
  static const String datePattern = r'^\d{4}-\d{2}-\d{2}$';
  static final RegExp dateRegex = RegExp(datePattern);
  
  static const String timePattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$';
  static final RegExp timeRegex = RegExp(timePattern);
  
  static const String dateTimePattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$';
  static final RegExp dateTimeRegex = RegExp(dateTimePattern);
  
  // HTML tag validation
  static const String htmlTagPattern = r'<[^>]*>';
  static final RegExp htmlTagRegex = RegExp(htmlTagPattern);
  
  // Whitespace validation
  static const String whitespacePattern = r'^\s*$';
  static final RegExp whitespaceRegex = RegExp(whitespacePattern);
  
  // Multiple whitespace pattern
  static const String multipleWhitespacePattern = r'\s+';
  static final RegExp multipleWhitespaceRegex = RegExp(multipleWhitespacePattern);
  
  // Base64 validation
  static const String base64Pattern = r'^[A-Za-z0-9+/]*={0,2}$';
  static final RegExp base64Regex = RegExp(base64Pattern);
  
  // JSON validation pattern (basic)
  static const String jsonPattern = r'^[\[\{].*[\]\}]$';
  static final RegExp jsonRegex = RegExp(jsonPattern);
  
  // Slug validation (URL-friendly string)
  static const String slugPattern = r'^[a-z0-9]+(?:-[a-z0-9]+)*$';
  static final RegExp slugRegex = RegExp(slugPattern);
  
  // Version number validation (semantic versioning)
  static const String versionPattern = r'^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$';
  static final RegExp versionRegex = RegExp(versionPattern);
  
  // Validation error messages
  static const String emailErrorMessage = 'Please enter a valid email address';
  static const String usernameErrorMessage = 'Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens';
  static const String passwordErrorMessage = 'Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters';
  static const String nameErrorMessage = 'Name must be 2-50 characters long and contain only letters, spaces, hyphens, and apostrophes';
  static const String phoneErrorMessage = 'Please enter a valid phone number';
  static const String urlErrorMessage = 'Please enter a valid URL';
  static const String requiredFieldErrorMessage = 'This field is required';
  static const String invalidFormatErrorMessage = 'Invalid format';
  static const String tooShortErrorMessage = 'Input is too short';
  static const String tooLongErrorMessage = 'Input is too long';
  static const String invalidCharactersErrorMessage = 'Contains invalid characters';
  static const String numericOnlyErrorMessage = 'Only numbers are allowed';
  static const String alphabeticOnlyErrorMessage = 'Only letters are allowed';
  static const String alphanumericOnlyErrorMessage = 'Only letters and numbers are allowed';
  
  // File validation error messages
  static const String fileSizeErrorMessage = 'File size exceeds the maximum limit';
  static const String fileTypeErrorMessage = 'File type is not supported';
  static const String fileRequiredErrorMessage = 'Please select a file';
  
  // Quest validation error messages
  static const String questTitleErrorMessage = 'Quest title must be 5-100 characters long';
  static const String questDescriptionErrorMessage = 'Quest description must be 10-1000 characters long';
  static const String xpRewardErrorMessage = 'XP reward must be between 1 and 10,000';
  static const String coinRewardErrorMessage = 'Coin reward must be between 0 and 100,000';
  
  // Date validation error messages
  static const String dateErrorMessage = 'Please enter a valid date';
  static const String timeErrorMessage = 'Please enter a valid time';
  static const String dateTimeErrorMessage = 'Please enter a valid date and time';
  static const String futureDateErrorMessage = 'Date must be in the future';
  static const String pastDateErrorMessage = 'Date must be in the past';
  
  // Validation helper methods
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return requiredFieldErrorMessage;
    }
    if (!emailRegex.hasMatch(value)) {
      return emailErrorMessage;
    }
    return null;
  }
  
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return requiredFieldErrorMessage;
    }
    if (value.length < minUsernameLength || value.length > maxUsernameLength) {
      return usernameErrorMessage;
    }
    if (!usernameRegex.hasMatch(value)) {
      return usernameErrorMessage;
    }
    return null;
  }
  
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return requiredFieldErrorMessage;
    }
    if (value.length < minPasswordLength || value.length > maxPasswordLength) {
      return passwordErrorMessage;
    }
    if (!passwordUppercaseRegex.hasMatch(value) ||
        !passwordLowercaseRegex.hasMatch(value) ||
        !passwordNumberRegex.hasMatch(value) ||
        !passwordSpecialCharRegex.hasMatch(value)) {
      return passwordErrorMessage;
    }
    return null;
  }
  
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return requiredFieldErrorMessage;
    }
    if (value.length < minNameLength || value.length > maxNameLength) {
      return nameErrorMessage;
    }
    if (!nameRegex.hasMatch(value)) {
      return nameErrorMessage;
    }
    return null;
  }
  
  static String? validateRequired(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null ? '$fieldName is required' : requiredFieldErrorMessage;
    }
    return null;
  }
  
  static String? validateLength(String? value, int minLength, int maxLength, [String? fieldName]) {
    if (value == null) return null;
    if (value.length < minLength) {
      return fieldName != null 
          ? '$fieldName must be at least $minLength characters long'
          : 'Must be at least $minLength characters long';
    }
    if (value.length > maxLength) {
      return fieldName != null
          ? '$fieldName must not exceed $maxLength characters'
          : 'Must not exceed $maxLength characters';
    }
    return null;
  }
}
