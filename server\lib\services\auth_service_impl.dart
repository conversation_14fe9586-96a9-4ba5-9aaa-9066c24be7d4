import 'dart:async';
import 'dart:convert';
import 'package:shared/shared.dart';
import 'user_repository_impl.dart';

/// Enhanced token manager with proper JWT-like implementation
class EnhancedTokenManager implements TokenManager {
  static const String _jwtSecret = 'your-super-secret-jwt-key-change-in-production';
  static const Duration _accessTokenExpiry = Duration(hours: 1);
  static const Duration _refreshTokenExpiry = Duration(days: 7);

  final Map<String, String> _tokenStorage = {}; // In-memory storage for demo

  @override
  Future<String?> getAccessToken() async {
    return _tokenStorage['access_token'];
  }

  @override
  Future<String?> getRefreshToken() async {
    return _tokenStorage['refresh_token'];
  }

  @override
  Future<void> saveTokens({required String accessToken, String? refreshToken}) async {
    _tokenStorage['access_token'] = accessToken;
    if (refreshToken != null) {
      _tokenStorage['refresh_token'] = refreshToken;
    }
  }

  @override
  Future<void> clearTokens() async {
    _tokenStorage.clear();
  }

  @override
  bool isAccessTokenExpired(String token) {
    final payload = parseJwtToken(token);
    if (payload == null) return true;

    final exp = payload['exp'] as int?;
    if (exp == null) return true;

    return DateTime.now().millisecondsSinceEpoch > exp * 1000;
  }

  @override
  bool isRefreshTokenExpired(String token) {
    final payload = parseJwtToken(token);
    if (payload == null) return true;

    final exp = payload['exp'] as int?;
    if (exp == null) return true;

    return DateTime.now().millisecondsSinceEpoch > exp * 1000;
  }

  @override
  Map<String, dynamic>? parseJwtToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final decoded = base64Url.decode(base64Url.normalize(payload));
      final payloadMap = jsonDecode(utf8.decode(decoded)) as Map<String, dynamic>;

      // Verify signature using HMAC
      final expectedSignature = _generateSignature(parts[0], parts[1]);
      if (!CryptoUtils.constantTimeEquals(parts[2], expectedSignature)) {
        return null; // Invalid signature
      }

      return payloadMap;
    } catch (e) {
      return null;
    }
  }

  @override
  DateTime? getTokenExpirationDate(String token) {
    final payload = parseJwtToken(token);
    if (payload == null) return null;
    
    final exp = payload['exp'] as int?;
    if (exp == null) return null;
    
    return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
  }
  
  /// Generate HMAC signature for JWT
  String _generateSignature(String header, String payload) {
    final message = '$header.$payload';
    final signature = CryptoUtils.hmacSha256(message, _jwtSecret);
    return base64Url.encode(utf8.encode(signature));
  }

  /// Generate a secure JWT token
  String generateToken(String userId, {Duration? expiresIn, String type = 'access'}) {
    final header = base64Url.encode(utf8.encode(jsonEncode({
      'alg': 'HS256',
      'typ': 'JWT',
    })));

    final now = DateTime.now();
    final expiration = now.add(expiresIn ?? _accessTokenExpiry);
    final payload = base64Url.encode(utf8.encode(jsonEncode({
      'sub': userId,
      'iat': now.millisecondsSinceEpoch ~/ 1000,
      'exp': expiration.millisecondsSinceEpoch ~/ 1000,
      'type': type,
    })));

    final signature = _generateSignature(header, payload);

    return '$header.$payload.$signature';
  }

  /// Generate access token
  String generateAccessToken(String userId) {
    return generateToken(userId, expiresIn: _accessTokenExpiry, type: 'access');
  }

  /// Generate refresh token
  String generateRefreshToken(String userId) {
    return generateToken(userId, expiresIn: _refreshTokenExpiry, type: 'refresh');
  }

  /// Get user ID from token
  String? getUserIdFromToken(String token) {
    final payload = parseJwtToken(token);
    return payload?['sub'] as String?;
  }
}

/// Server-side authentication service implementation
class ServerAuthService implements AuthService {
  final InMemoryUserRepository _userRepository;
  final TokenManager _tokenManager;
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  final StreamController<User?> _userController = StreamController<User?>.broadcast();
  
  AuthState _currentState = AuthState.initial;
  User? _currentUser;

  ServerAuthService(this._userRepository, this._tokenManager);

  @override
  Stream<AuthState> get authStateStream => _authStateController.stream;

  @override
  Stream<User?> get userStream => _userController.stream;

  @override
  AuthState get currentState => _currentState;

  @override
  User? get currentUser => _currentUser;

  @override
  bool get isAuthenticated => _currentState == AuthState.authenticated && _currentUser != null;

  @override
  Future<void> initialize() async {
    _currentState = AuthState.initial;
    _authStateController.add(_currentState);
  }

  @override
  Future<AuthResult> register({
    required String email,
    required String password,
    required String username,
    String? firstName,
    String? lastName,
    String? displayName,
  }) async {
    try {
      _currentState = AuthState.loading;
      _authStateController.add(_currentState);

      // Check if user already exists
      try {
        final existingUser = await _userRepository.getByEmail(email);
        if (existingUser != null) {
          _currentState = AuthState.error;
          _authStateController.add(_currentState);
          return AuthResult.failure(message: 'User with this email already exists');
        }
      } catch (e) {
        // User doesn't exist, continue with registration
      }

      try {
        final existingUser = await _userRepository.getByUsername(username);
        if (existingUser != null) {
          _currentState = AuthState.error;
          _authStateController.add(_currentState);
          return AuthResult.failure(message: 'Username already taken');
        }
      } catch (e) {
        // Username doesn't exist, continue with registration
      }

      // Create new user
      final user = User(
        id: IdGenerator.generateUserId(),
        username: username,
        email: email,
        firstName: firstName ?? '',
        lastName: lastName ?? '',
        displayName: displayName ?? '$firstName $lastName'.trim(),
        createdAt: DateTime.now(),
        isActive: true,
        role: UserRole.user,
        xp: 0,
        level: 1,
      );

      await _userRepository.create(user);

      // Generate tokens
      final tokenManager = _tokenManager as EnhancedTokenManager;
      final accessToken = tokenManager.generateAccessToken(user.id);
      final refreshToken = tokenManager.generateRefreshToken(user.id);

      await _tokenManager.saveTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
      );

      _currentUser = user;
      _currentState = AuthState.authenticated;
      _authStateController.add(_currentState);
      _userController.add(_currentUser);

      return AuthResult.success(
        message: 'Registration successful',
        user: user,
        token: accessToken,
        refreshToken: refreshToken,
      );
    } catch (e) {
      _currentState = AuthState.error;
      _authStateController.add(_currentState);
      return AuthResult.failure(message: 'Registration failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _currentState = AuthState.loading;
      _authStateController.add(_currentState);

      // Find user by email
      final user = await _userRepository.getByEmail(email);
      if (user == null) {
        _currentState = AuthState.unauthenticated;
        _authStateController.add(_currentState);
        return AuthResult.failure(message: 'Invalid email or password');
      }

      // TODO: Verify password (in real implementation)
      // For now, accept any password for demo purposes

      // Update last login
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      await _userRepository.update(user.id, updatedUser);

      // Generate tokens
      final tokenManager = _tokenManager as EnhancedTokenManager;
      final accessToken = tokenManager.generateAccessToken(user.id);
      final refreshToken = tokenManager.generateRefreshToken(user.id);

      await _tokenManager.saveTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
      );

      _currentUser = updatedUser;
      _currentState = AuthState.authenticated;
      _authStateController.add(_currentState);
      _userController.add(_currentUser);

      return AuthResult.success(
        message: 'Login successful',
        user: updatedUser,
        token: accessToken,
        refreshToken: refreshToken,
      );
    } catch (e) {
      _currentState = AuthState.error;
      _authStateController.add(_currentState);
      return AuthResult.failure(message: 'Login failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> loginWithUsername({
    required String username,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _currentState = AuthState.loading;
      _authStateController.add(_currentState);

      // Find user by username
      final user = await _userRepository.getByUsername(username);
      if (user == null) {
        _currentState = AuthState.unauthenticated;
        _authStateController.add(_currentState);
        return AuthResult.failure(message: 'Invalid username or password');
      }

      // TODO: Verify password (in real implementation)
      // For now, accept any password for demo purposes

      // Update last login
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      await _userRepository.update(user.id, updatedUser);

      // Generate tokens
      final tokenManager = _tokenManager as EnhancedTokenManager;
      final accessToken = tokenManager.generateAccessToken(user.id);
      final refreshToken = tokenManager.generateRefreshToken(user.id);

      await _tokenManager.saveTokens(
        accessToken: accessToken,
        refreshToken: refreshToken,
      );

      _currentUser = updatedUser;
      _currentState = AuthState.authenticated;
      _authStateController.add(_currentState);
      _userController.add(_currentUser);

      return AuthResult.success(
        message: 'Login successful',
        user: updatedUser,
        token: accessToken,
        refreshToken: refreshToken,
      );
    } catch (e) {
      _currentState = AuthState.error;
      _authStateController.add(_currentState);
      return AuthResult.failure(message: 'Login failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    await _tokenManager.clearTokens();
    _currentUser = null;
    _currentState = AuthState.unauthenticated;
    _authStateController.add(_currentState);
    _userController.add(null);
  }

  @override
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await _tokenManager.getRefreshToken();
      if (refreshToken == null || _tokenManager.isRefreshTokenExpired(refreshToken)) {
        await logout();
        return false;
      }

      final payload = _tokenManager.parseJwtToken(refreshToken);
      if (payload == null) {
        await logout();
        return false;
      }

      final userId = payload['sub'] as String?;
      if (userId == null) {
        await logout();
        return false;
      }

      final user = await _userRepository.getById(userId);
      if (user == null) {
        await logout();
        return false;
      }

      // Generate new tokens
      final tokenManager = _tokenManager as EnhancedTokenManager;
      final newAccessToken = tokenManager.generateAccessToken(user.id);
      final newRefreshToken = tokenManager.generateRefreshToken(user.id);

      await _tokenManager.saveTokens(
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      );

      _currentUser = user;
      _currentState = AuthState.authenticated;
      _authStateController.add(_currentState);
      _userController.add(_currentUser);

      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }

  @override
  Future<bool> verifyAuth() async {
    try {
      final accessToken = await _tokenManager.getAccessToken();
      if (accessToken == null) return false;

      if (_tokenManager.isAccessTokenExpired(accessToken)) {
        return await refreshToken();
      }

      final payload = _tokenManager.parseJwtToken(accessToken);
      if (payload == null) return false;

      final userId = payload['sub'] as String?;
      if (userId == null) return false;

      final user = await _userRepository.getById(userId);
      if (user == null) return false;

      _currentUser = user;
      _currentState = AuthState.authenticated;
      _authStateController.add(_currentState);
      _userController.add(_currentUser);

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    return _currentUser;
  }

  @override
  Future<AuthResult> updateProfile(Map<String, dynamic> updates) async {
    if (_currentUser == null) {
      return AuthResult.failure(message: 'User not authenticated');
    }

    try {
      final updatedUser = await _userRepository.updateProfile(_currentUser!.id, updates);
      _currentUser = updatedUser;
      _userController.add(_currentUser);

      return AuthResult.success(
        message: 'Profile updated successfully',
        user: updatedUser,
      );
    } catch (e) {
      return AuthResult.failure(message: 'Failed to update profile: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    // TODO: Implement password change logic
    return AuthResult.failure(message: 'Password change not implemented');
  }

  @override
  Future<AuthResult> requestPasswordReset(String email) async {
    // TODO: Implement password reset request
    return AuthResult.failure(message: 'Password reset not implemented');
  }

  @override
  Future<AuthResult> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    // TODO: Implement password reset
    return AuthResult.failure(message: 'Password reset not implemented');
  }

  @override
  Future<AuthResult> verifyEmail(String token) async {
    // TODO: Implement email verification
    return AuthResult.failure(message: 'Email verification not implemented');
  }

  @override
  Future<AuthResult> resendEmailVerification() async {
    // TODO: Implement resend email verification
    return AuthResult.failure(message: 'Email verification not implemented');
  }

  @override
  Future<AuthResult> deleteAccount(String password) async {
    if (_currentUser == null) {
      return AuthResult.failure(message: 'User not authenticated');
    }

    try {
      await _userRepository.delete(_currentUser!.id);
      await logout();
      return AuthResult.success(message: 'Account deleted successfully');
    } catch (e) {
      return AuthResult.failure(message: 'Failed to delete account: ${e.toString()}');
    }
  }

  @override
  Future<void> clearAuthData() async {
    await _tokenManager.clearTokens();
    _currentUser = null;
    _currentState = AuthState.initial;
    _authStateController.add(_currentState);
    _userController.add(null);
  }

  @override
  void dispose() {
    _authStateController.close();
    _userController.close();
  }
}
