<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quester API Server</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        .endpoints {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }
        code {
            background: #e9ecef;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Quester API Server</h1>
        
        <div class="status">
            ✅ Server is running successfully!
        </div>
        
        <h2>Available Endpoints</h2>
        <div class="endpoints">
            <ul>
                <li><code>GET /health</code> - Health check endpoint</li>
                <li><code>GET /version</code> - API version information</li>
                <li><code>POST /api/auth/register</code> - User registration</li>
                <li><code>POST /api/auth/login</code> - User login</li>
                <li><code>POST /api/auth/refresh</code> - Refresh JWT token</li>
                <li><code>GET /api/quests</code> - Get user quests</li>
                <li><code>POST /api/quests</code> - Create new quest</li>
                <li><code>GET /ws/notifications</code> - WebSocket notifications</li>
                <li><code>GET /ws/updates</code> - WebSocket real-time updates</li>
            </ul>
        </div>
        
        <h2>Documentation</h2>
        <p>For complete API documentation, please refer to the API_REFERENCE.md file in the server directory.</p>
        
        <h2>Client Application</h2>
        <p>The Flutter client application is available at <a href="http://localhost:8000">http://localhost:8000</a> (development) or through the Nginx proxy.</p>
        
        <footer style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #eee; color: #666; text-align: center;">
            <p>Quester - Quest Management System</p>
        </footer>
    </div>
</body>
</html>
