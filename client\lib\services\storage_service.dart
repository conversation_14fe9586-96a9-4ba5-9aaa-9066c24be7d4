import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shared/shared.dart';

/// Service for handling local storage operations
class StorageService {
  final SharedPreferences _sharedPreferences;

  StorageService({
    required SharedPreferences sharedPreferences,
  }) : _sharedPreferences = sharedPreferences;

  // ===== Shared Preferences Methods =====

  /// Save a string value
  Future<bool> setString(String key, String value) async {
    return await _sharedPreferences.setString(key, value);
  }

  /// Get a string value
  String? getString(String key) {
    return _sharedPreferences.getString(key);
  }

  /// Save an integer value
  Future<bool> setInt(String key, int value) async {
    return await _sharedPreferences.setInt(key, value);
  }

  /// Get an integer value
  int? getInt(String key) {
    return _sharedPreferences.getInt(key);
  }

  /// Save a boolean value
  Future<bool> setBool(String key, bool value) async {
    return await _sharedPreferences.setBool(key, value);
  }

  /// Get a boolean value
  bool? getBool(String key) {
    return _sharedPreferences.getBool(key);
  }

  /// Save a double value
  Future<bool> setDouble(String key, double value) async {
    return await _sharedPreferences.setDouble(key, value);
  }

  /// Get a double value
  double? getDouble(String key) {
    return _sharedPreferences.getDouble(key);
  }

  /// Save a list of strings
  Future<bool> setStringList(String key, List<String> value) async {
    return await _sharedPreferences.setStringList(key, value);
  }

  /// Get a list of strings
  List<String>? getStringList(String key) {
    return _sharedPreferences.getStringList(key);
  }

  /// Save a JSON object
  Future<bool> setJson(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await _sharedPreferences.setString(key, jsonString);
  }

  /// Get a JSON object
  Map<String, dynamic>? getJson(String key) {
    final jsonString = _sharedPreferences.getString(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      debugPrint('Error decoding JSON for key $key: $e');
      return null;
    }
  }

  /// Remove a key
  Future<bool> remove(String key) async {
    return await _sharedPreferences.remove(key);
  }

  /// Check if a key exists
  bool containsKey(String key) {
    return _sharedPreferences.containsKey(key);
  }

  /// Get all keys
  Set<String> getKeys() {
    return _sharedPreferences.getKeys();
  }

  /// Clear all data
  Future<bool> clear() async {
    return await _sharedPreferences.clear();
  }

  // ===== Additional Storage Methods =====

  // ===== Convenience Methods =====

  /// Save user authentication token
  Future<void> saveAuthToken(String token) async {
    await setString(AppConstants.authTokenKey, token);
  }

  /// Get user authentication token
  Future<String?> getAuthToken() async {
    return getString(AppConstants.authTokenKey);
  }

  /// Remove user authentication token
  Future<void> removeAuthToken() async {
    await remove(AppConstants.authTokenKey);
  }

  /// Save refresh token
  Future<void> saveRefreshToken(String token) async {
    await setString(AppConstants.refreshTokenKey, token);
  }

  /// Get refresh token
  Future<String?> getRefreshToken() async {
    return getString(AppConstants.refreshTokenKey);
  }

  /// Remove refresh token
  Future<void> removeRefreshToken() async {
    await remove(AppConstants.refreshTokenKey);
  }

  /// Save user data
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    await setJson(AppConstants.userDataKey, userData);
  }

  /// Get user data
  Future<Map<String, dynamic>?> getUserData() async {
    return getJson(AppConstants.userDataKey);
  }

  /// Remove user data
  Future<void> removeUserData() async {
    await remove(AppConstants.userDataKey);
  }

  /// Save theme mode
  Future<bool> saveThemeMode(String themeMode) async {
    return await setString(AppConstants.themeKey, themeMode);
  }

  /// Get theme mode
  String? getThemeMode() {
    return getString(AppConstants.themeKey);
  }

  /// Save language preference
  Future<bool> saveLanguage(String language) async {
    return await setString(AppConstants.languageKey, language);
  }

  /// Get language preference
  String? getLanguage() {
    return getString(AppConstants.languageKey);
  }

  /// Save notification settings
  Future<bool> saveNotificationSettings(Map<String, dynamic> settings) async {
    return await setJson(AppConstants.notificationSettingsKey, settings);
  }

  /// Get notification settings
  Map<String, dynamic>? getNotificationSettings() {
    return getJson(AppConstants.notificationSettingsKey);
  }

  /// Save app settings
  Future<bool> saveAppSettings(Map<String, dynamic> settings) async {
    return await setJson(AppConstants.settingsKey, settings);
  }

  /// Get app settings
  Map<String, dynamic>? getAppSettings() {
    return getJson(AppConstants.settingsKey);
  }

  /// Save cached data with expiration
  Future<bool> setCachedData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiration,
  }) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiration': expiration?.inMilliseconds,
    };
    return await setJson('cache_$key', cacheData);
  }

  /// Get cached data if not expired
  Map<String, dynamic>? getCachedData(String key) {
    final cacheData = getJson('cache_$key');
    if (cacheData == null) return null;

    final timestamp = cacheData['timestamp'] as int?;
    final expiration = cacheData['expiration'] as int?;
    
    if (timestamp != null && expiration != null) {
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > expiration) {
        // Cache expired, remove it
        remove('cache_$key');
        return null;
      }
    }

    return cacheData['data'] as Map<String, dynamic>?;
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    final keys = getKeys();
    final cacheKeys = keys.where((key) => key.startsWith('cache_'));
    
    for (final key in cacheKeys) {
      await remove(key);
    }
  }

  /// Clear all user-related data (logout)
  Future<void> clearUserData() async {
    await removeAuthToken();
    await removeRefreshToken();
    await removeUserData();
    await clearCache();
  }

  /// Get storage statistics
  Map<String, dynamic> getStorageStats() {
    final keys = getKeys();

    return {
      'total_keys': keys.length,
      'cache_keys': keys.where((key) => key.startsWith('cache_')).length,
      'user_data_exists': containsKey(AppConstants.userDataKey),
      'auth_token_exists': getString(AppConstants.authTokenKey) != null,
    };
  }
}
