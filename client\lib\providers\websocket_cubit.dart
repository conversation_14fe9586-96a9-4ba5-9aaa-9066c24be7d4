import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared/shared.dart' hide ApiService, WebSocketService;

import '../services/websocket_service.dart';
import 'notification_cubit.dart';

/// WebSocket connection states
abstract class WebSocketState {}

class WebSocketDisconnected extends WebSocketState {}

class WebSocketConnecting extends WebSocketState {}

class WebSocketConnected extends WebSocketState {}

class WebSocketError extends WebSocketState {
  final String message;

  WebSocketError({required this.message});
}

/// WebSocket management cubit
class WebSocketCubit extends Cubit<WebSocketState> {
  final WebSocketService _webSocketService;
  final NotificationCubit? _notificationCubit;

  WebSocketCubit({
    WebSocketService? webSocketService,
    NotificationCubit? notificationCubit,
  })  : _webSocketService = webSocketService ?? WebSocketService(),
        _notificationCubit = notificationCubit,
        super(WebSocketDisconnected()) {
    _initializeWebSocket();
  }
  StreamSubscription? _connectionSubscription;
  StreamSubscription? _notificationSubscription;

  /// Initialize WebSocket listeners
  void _initializeWebSocket() {
    // Listen to connection state changes
    _connectionSubscription = _webSocketService.connectionStream.listen(
      (isConnected) {
        if (isConnected) {
          emit(WebSocketConnected());
        } else if (_webSocketService.isConnecting) {
          emit(WebSocketConnecting());
        } else {
          emit(WebSocketDisconnected());
        }
      },
    );

    // Listen to notifications
    _notificationSubscription = _webSocketService.notificationStream.listen(
      (notificationData) {
        _handleNotification(notificationData);
      },
    );
  }

  /// Connect to WebSocket server
  Future<void> connect(String authToken) async {
    try {
      emit(WebSocketConnecting());
      await _webSocketService.connect(authToken);
    } catch (error) {
      emit(WebSocketError(message: error.toString()));
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    try {
      await _webSocketService.disconnect();
      emit(WebSocketDisconnected());
    } catch (error) {
      emit(WebSocketError(message: error.toString()));
    }
  }

  /// Subscribe to a channel
  void subscribe(String channel) {
    _webSocketService.subscribe(channel);
  }

  /// Unsubscribe from a channel
  void unsubscribe(String channel) {
    _webSocketService.unsubscribe(channel);
  }

  /// Send message through WebSocket
  void sendMessage(Map<String, dynamic> message) {
    _webSocketService.sendMessage(message);
  }

  /// Handle incoming notifications
  void _handleNotification(Map<String, dynamic> notificationData) {
    try {
      // Handle notification data
      final title = notificationData['title'] as String? ?? 'New Notification';
      final message = notificationData['message'] as String? ?? '';
      final type = notificationData['type'] as String? ?? 'general';

      // You can emit events or call other services here
      // For example, update the notification cubit
      debugPrint('📬 Received real-time notification: [$type] $title - $message');

      // Integrate with NotificationCubit to add the notification
      if (_notificationCubit != null) {
        final notification = Notification(
          id: 'ws_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'current_user', // Would get from auth state
          title: title,
          message: message,
          type: _mapStringToNotificationType(type),
          createdAt: DateTime.now(),
        );
        _notificationCubit.addNotification(notification);
      }

    } catch (error) {
      debugPrint('❌ Failed to handle notification: $error');
    }
  }

  /// Check if WebSocket is connected
  bool get isConnected => _webSocketService.isConnected;

  /// Check if WebSocket is connecting
  bool get isConnecting => _webSocketService.isConnecting;

  /// Get WebSocket message stream
  Stream<Map<String, dynamic>> get messageStream => _webSocketService.messageStream;

  /// Get WebSocket notification stream
  Stream<Map<String, dynamic>> get notificationStream => _webSocketService.notificationStream;

  /// Map string type to NotificationType enum
  NotificationType _mapStringToNotificationType(String type) {
    switch (type.toLowerCase()) {
      case 'info':
        return NotificationType.info;
      case 'success':
        return NotificationType.success;
      case 'warning':
        return NotificationType.warning;
      case 'error':
        return NotificationType.error;
      case 'achievement':
      case 'achievement_unlocked':
        return NotificationType.achievementUnlocked;
      case 'quest':
      case 'quest_complete':
        return NotificationType.questComplete;
      case 'quest_assigned':
        return NotificationType.questAssigned;
      case 'level_up':
        return NotificationType.levelUp;
      case 'friend_request':
        return NotificationType.friendRequest;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.info;
    }
  }

  @override
  Future<void> close() {
    _connectionSubscription?.cancel();
    _notificationSubscription?.cancel();
    _webSocketService.dispose();
    return super.close();
  }
}
