/// Quester shared package providing common models, utilities, and constants
/// for the Flutter client and Dart server applications.
///
/// This package includes:
/// - Data models with JSON serialization
/// - API response framework
/// - Validation utilities
/// - Constants and configuration
/// - Service interfaces
/// - Exception handling
library;

// Export all public APIs
export 'src/models/models.dart';
export 'src/services/services.dart';
export 'src/utils/utils.dart';
export 'src/constants/constants.dart';
export 'src/exceptions/exceptions.dart';
