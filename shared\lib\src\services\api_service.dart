import 'dart:async';
import '../models/models.dart';

/// Abstract API service interface for HTTP operations
abstract class ApiService {
  /// Authentication endpoints
  Future<ApiResponse<AuthResponse>> register(RegisterRequest request);
  Future<ApiResponse<AuthResponse>> login(LoginRequest request);
  Future<ApiResponse<void>> logout();
  Future<ApiResponse<AuthResponse>> refreshToken(String refreshToken);
  
  /// User management endpoints
  Future<ApiResponse<User>> getCurrentUser();
  Future<ApiResponse<User>> getUserById(String id);
  Future<ApiResponse<List<User>>> getUsers({int page = 1, int pageSize = 20});
  Future<ApiResponse<User>> updateUser(String id, Map<String, dynamic> updates);
  Future<ApiResponse<void>> deleteUser(String id);
  
  /// Quest management endpoints
  Future<ApiResponse<List<Quest>>> getQuests({
    int page = 1,
    int pageSize = 20,
    QuestStatus? status,
    QuestDifficulty? difficulty,
    QuestCategory? category,
  });
  Future<ApiResponse<Quest>> getQuestById(String id);
  Future<ApiResponse<Quest>> createQuest(Quest quest);
  Future<ApiResponse<Quest>> updateQuest(String id, Map<String, dynamic> updates);
  Future<ApiResponse<void>> deleteQuest(String id);
  Future<ApiResponse<Quest>> completeQuest(QuestCompletionRequest request);
  
  /// User quest assignments
  Future<ApiResponse<List<Quest>>> getUserQuests(String userId, {QuestStatus? status});
  Future<ApiResponse<Quest>> assignQuestToUser(String questId, String userId);
  Future<ApiResponse<void>> unassignQuestFromUser(String questId, String userId);
  
  /// Notification endpoints
  Future<ApiResponse<List<Notification>>> getNotifications({
    int page = 1,
    int pageSize = 20,
    bool? isRead,
  });
  Future<ApiResponse<Notification>> getNotificationById(String id);
  Future<ApiResponse<Notification>> markNotificationAsRead(String id);
  Future<ApiResponse<void>> markAllNotificationsAsRead();
  Future<ApiResponse<void>> deleteNotification(String id);
  
  /// Dashboard and analytics endpoints
  Future<ApiResponse<DashboardStats>> getDashboardStats();
  Future<ApiResponse<List<UserActivity>>> getRecentActivity({
    int page = 1,
    int pageSize = 20,
  });
  Future<ApiResponse<List<QuestStats>>> getQuestStats();
  Future<ApiResponse<List<UserPerformance>>> getUserPerformance({
    int page = 1,
    int pageSize = 20,
  });
  Future<ApiResponse<SystemHealth>> getSystemHealth();
  
  /// File upload endpoints
  Future<ApiResponse<String>> uploadAvatar(String userId, List<int> fileBytes, String fileName);
  Future<ApiResponse<String>> uploadQuestImage(String questId, List<int> fileBytes, String fileName);
  
  /// Search endpoints
  Future<ApiResponse<List<User>>> searchUsers(String query, {int page = 1, int pageSize = 20});
  Future<ApiResponse<List<Quest>>> searchQuests(String query, {int page = 1, int pageSize = 20});
  
  /// Configuration and settings
  void setBaseUrl(String baseUrl);
  void setAuthToken(String token);
  void clearAuthToken();
  void setTimeout(Duration timeout);
  
  /// Request interceptors
  void addRequestInterceptor(RequestInterceptor interceptor);
  void removeRequestInterceptor(RequestInterceptor interceptor);
  
  /// Response interceptors
  void addResponseInterceptor(ResponseInterceptor interceptor);
  void removeResponseInterceptor(ResponseInterceptor interceptor);
}

/// Request interceptor interface
abstract class RequestInterceptor {
  Future<Map<String, String>> onRequest(
    String method,
    String url,
    Map<String, String> headers,
    dynamic body,
  );
}

/// Response interceptor interface
abstract class ResponseInterceptor {
  Future<void> onResponse(
    String method,
    String url,
    int statusCode,
    Map<String, String> headers,
    dynamic body,
  );
}

/// HTTP methods enumeration
enum HttpMethod {
  get,
  post,
  put,
  patch,
  delete,
}

/// API request configuration
class ApiRequestConfig {
  final String method;
  final String url;
  final Map<String, String> headers;
  final dynamic body;
  final Duration timeout;
  final bool requiresAuth;

  const ApiRequestConfig({
    required this.method,
    required this.url,
    this.headers = const {},
    this.body,
    this.timeout = const Duration(seconds: 30),
    this.requiresAuth = true,
  });
}

/// API response wrapper
class ApiResponseWrapper<T> {
  final int statusCode;
  final Map<String, String> headers;
  final T data;
  final Duration responseTime;

  const ApiResponseWrapper({
    required this.statusCode,
    required this.headers,
    required this.data,
    required this.responseTime,
  });
}

/// API error types
enum ApiErrorType {
  network,
  timeout,
  authentication,
  authorization,
  validation,
  server,
  unknown,
}

// Note: ApiException is defined in ../exceptions/api_exception.dart

/// Pagination parameters
class PaginationParams {
  final int page;
  final int pageSize;
  final String? sortBy;
  final String? sortOrder;

  const PaginationParams({
    this.page = 1,
    this.pageSize = 20,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, String> toQueryParams() {
    return {
      'page': page.toString(),
      'pageSize': pageSize.toString(),
      if (sortBy != null) 'sortBy': sortBy!,
      if (sortOrder != null) 'sortOrder': sortOrder!,
    };
  }
}

/// Filter parameters for quest queries
class QuestFilterParams {
  final QuestStatus? status;
  final QuestDifficulty? difficulty;
  final QuestCategory? category;
  final String? assignedUserId;
  final DateTime? createdAfter;
  final DateTime? createdBefore;

  const QuestFilterParams({
    this.status,
    this.difficulty,
    this.category,
    this.assignedUserId,
    this.createdAfter,
    this.createdBefore,
  });

  Map<String, String> toQueryParams() {
    final params = <String, String>{};
    
    if (status != null) params['status'] = status!.name;
    if (difficulty != null) params['difficulty'] = difficulty!.name;
    if (category != null) params['category'] = category!.name;
    if (assignedUserId != null) params['assignedUserId'] = assignedUserId!;
    if (createdAfter != null) params['createdAfter'] = createdAfter!.toIso8601String();
    if (createdBefore != null) params['createdBefore'] = createdBefore!.toIso8601String();
    
    return params;
  }
}
