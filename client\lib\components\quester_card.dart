import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Reusable card component with consistent styling and responsive design
class QuesterCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? backgroundColor;
  final Color? shadowColor;
  final BorderRadius? borderRadius;
  final Border? border;
  final VoidCallback? onTap;
  final bool isClickable;
  final double? width;
  final double? height;
  final Gradient? gradient;

  const QuesterCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.shadowColor,
    this.borderRadius,
    this.border,
    this.onTap,
    this.isClickable = false,
    this.width,
    this.height,
    this.gradient,
  });

  /// Standard card factory
  factory QuesterCard.standard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(16.w),
      margin: margin ?? EdgeInsets.all(8.w),
      elevation: 2,
      borderRadius: BorderRadius.circular(12.r),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Elevated card factory
  factory QuesterCard.elevated({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(20.w),
      margin: margin ?? EdgeInsets.all(12.w),
      elevation: 8,
      borderRadius: BorderRadius.circular(16.r),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Flat card factory
  factory QuesterCard.flat({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    Color? borderColor,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(16.w),
      margin: margin ?? EdgeInsets.all(8.w),
      elevation: 0,
      borderRadius: BorderRadius.circular(12.r),
      border: Border.all(
        color: borderColor ?? Colors.grey.shade300,
        width: 1,
      ),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Gradient card factory
  factory QuesterCard.gradient({
    required Widget child,
    required Gradient gradient,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(16.w),
      margin: margin ?? EdgeInsets.all(8.w),
      elevation: 4,
      borderRadius: BorderRadius.circular(12.r),
      gradient: gradient,
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Quest card factory
  factory QuesterCard.quest({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(16.w),
      margin: margin ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      elevation: 3,
      borderRadius: BorderRadius.circular(16.r),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Achievement card factory
  factory QuesterCard.achievement({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(20.w),
      margin: margin ?? EdgeInsets.all(12.w),
      elevation: 6,
      borderRadius: BorderRadius.circular(20.r),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.amber.shade100,
          Colors.orange.shade100,
        ],
      ),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  /// Dashboard card factory
  factory QuesterCard.dashboard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return QuesterCard(
      padding: padding ?? EdgeInsets.all(24.w),
      margin: margin ?? EdgeInsets.all(8.w),
      elevation: 2,
      borderRadius: BorderRadius.circular(16.r),
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget cardContent = Container(
      width: width,
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        color: gradient == null ? (backgroundColor ?? theme.cardColor) : null,
        gradient: gradient,
        borderRadius: borderRadius ?? BorderRadius.circular(12.r),
        border: border,
        boxShadow: elevation != null && elevation! > 0
            ? [
                BoxShadow(
                  color: shadowColor ?? Colors.black.withValues(alpha: 0.1),
                  blurRadius: elevation! * 2,
                  offset: Offset(0, elevation! / 2),
                  spreadRadius: 0,
                ),
              ]
            : null,
      ),
      child: child,
    );

    if (isClickable || onTap != null) {
      cardContent = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(12.r),
          splashColor: theme.colorScheme.primary.withValues(alpha: 0.1),
          highlightColor: theme.colorScheme.primary.withValues(alpha: 0.05),
          child: cardContent,
        ),
      );
    }

    if (margin != null) {
      return Container(
        margin: margin,
        child: cardContent,
      );
    }

    return cardContent;
  }
}

/// Card header component for consistent card headers
class QuesterCardHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final EdgeInsetsGeometry? padding;

  const QuesterCardHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.titleStyle,
    this.subtitleStyle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            SizedBox(width: 12.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: titleStyle ?? theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 16.sp,
                  ),
                ),
                if (subtitle != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    subtitle!,
                    style: subtitleStyle ?? theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            SizedBox(width: 12.w),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// Card footer component for consistent card footers
class QuesterCardFooter extends StatelessWidget {
  final List<Widget> actions;
  final MainAxisAlignment alignment;
  final EdgeInsetsGeometry? padding;

  const QuesterCardFooter({
    super.key,
    required this.actions,
    this.alignment = MainAxisAlignment.end,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.only(top: 12.h),
      child: Row(
        mainAxisAlignment: alignment,
        children: actions
            .expand((action) => [action, SizedBox(width: 8.w)])
            .take(actions.length * 2 - 1)
            .toList(),
      ),
    );
  }
}
