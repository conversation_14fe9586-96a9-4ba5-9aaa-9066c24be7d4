# Root .dockerignore for Quester project
# This file optimizes Docker build contexts by excluding unnecessary files

# Version control
.git
.gitignore
.gitattributes

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Documentation (keep essential README files)
*.md
!README.md
!*/README.md
CHANGELOG.md
docs/
documentation/

# Build artifacts (but keep pubspec.lock for reproducible builds)
build/
.dart_tool/
.packages
.pub-cache/
dist/
out/

# Flutter specific files
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files when building
Dockerfile*
docker-compose*.yml
.dockerignore

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Coverage and test reports
coverage/
*.lcov

# Logs
*.log
logs/

# Temporary files
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Platform specific folders (will be selectively included in specific builds)
android/
ios/
linux/
macos/
windows/
web/

# Flutter ephemeral files and symlinks that cause Docker issues on Windows
**/flutter/ephemeral/
**/.plugin_symlinks/
**/Generated.xcconfig
**/flutter_export_environment.sh

# Settings and data directories that shouldn't be in containers
settings/data/
settings/scripts/mongo-init.js

# Deployment scripts
deploy.sh
deploy.bat
