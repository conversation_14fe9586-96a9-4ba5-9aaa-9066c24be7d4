import '../models/models.dart';

/// Validation service for input validation and business rules
class ValidationService {
  /// Email validation using RFC 5322 compliant regex
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    final regex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return regex.hasMatch(email);
  }

  /// Username validation
  static bool isValidUsername(String username) {
    if (username.isEmpty) return false;
    if (username.length < 3 || username.length > 20) return false;
    
    // Allow letters, numbers, underscores, and hyphens
    final regex = RegExp(r'^[a-zA-Z0-9_-]+$');
    return regex.hasMatch(username);
  }

  /// Password strength validation
  static bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    if (password.length > 128) return false;
    
    // Check for at least one uppercase letter
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    
    // Check for at least one lowercase letter
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    
    // Check for at least one number
    final hasNumbers = password.contains(RegExp(r'[0-9]'));
    
    // Check for at least one special character
    final hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasNumbers && hasSpecialChars;
  }

  /// Get password strength score (0-4)
  static int getPasswordStrength(String password) {
    int score = 0;
    
    if (password.length >= 8) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;
    
    return score.clamp(0, 4);
  }

  /// Get password strength description
  static String getPasswordStrengthDescription(String password) {
    final strength = getPasswordStrength(password);
    switch (strength) {
      case 0:
      case 1:
        return 'Very Weak';
      case 2:
        return 'Weak';
      case 3:
        return 'Medium';
      case 4:
        return 'Strong';
      default:
        return 'Unknown';
    }
  }

  /// Name validation (first name, last name)
  static bool isValidName(String name) {
    if (name.isEmpty) return false;
    if (name.length < 2 || name.length > 50) return false;
    
    // Allow letters, spaces, hyphens, and apostrophes
    final regex = RegExp(r"^[a-zA-Z\s\-']+$");
    return regex.hasMatch(name);
  }

  /// Quest validation
  static bool isValidQuest(Quest quest) {
    if (quest.title.trim().isEmpty) return false;
    if (quest.title.length < 5 || quest.title.length > 100) return false;
    
    if (quest.description.trim().isEmpty) return false;
    if (quest.description.length > 1000) return false;
    
    if (quest.xpReward <= 0) return false;
    if (quest.xpReward > 10000) return false;
    
    return true;
  }

  /// Validate quest title
  static bool isValidQuestTitle(String title) {
    if (title.trim().isEmpty) return false;
    if (title.length < 5 || title.length > 100) return false;
    return true;
  }

  /// Validate quest description
  static bool isValidQuestDescription(String description) {
    if (description.trim().isEmpty) return false;
    if (description.length > 1000) return false;
    return true;
  }

  /// Validate XP reward
  static bool isValidXpReward(int xp) {
    return xp > 0 && xp <= 10000;
  }

  /// URL validation
  static bool isValidUrl(String url) {
    if (url.isEmpty) return false;
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Phone number validation (basic)
  static bool isValidPhoneNumber(String phone) {
    if (phone.isEmpty) return false;
    
    // Remove all non-digit characters
    final digitsOnly = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it has 10-15 digits
    return digitsOnly.length >= 10 && digitsOnly.length <= 15;
  }

  /// Validate user registration request
  static List<String> validateRegistrationRequest(RegisterRequest request) {
    final errors = <String>[];
    
    if (!isValidUsername(request.username)) {
      errors.add('Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens');
    }
    
    if (!isValidEmail(request.email)) {
      errors.add('Please enter a valid email address');
    }
    
    if (!isStrongPassword(request.password)) {
      errors.add('Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters');
    }
    
    if (!isValidName(request.firstName)) {
      errors.add('First name must be 2-50 characters long and contain only letters, spaces, hyphens, and apostrophes');
    }
    
    if (!isValidName(request.lastName)) {
      errors.add('Last name must be 2-50 characters long and contain only letters, spaces, hyphens, and apostrophes');
    }
    
    return errors;
  }

  /// Validate login request
  static List<String> validateLoginRequest(LoginRequest request) {
    final errors = <String>[];
    
    if (!isValidEmail(request.email)) {
      errors.add('Please enter a valid email address');
    }
    
    if (request.password.isEmpty) {
      errors.add('Password is required');
    }
    
    return errors;
  }

  /// Validate quest creation
  static List<String> validateQuestCreation(Quest quest) {
    final errors = <String>[];
    
    if (!isValidQuestTitle(quest.title)) {
      errors.add('Quest title must be 5-100 characters long');
    }
    
    if (!isValidQuestDescription(quest.description)) {
      errors.add('Quest description must not be empty and not exceed 1000 characters');
    }
    
    if (!isValidXpReward(quest.xpReward)) {
      errors.add('XP reward must be between 1 and 10,000');
    }
    
    return errors;
  }

  /// Sanitize string input
  static String sanitizeString(String input) {
    return input.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Check if string contains only alphanumeric characters
  static bool isAlphanumeric(String input) {
    final regex = RegExp(r'^[a-zA-Z0-9]+$');
    return regex.hasMatch(input);
  }

  /// Check if string is a valid hex color
  static bool isValidHexColor(String color) {
    final regex = RegExp(r'^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$');
    return regex.hasMatch(color);
  }

  /// Validate achievement title
  static bool isValidAchievementTitle(String title) {
    if (title.trim().isEmpty) return false;
    if (title.length < 3 || title.length > 100) return false;
    return true;
  }

  /// Validate achievement description
  static bool isValidAchievementDescription(String description) {
    if (description.trim().isEmpty) return false;
    if (description.length > 500) return false;
    return true;
  }

  /// Validate XP reward amount
  static bool isValidXpAmount(int xp) {
    return xp >= 0 && xp <= 100000;
  }

  /// Validate coin reward amount
  static bool isValidCoinAmount(int coins) {
    return coins >= 0 && coins <= 1000000;
  }

  /// Validate WebSocket channel name
  static bool isValidChannelName(String channel) {
    if (channel.isEmpty) return false;
    if (channel.length > 50) return false;

    // Allow letters, numbers, underscores, hyphens, and dots
    final regex = RegExp(r'^[a-zA-Z0-9_.-]+$');
    return regex.hasMatch(channel);
  }

  /// Validate pagination parameters
  static bool isValidPaginationParams(int page, int pageSize) {
    return page >= 1 && pageSize >= 1 && pageSize <= 100;
  }

  /// Validate file size (in bytes)
  static bool isValidFileSize(int sizeInBytes, int maxSizeInBytes) {
    return sizeInBytes > 0 && sizeInBytes <= maxSizeInBytes;
  }

  /// Validate image file extension
  static bool isValidImageExtension(String fileName) {
    final validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    final extension = fileName.toLowerCase().split('.').last;
    return validExtensions.contains(extension);
  }
}
