import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'dashboard.g.dart';

/// Dashboard statistics model for analytics
@JsonSerializable()
class DashboardStats extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final int totalQuests;
  final int activeQuests;
  final int completedQuests;
  final int totalXpEarned;
  final double averageQuestCompletion;
  final DateTime lastUpdated;

  const DashboardStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.totalQuests,
    required this.activeQuests,
    required this.completedQuests,
    required this.totalXpEarned,
    required this.averageQuestCompletion,
    required this.lastUpdated,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) =>
      _$DashboardStatsFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardStatsToJson(this);

  @override
  List<Object?> get props => [
        totalUsers,
        activeUsers,
        totalQuests,
        activeQuests,
        completedQuests,
        totalXpEarned,
        averageQuestCompletion,
        lastUpdated,
      ];
}

/// User activity tracking model
@JsonSerializable()
class UserActivity extends Equatable {
  final String id;
  final String userId;
  final String username;
  final String action;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? details;
  final String? questId;
  final int? xpGained;

  const UserActivity({
    required this.id,
    required this.userId,
    required this.username,
    required this.action,
    required this.description,
    required this.timestamp,
    this.details,
    this.questId,
    this.xpGained,
  });

  factory UserActivity.fromJson(Map<String, dynamic> json) =>
      _$UserActivityFromJson(json);

  Map<String, dynamic> toJson() => _$UserActivityToJson(this);

  /// Factory for quest completion activity
  factory UserActivity.questCompleted({
    required String id,
    required String userId,
    required String username,
    required String questTitle,
    required int xpGained,
    String? questId,
  }) {
    return UserActivity(
      id: id,
      userId: userId,
      username: username,
      action: 'quest_completed',
      description: 'Completed quest: $questTitle',
      timestamp: DateTime.now(),
      questId: questId,
      xpGained: xpGained,
      details: {
        'questTitle': questTitle,
        'xpGained': xpGained,
      },
    );
  }

  /// Factory for user registration activity
  factory UserActivity.userRegistered({
    required String id,
    required String userId,
    required String username,
  }) {
    return UserActivity(
      id: id,
      userId: userId,
      username: username,
      action: 'user_registered',
      description: 'New user registered',
      timestamp: DateTime.now(),
    );
  }

  /// Factory for level up activity
  factory UserActivity.levelUp({
    required String id,
    required String userId,
    required String username,
    required int newLevel,
  }) {
    return UserActivity(
      id: id,
      userId: userId,
      username: username,
      action: 'level_up',
      description: 'Reached level $newLevel',
      timestamp: DateTime.now(),
      details: {
        'newLevel': newLevel,
      },
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        username,
        action,
        description,
        timestamp,
        details,
        questId,
        xpGained,
      ];
}

/// Quest statistics model
@JsonSerializable()
class QuestStats extends Equatable {
  final String questId;
  final String title;
  final int totalAssignments;
  final int completions;
  final int failures;
  final double completionRate;
  final double averageCompletionTime;
  final DateTime lastCompleted;

  const QuestStats({
    required this.questId,
    required this.title,
    required this.totalAssignments,
    required this.completions,
    required this.failures,
    required this.completionRate,
    required this.averageCompletionTime,
    required this.lastCompleted,
  });

  factory QuestStats.fromJson(Map<String, dynamic> json) =>
      _$QuestStatsFromJson(json);

  Map<String, dynamic> toJson() => _$QuestStatsToJson(this);

  @override
  List<Object?> get props => [
        questId,
        title,
        totalAssignments,
        completions,
        failures,
        completionRate,
        averageCompletionTime,
        lastCompleted,
      ];
}

/// User performance metrics
@JsonSerializable()
class UserPerformance extends Equatable {
  final String userId;
  final String username;
  final int totalQuests;
  final int completedQuests;
  final int failedQuests;
  final double completionRate;
  final int totalXp;
  final int currentLevel;
  final int rank;
  final DateTime lastActive;

  const UserPerformance({
    required this.userId,
    required this.username,
    required this.totalQuests,
    required this.completedQuests,
    required this.failedQuests,
    required this.completionRate,
    required this.totalXp,
    required this.currentLevel,
    required this.rank,
    required this.lastActive,
  });

  factory UserPerformance.fromJson(Map<String, dynamic> json) =>
      _$UserPerformanceFromJson(json);

  Map<String, dynamic> toJson() => _$UserPerformanceToJson(this);

  @override
  List<Object?> get props => [
        userId,
        username,
        totalQuests,
        completedQuests,
        failedQuests,
        completionRate,
        totalXp,
        currentLevel,
        rank,
        lastActive,
      ];
}

/// System health metrics
@JsonSerializable()
class SystemHealth extends Equatable {
  final String status;
  final DateTime timestamp;
  final double cpuUsage;
  final double memoryUsage;
  final int activeConnections;
  final int totalRequests;
  final double averageResponseTime;
  final Map<String, dynamic> services;

  const SystemHealth({
    required this.status,
    required this.timestamp,
    required this.cpuUsage,
    required this.memoryUsage,
    required this.activeConnections,
    required this.totalRequests,
    required this.averageResponseTime,
    required this.services,
  });

  factory SystemHealth.fromJson(Map<String, dynamic> json) =>
      _$SystemHealthFromJson(json);

  Map<String, dynamic> toJson() => _$SystemHealthToJson(this);

  bool get isHealthy => status == 'healthy';

  @override
  List<Object?> get props => [
        status,
        timestamp,
        cpuUsage,
        memoryUsage,
        activeConnections,
        totalRequests,
        averageResponseTime,
        services,
      ];
}
