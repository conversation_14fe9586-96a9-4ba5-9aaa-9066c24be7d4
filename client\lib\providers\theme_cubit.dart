import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../core/theme/app_theme.dart';
import '../services/storage_service.dart';

/// Theme state class
class ThemeState extends Equatable {
  final ThemeMode themeMode;
  final ThemeData themeData;

  const ThemeState({
    required this.themeMode,
    required this.themeData,
  });

  factory ThemeState.light() {
    return ThemeState(
      themeMode: ThemeMode.light,
      themeData: AppTheme.lightTheme,
    );
  }

  factory ThemeState.dark() {
    return ThemeState(
      themeMode: ThemeMode.dark,
      themeData: AppTheme.darkTheme,
    );
  }

  factory ThemeState.system() {
    return ThemeState(
      themeMode: ThemeMode.system,
      themeData: AppTheme.lightTheme, // Default fallback
    );
  }

  ThemeState copyWith({
    ThemeMode? themeMode,
    ThemeData? themeData,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      themeData: themeData ?? this.themeData,
    );
  }

  @override
  List<Object?> get props => [themeMode, themeData];
}

/// Theme management cubit
class ThemeCubit extends Cubit<ThemeState> {
  final StorageService? _storageService;

  ThemeCubit({StorageService? storageService})
      : _storageService = storageService,
        super(ThemeState.light()) {
    _loadTheme();
  }

  static const String _themeKey = 'theme_mode';

  /// Load saved theme preference
  Future<void> _loadTheme() async {
    try {
      String? themeMode;
      if (_storageService != null) {
        themeMode = _storageService.getString(_themeKey);
      }

      switch (themeMode) {
        case 'dark':
          emit(ThemeState.dark());
          break;
        case 'light':
          emit(ThemeState.light());
          break;
        case 'system':
          emit(ThemeState.system());
          break;
        default:
          emit(ThemeState.light());
      }
    } catch (e) {
      // If loading fails, use light theme as default
      emit(ThemeState.light());
    }
  }

  /// Toggle between light and dark theme
  Future<void> toggleTheme() async {
    try {
      final isDark = state.themeMode == ThemeMode.dark;
      final newState = isDark ? ThemeState.light() : ThemeState.dark();

      // Save preference
      if (_storageService != null) {
        await _storageService.setString(_themeKey, isDark ? 'light' : 'dark');
      }

      emit(newState);
    } catch (e) {
      // Handle error silently, keep current theme
    }
  }

  /// Set light theme
  Future<void> setLightTheme() async {
    try {
      if (_storageService != null) {
        await _storageService.setString(_themeKey, 'light');
      }
      emit(ThemeState.light());
    } catch (e) {
      // Handle error silently
    }
  }

  /// Set dark theme
  Future<void> setDarkTheme() async {
    try {
      if (_storageService != null) {
        await _storageService.setString(_themeKey, 'dark');
      }
      emit(ThemeState.dark());
    } catch (e) {
      // Handle error silently
    }
  }

  /// Set system theme
  Future<void> setSystemTheme() async {
    try {
      if (_storageService != null) {
        await _storageService.setString(_themeKey, 'system');
      }
      emit(ThemeState.system());
    } catch (e) {
      // Handle error silently
    }
  }

  /// Check if current theme is dark
  bool get isDarkTheme => state.themeMode == ThemeMode.dark;

  /// Check if current theme is light
  bool get isLightTheme => state.themeMode == ThemeMode.light;

  /// Check if current theme is system
  bool get isSystemTheme => state.themeMode == ThemeMode.system;
}
