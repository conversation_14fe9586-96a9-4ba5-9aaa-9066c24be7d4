/// Application-wide constants and configuration
class AppConstants {
  // App information
  static const String appName = 'Quester';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Adventure Platform for Quest Management';
  static const String appPackageName = 'com.quester.app';
  static const String appBundleId = 'com.quester.app';
  
  // Company information
  static const String companyName = 'Quester Inc.';
  static const String companyWebsite = 'https://quester.app';
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = 'https://quester.app/privacy';
  static const String termsOfServiceUrl = 'https://quester.app/terms';
  
  // Pagination defaults
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int minPageSize = 5;
  
  // Validation limits
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  
  // Quest limits
  static const int minQuestTitleLength = 5;
  static const int maxQuestTitleLength = 100;
  static const int minQuestDescriptionLength = 10;
  static const int maxQuestDescriptionLength = 1000;
  static const int minXpReward = 1;
  static const int maxXpReward = 10000;
  static const int minCoinReward = 0;
  static const int maxCoinReward = 100000;
  
  // XP and levels
  static const int baseXpPerLevel = 1000;
  static const double xpMultiplier = 1.5;
  static const int maxLevel = 100;
  static const int startingXp = 0;
  static const int startingLevel = 1;
  
  // Notification settings
  static const int maxNotificationsPerUser = 1000;
  static const Duration notificationRetentionPeriod = Duration(days: 30);
  static const Duration notificationExpiryPeriod = Duration(days: 7);
  
  // File upload limits
  static const int maxAvatarSizeMB = 5;
  static const int maxQuestImageSizeMB = 10;
  static const int maxFileSizeMB = 50;
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  static const List<String> allowedDocumentExtensions = ['pdf', 'doc', 'docx', 'txt'];
  
  // Session and security
  static const Duration sessionTimeout = Duration(hours: 24);
  static const Duration refreshTokenExpiry = Duration(days: 30);
  static const Duration passwordResetTokenExpiry = Duration(hours: 1);
  static const Duration emailVerificationTokenExpiry = Duration(hours: 24);
  static const int maxLoginAttempts = 5;
  static const Duration loginLockoutDuration = Duration(minutes: 15);
  
  // Rate limiting
  static const int maxRequestsPerMinute = 60;
  static const int maxRequestsPerHour = 1000;
  static const int maxRequestsPerDay = 10000;
  
  // Cache settings
  static const Duration defaultCacheDuration = Duration(minutes: 15);
  static const Duration userCacheDuration = Duration(minutes: 30);
  static const Duration questCacheDuration = Duration(minutes: 10);
  static const Duration dashboardCacheDuration = Duration(minutes: 5);
  
  // WebSocket settings
  static const Duration websocketPingInterval = Duration(seconds: 30);
  static const Duration websocketReconnectDelay = Duration(seconds: 5);
  static const int maxWebsocketReconnectAttempts = 10;
  static const Duration websocketConnectionTimeout = Duration(seconds: 10);
  
  // UI/UX constants
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  static const Duration debounceDelay = Duration(milliseconds: 500);
  static const Duration toastDuration = Duration(seconds: 3);
  static const Duration snackbarDuration = Duration(seconds: 4);
  
  // Responsive breakpoints - Updated to match client expectations
  static const double mobileBreakpoint = 450.0;
  static const double tabletBreakpoint = 800.0;
  static const double desktopBreakpoint = 1200.0;
  static const double largeDesktopBreakpoint = 1920.0;
  
  // Grid and layout
  static const int mobileGridColumns = 1;
  static const int tabletGridColumns = 2;
  static const int desktopGridColumns = 3;
  static const int wideDesktopGridColumns = 4;
  
  // Spacing and sizing
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;
  
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 12.0;
  static const double extraLargeRadius = 16.0;
  
  static const double smallElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double largeElevation = 8.0;
  static const double extraLargeElevation = 16.0;
  
  // Icon sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // Avatar sizes
  static const double smallAvatarSize = 32.0;
  static const double mediumAvatarSize = 48.0;
  static const double largeAvatarSize = 64.0;
  static const double extraLargeAvatarSize = 96.0;
  
  // Button sizes
  static const double smallButtonHeight = 32.0;
  static const double mediumButtonHeight = 40.0;
  static const double largeButtonHeight = 48.0;
  static const double extraLargeButtonHeight = 56.0;
  
  // Text field sizes
  static const double smallTextFieldHeight = 40.0;
  static const double mediumTextFieldHeight = 48.0;
  static const double largeTextFieldHeight = 56.0;
  
  // App bar heights
  static const double mobileAppBarHeight = 56.0;
  static const double desktopAppBarHeight = 64.0;
  
  // Navigation rail widths
  static const double collapsedNavigationRailWidth = 72.0;
  static const double expandedNavigationRailWidth = 256.0;
  
  // Bottom navigation heights
  static const double bottomNavigationHeight = 80.0;
  
  // Drawer widths
  static const double navigationDrawerWidth = 304.0;
  static const double endDrawerWidth = 320.0;
  
  // Card dimensions
  static const double minCardWidth = 280.0;
  static const double maxCardWidth = 400.0;
  static const double cardAspectRatio = 16 / 9;
  
  // List item heights
  static const double listItemHeight = 56.0;
  static const double denseListItemHeight = 48.0;
  static const double compactListItemHeight = 40.0;
  
  // Search and filtering
  static const int minSearchLength = 2;
  static const int maxSearchLength = 100;
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);
  static const int maxSearchResults = 50;
  
  // Notification badge
  static const int maxNotificationBadgeCount = 99;
  static const String notificationBadgeOverflow = '99+';
  
  // Date and time formats
  static const String dateFormat = 'MMM dd, yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'MMM dd, yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-ddTHH:mm:ss.SSSZ';
  
  // Locale and internationalization
  static const String defaultLocale = 'en_US';
  static const List<String> supportedLocales = ['en_US', 'es_ES', 'fr_FR', 'de_DE'];
  
  // Theme settings
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';
  
  // Storage keys - Expanded from client
  static const String themeKey = 'theme_mode';
  static const String localeKey = 'locale';
  static const String languageKey = 'language';
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  static const String cacheKey = 'cache_data';
  static const String notificationSettingsKey = 'notification_settings';
  
  // Feature flags
  static const String featureDarkMode = 'dark_mode';
  static const String featureNotifications = 'notifications';
  static const String featureAnalytics = 'analytics';
  static const String featureBiometrics = 'biometrics';
  static const String featureOfflineMode = 'offline_mode';
  
  // Environment detection
  static bool get isDebug {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }
  
  static bool get isRelease => !isDebug;
  
  // Platform detection helpers
  static const String platformAndroid = 'android';
  static const String platformIOS = 'ios';
  static const String platformWeb = 'web';
  static const String platformWindows = 'windows';
  static const String platformMacOS = 'macos';
  static const String platformLinux = 'linux';

  
  // Error messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your connection.';
  static const String timeoutErrorMessage = 'Request timed out. Please try again.';
  static const String unauthorizedErrorMessage = 'You are not authorized to perform this action.';
  static const String notFoundErrorMessage = 'The requested resource was not found.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  
  // Success messages
  static const String genericSuccessMessage = 'Operation completed successfully.';
  static const String saveSuccessMessage = 'Changes saved successfully.';
  static const String deleteSuccessMessage = 'Item deleted successfully.';
  static const String updateSuccessMessage = 'Item updated successfully.';
  static const String createSuccessMessage = 'Item created successfully.';

  // App Bar specific constants
  static const double appBarElevation = 4.0;
  static const String appBarTitle = 'Quester';
  static const String appBarLogoPath = 'assets/images/quester_logo.png';

  // Navigation constants
  static const int maxNavigationItems = 5;
  static const double navigationIconSize = 24.0;
  static const double navigationLabelFontSize = 12.0;

  // Sidebar constants
  static const double sidebarWidth = 320.0;
  static const double sidebarMinWidth = 280.0;
  static const double sidebarMaxWidth = 400.0;
  static const Duration sidebarAnimationDuration = Duration(milliseconds: 250);

  // Notification constants
  static const int maxNotificationDisplayCount = 5;
  static const Duration notificationAutoHideDuration = Duration(seconds: 5);
  static const double notificationDropdownWidth = 360.0;
  static const double notificationDropdownMaxHeight = 400.0;

  // Component spacing
  static const double componentPadding = 16.0;
  static const double componentMargin = 8.0;
  static const double sectionSpacing = 24.0;
  static const double pageHorizontalPadding = 16.0;
  static const double pageVerticalPadding = 24.0;

  // Color constants
  static const String primaryColorHex = '#2563EB';
  static const String secondaryColorHex = '#64748B';
  static const String successColorHex = '#059669';
  static const String warningColorHex = '#D97706';
  static const String errorColorHex = '#DC2626';
  static const String greyAppBarColorHex = '#F5F5F5';

  // Achievement constants
  static const int maxAchievementsPerCategory = 50;
  static const int maxAchievementRequirements = 10;
  static const Duration achievementAnimationDuration = Duration(milliseconds: 800);

  // Leaderboard constants
  static const int defaultLeaderboardSize = 10;
  static const int maxLeaderboardSize = 100;
  static const Duration leaderboardUpdateInterval = Duration(minutes: 5);

  // Real-time update intervals
  static const Duration dashboardUpdateInterval = Duration(seconds: 30);
  static const Duration notificationCheckInterval = Duration(seconds: 15);
  static const Duration userStatusUpdateInterval = Duration(minutes: 1);

  // Additional client constants
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100;

  // Feature flags - Shared between client and server
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableWebSocket = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = false;

  // Development constants
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = false;

  // Social features
  static const int maxFriendsCount = 1000;
  static const int maxGroupSize = 50;
  static const Duration friendRequestExpiration = Duration(days: 30);

  // Additional quest constants
  static const int maxActiveQuests = 10;
  static const Duration questTimeout = Duration(days: 30);
}
